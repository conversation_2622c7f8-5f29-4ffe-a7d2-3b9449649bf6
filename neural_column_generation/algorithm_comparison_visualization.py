#!/usr/bin/env python3
"""
算法对比可视化系统
创建全面的可视化图表来展示神经列生成与基线算法的对比结果
"""

import os
import sys
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import pandas as pd
from collections import defaultdict
import json
from datetime import datetime

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False
sns.set_style("whitegrid")
sns.set_palette("husl")

# 添加路径
sys.path.append('..')
sys.path.append('.')

from improved_sd_batch_test import ImprovedSDTester


class AlgorithmComparisonVisualizer:
    """算法对比可视化器"""

    def __init__(self):
        self.results = []
        self.figures = []

    def load_test_results(self):
        """加载测试结果"""
        print("📊 Loading test results for visualization...")

        # 运行测试获取结果
        tester = ImprovedSDTester()
        self.results = tester.run_improved_batch_test(max_instances_per_size=6)

        if not self.results:
            print("❌ No results to visualize")
            return False

        print(f"✅ Loaded {len(self.results)} test results")
        return True

    def create_performance_comparison_chart(self):
        """创建性能对比图表"""
        print("📈 Creating performance comparison chart...")

        # 准备数据
        neural_makespans = []
        spt_makespans = []
        fifo_makespans = []
        lpt_makespans = []
        instance_labels = []
        improvements = []

        for i, result in enumerate(self.results):
            if result['neural']['success']:
                neural_makespans.append(result['neural']['best_makespan'])
                instance_labels.append(f"{result['size_label']}-{i+1}")

                # 基线算法结果
                baselines = result.get('baselines', {})
                spt_makespans.append(baselines.get('SPT', {}).get('makespan', np.nan))
                fifo_makespans.append(baselines.get('FIFO', {}).get('makespan', np.nan))
                lpt_makespans.append(baselines.get('LPT', {}).get('makespan', np.nan))

                improvements.append(result.get('improvement', 0))

        # 创建对比图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(16, 12))

        # 子图1: Makespan对比
        x = np.arange(len(instance_labels))
        width = 0.2

        ax1.bar(x - 1.5*width, neural_makespans, width, label='Neural Network', color='#FF6B6B', alpha=0.8)
        ax1.bar(x - 0.5*width, spt_makespans, width, label='SPT', color='#4ECDC4', alpha=0.8)
        ax1.bar(x + 0.5*width, fifo_makespans, width, label='FIFO', color='#45B7D1', alpha=0.8)
        ax1.bar(x + 1.5*width, lpt_makespans, width, label='LPT', color='#96CEB4', alpha=0.8)

        ax1.set_xlabel('Test Instances', fontsize=12)
        ax1.set_ylabel('Makespan', fontsize=12)
        ax1.set_title('Algorithm Performance Comparison - Makespan', fontsize=14, fontweight='bold')
        ax1.set_xticks(x)
        ax1.set_xticklabels(instance_labels, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 子图2: 改进百分比
        colors = ['#FF6B6B' if imp >= 0 else '#FFA07A' for imp in improvements]
        bars = ax2.bar(x, improvements, color=colors, alpha=0.7)

        # 添加零线
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        ax2.set_xlabel('Test Instances', fontsize=12)
        ax2.set_ylabel('Improvement (%)', fontsize=12)
        ax2.set_title('Neural Network Improvement over Best Baseline', fontsize=14, fontweight='bold')
        ax2.set_xticks(x)
        ax2.set_xticklabels(instance_labels, rotation=45, ha='right')
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for i, (bar, imp) in enumerate(zip(bars, improvements)):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + (1 if height >= 0 else -3),
                    f'{imp:.1f}%', ha='center', va='bottom' if height >= 0 else 'top', fontsize=8)

        plt.tight_layout()
        self.figures.append(('performance_comparison', fig))
        return fig

    def create_algorithm_reliability_chart(self):
        """创建算法可靠性图表"""
        print("🛡️ Creating algorithm reliability chart...")

        # 统计各算法的成功率
        algorithms = ['Neural Network', 'SPT', 'FIFO', 'LPT']
        success_rates = []

        total_instances = len(self.results)

        # Neural Network
        neural_successes = sum(1 for r in self.results if r['neural']['success'])
        success_rates.append(neural_successes / total_instances * 100)

        # 基线算法
        for alg in ['SPT', 'FIFO', 'LPT']:
            successes = sum(1 for r in self.results
                          if alg in r.get('baselines', {}) and
                          r['baselines'][alg]['feasible'])
            success_rates.append(successes / total_instances * 100)

        # 创建可靠性图表
        fig, ax = plt.subplots(1, 1, figsize=(10, 6))

        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        bars = ax.bar(algorithms, success_rates, color=colors, alpha=0.8)

        ax.set_ylabel('Success Rate (%)', fontsize=12)
        ax.set_title('Algorithm Reliability Comparison', fontsize=14, fontweight='bold')
        ax.set_ylim(0, 105)
        ax.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, rate in zip(bars, success_rates):
            height = bar.get_height()
            ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{rate:.1f}%', ha='center', va='bottom', fontsize=11, fontweight='bold')

        plt.tight_layout()
        self.figures.append(('algorithm_reliability', fig))
        return fig

    def create_performance_by_scale_chart(self):
        """创建按规模分组的性能图表"""
        print("📊 Creating performance by scale chart...")

        # 按规模分组
        scale_groups = defaultdict(list)
        for result in self.results:
            if result['neural']['success']:
                scale_groups[result['size_label']].append(result)

        # 准备数据
        scales = list(scale_groups.keys())
        neural_avg = []
        spt_avg = []
        fifo_avg = []
        lpt_avg = []
        improvements_avg = []

        for scale in scales:
            results = scale_groups[scale]

            # 神经网络平均
            neural_makespans = [r['neural']['best_makespan'] for r in results]
            neural_avg.append(np.mean(neural_makespans))

            # 基线算法平均
            spt_makespans = [r['baselines']['SPT']['makespan'] for r in results
                           if 'SPT' in r.get('baselines', {}) and r['baselines']['SPT']['feasible']]
            spt_avg.append(np.mean(spt_makespans) if spt_makespans else np.nan)

            fifo_makespans = [r['baselines']['FIFO']['makespan'] for r in results
                            if 'FIFO' in r.get('baselines', {}) and r['baselines']['FIFO']['feasible']]
            fifo_avg.append(np.mean(fifo_makespans) if fifo_makespans else np.nan)

            lpt_makespans = [r['baselines']['LPT']['makespan'] for r in results
                           if 'LPT' in r.get('baselines', {}) and r['baselines']['LPT']['feasible']]
            lpt_avg.append(np.mean(lpt_makespans) if lpt_makespans else np.nan)

            # 改进平均
            improvements = [r['improvement'] for r in results if r['improvement'] is not None]
            improvements_avg.append(np.mean(improvements) if improvements else 0)

        # 创建图表
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))

        # 子图1: 平均Makespan对比
        x = np.arange(len(scales))
        width = 0.2

        ax1.bar(x - 1.5*width, neural_avg, width, label='Neural Network', color='#FF6B6B', alpha=0.8)
        ax1.bar(x - 0.5*width, spt_avg, width, label='SPT', color='#4ECDC4', alpha=0.8)
        ax1.bar(x + 0.5*width, fifo_avg, width, label='FIFO', color='#45B7D1', alpha=0.8)
        ax1.bar(x + 1.5*width, lpt_avg, width, label='LPT', color='#96CEB4', alpha=0.8)

        ax1.set_xlabel('Instance Scale', fontsize=12)
        ax1.set_ylabel('Average Makespan', fontsize=12)
        ax1.set_title('Average Performance by Instance Scale', fontsize=14, fontweight='bold')
        ax1.set_xticks(x)
        ax1.set_xticklabels(scales)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 子图2: 平均改进
        colors = ['#FF6B6B' if imp >= 0 else '#FFA07A' for imp in improvements_avg]
        bars = ax2.bar(scales, improvements_avg, color=colors, alpha=0.7)

        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.set_xlabel('Instance Scale', fontsize=12)
        ax2.set_ylabel('Average Improvement (%)', fontsize=12)
        ax2.set_title('Average Improvement by Instance Scale', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for bar, imp in zip(bars, improvements_avg):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + (0.5 if height >= 0 else -0.5),
                    f'{imp:.1f}%', ha='center', va='bottom' if height >= 0 else 'top', fontsize=10)

        plt.tight_layout()
        self.figures.append(('performance_by_scale', fig))
        return fig

    def create_data_source_comparison_chart(self):
        """创建数据源对比图表"""
        print("🔍 Creating data source comparison chart...")

        # 按数据源分组
        source_groups = defaultdict(list)
        for result in self.results:
            if result['neural']['success']:
                source_groups[result['data_source']].append(result)

        # 准备数据
        sources = list(source_groups.keys())
        metrics = {
            'avg_makespan': [],
            'avg_improvement': [],
            'success_rate': [],
            'positive_improvement_rate': []
        }

        for source in sources:
            results = source_groups[source]

            # 平均makespan
            makespans = [r['neural']['best_makespan'] for r in results]
            metrics['avg_makespan'].append(np.mean(makespans))

            # 平均改进
            improvements = [r['improvement'] for r in results if r['improvement'] is not None]
            metrics['avg_improvement'].append(np.mean(improvements) if improvements else 0)

            # 成功率
            metrics['success_rate'].append(100.0)  # 神经网络100%成功

            # 正改进率
            positive_improvements = [imp for imp in improvements if imp > 0]
            metrics['positive_improvement_rate'].append(
                len(positive_improvements) / len(improvements) * 100 if improvements else 0
            )

        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        colors = ['#FF6B6B', '#4ECDC4']

        # 子图1: 平均Makespan
        bars1 = ax1.bar(sources, metrics['avg_makespan'], color=colors, alpha=0.8)
        ax1.set_ylabel('Average Makespan', fontsize=12)
        ax1.set_title('Average Makespan by Data Source', fontsize=14, fontweight='bold')
        ax1.grid(True, alpha=0.3)

        for bar, val in zip(bars1, metrics['avg_makespan']):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                    f'{val:.1f}', ha='center', va='bottom', fontsize=11)

        # 子图2: 平均改进
        improvement_colors = ['#FF6B6B' if imp >= 0 else '#FFA07A' for imp in metrics['avg_improvement']]
        bars2 = ax2.bar(sources, metrics['avg_improvement'], color=improvement_colors, alpha=0.8)
        ax2.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax2.set_ylabel('Average Improvement (%)', fontsize=12)
        ax2.set_title('Average Improvement by Data Source', fontsize=14, fontweight='bold')
        ax2.grid(True, alpha=0.3)

        for bar, val in zip(bars2, metrics['avg_improvement']):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + (0.2 if height >= 0 else -0.2),
                    f'{val:.1f}%', ha='center', va='bottom' if height >= 0 else 'top', fontsize=11)

        # 子图3: 成功率
        bars3 = ax3.bar(sources, metrics['success_rate'], color=colors, alpha=0.8)
        ax3.set_ylabel('Success Rate (%)', fontsize=12)
        ax3.set_title('Success Rate by Data Source', fontsize=14, fontweight='bold')
        ax3.set_ylim(0, 105)
        ax3.grid(True, alpha=0.3)

        for bar, val in zip(bars3, metrics['success_rate']):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{val:.1f}%', ha='center', va='bottom', fontsize=11)

        # 子图4: 正改进率
        bars4 = ax4.bar(sources, metrics['positive_improvement_rate'], color=colors, alpha=0.8)
        ax4.set_ylabel('Positive Improvement Rate (%)', fontsize=12)
        ax4.set_title('Positive Improvement Rate by Data Source', fontsize=14, fontweight='bold')
        ax4.grid(True, alpha=0.3)

        for bar, val in zip(bars4, metrics['positive_improvement_rate']):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 1,
                    f'{val:.1f}%', ha='center', va='bottom', fontsize=11)

        plt.tight_layout()
        self.figures.append(('data_source_comparison', fig))
        return fig

    def create_distribution_analysis_chart(self):
        """创建分布分析图表"""
        print("📊 Creating distribution analysis chart...")

        # 准备数据
        neural_makespans = []
        spt_makespans = []
        improvements = []
        generation_times = []

        for result in self.results:
            if result['neural']['success']:
                neural_makespans.append(result['neural']['best_makespan'])
                generation_times.append(result['neural']['generation_time'])

                if 'SPT' in result.get('baselines', {}) and result['baselines']['SPT']['feasible']:
                    spt_makespans.append(result['baselines']['SPT']['makespan'])

                if result['improvement'] is not None:
                    improvements.append(result['improvement'])

        # 创建图表
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 子图1: Makespan分布对比
        ax1.hist(neural_makespans, bins=20, alpha=0.7, label='Neural Network', color='#FF6B6B')
        ax1.hist(spt_makespans, bins=20, alpha=0.7, label='SPT', color='#4ECDC4')
        ax1.set_xlabel('Makespan', fontsize=12)
        ax1.set_ylabel('Frequency', fontsize=12)
        ax1.set_title('Makespan Distribution Comparison', fontsize=14, fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 子图2: 改进分布
        ax2.hist(improvements, bins=20, alpha=0.7, color='#45B7D1', edgecolor='black')
        ax2.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='No Improvement')
        ax2.axvline(x=np.mean(improvements), color='orange', linestyle='-', alpha=0.7,
                   label=f'Mean: {np.mean(improvements):.1f}%')
        ax2.set_xlabel('Improvement (%)', fontsize=12)
        ax2.set_ylabel('Frequency', fontsize=12)
        ax2.set_title('Improvement Distribution', fontsize=14, fontweight='bold')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 子图3: 生成时间分布
        ax3.hist(generation_times, bins=20, alpha=0.7, color='#96CEB4', edgecolor='black')
        ax3.axvline(x=np.mean(generation_times), color='red', linestyle='-', alpha=0.7,
                   label=f'Mean: {np.mean(generation_times):.2f}s')
        ax3.set_xlabel('Generation Time (seconds)', fontsize=12)
        ax3.set_ylabel('Frequency', fontsize=12)
        ax3.set_title('Generation Time Distribution', fontsize=14, fontweight='bold')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 子图4: 散点图 - Makespan vs 改进
        valid_pairs = [(neural_makespans[i], improvements[i])
                      for i in range(min(len(neural_makespans), len(improvements)))]

        if valid_pairs:
            x_vals, y_vals = zip(*valid_pairs)
            ax4.scatter(x_vals, y_vals, alpha=0.6, color='#FF6B6B', s=50)
            ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)

            # 添加趋势线
            z = np.polyfit(x_vals, y_vals, 1)
            p = np.poly1d(z)
            ax4.plot(sorted(x_vals), p(sorted(x_vals)), "r--", alpha=0.8, label='Trend')

            ax4.set_xlabel('Neural Network Makespan', fontsize=12)
            ax4.set_ylabel('Improvement (%)', fontsize=12)
            ax4.set_title('Makespan vs Improvement Correlation', fontsize=14, fontweight='bold')
            ax4.legend()
            ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        self.figures.append(('distribution_analysis', fig))
        return fig

    def create_detailed_instance_analysis_chart(self):
        """创建详细实例分析图表"""
        print("🔍 Creating detailed instance analysis chart...")

        # 选择几个代表性实例进行详细分析
        representative_instances = []

        # 选择最佳改进实例
        best_improvement = max(self.results, key=lambda x: x.get('improvement', -float('inf')))
        representative_instances.append(('Best Improvement', best_improvement))

        # 选择最差表现实例
        worst_improvement = min(self.results, key=lambda x: x.get('improvement', float('inf')))
        representative_instances.append(('Worst Performance', worst_improvement))

        # 选择中等表现实例
        improvements = [r.get('improvement', 0) for r in self.results if r.get('improvement') is not None]
        median_improvement = np.median(improvements)
        median_instance = min(self.results,
                            key=lambda x: abs(x.get('improvement', 0) - median_improvement))
        representative_instances.append(('Median Performance', median_instance))

        # 创建详细分析图表
        fig, axes = plt.subplots(1, 3, figsize=(18, 6))

        for i, (title, instance) in enumerate(representative_instances):
            ax = axes[i]

            if instance['neural']['success']:
                # 准备数据
                algorithms = []
                makespans = []
                colors = []

                # 神经网络
                algorithms.append('Neural\nNetwork')
                makespans.append(instance['neural']['best_makespan'])
                colors.append('#FF6B6B')

                # 基线算法
                for alg, color in [('SPT', '#4ECDC4'), ('FIFO', '#45B7D1'), ('LPT', '#96CEB4')]:
                    if alg in instance.get('baselines', {}) and instance['baselines'][alg]['feasible']:
                        algorithms.append(alg)
                        makespans.append(instance['baselines'][alg]['makespan'])
                        colors.append(color)

                # 创建柱状图
                bars = ax.bar(algorithms, makespans, color=colors, alpha=0.8)

                # 添加数值标签
                for bar, makespan in zip(bars, makespans):
                    height = bar.get_height()
                    ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                           f'{makespan:.1f}', ha='center', va='bottom', fontsize=10, fontweight='bold')

                # 设置标题和标签
                improvement = instance.get('improvement', 0)
                ax.set_title(f'{title}\n{instance["size_label"]} - Improvement: {improvement:+.1f}%',
                           fontsize=12, fontweight='bold')
                ax.set_ylabel('Makespan', fontsize=11)
                ax.grid(True, alpha=0.3)

                # 高亮最佳结果
                best_idx = makespans.index(min(makespans))
                bars[best_idx].set_edgecolor('gold')
                bars[best_idx].set_linewidth(3)

        plt.tight_layout()
        self.figures.append(('detailed_instance_analysis', fig))
        return fig

    def create_summary_dashboard(self):
        """创建汇总仪表板"""
        print("📊 Creating summary dashboard...")

        # 计算关键指标
        total_instances = len(self.results)
        neural_successes = [r for r in self.results if r['neural']['success']]
        success_rate = len(neural_successes) / total_instances * 100

        improvements = [r['improvement'] for r in self.results if r['improvement'] is not None]
        avg_improvement = np.mean(improvements) if improvements else 0
        positive_improvements = [imp for imp in improvements if imp > 0]
        positive_rate = len(positive_improvements) / len(improvements) * 100 if improvements else 0

        makespans = [r['neural']['best_makespan'] for r in neural_successes]
        avg_makespan = np.mean(makespans) if makespans else 0

        generation_times = [r['neural']['generation_time'] for r in neural_successes]
        avg_time = np.mean(generation_times) if generation_times else 0

        # 创建仪表板
        fig = plt.figure(figsize=(20, 12))
        gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)

        # 关键指标卡片
        metrics = [
            ('Success Rate', f'{success_rate:.1f}%', '#4CAF50'),
            ('Avg Improvement', f'{avg_improvement:+.1f}%', '#FF9800' if avg_improvement >= 0 else '#F44336'),
            ('Positive Rate', f'{positive_rate:.1f}%', '#2196F3'),
            ('Avg Generation Time', f'{avg_time:.2f}s', '#9C27B0')
        ]

        for i, (metric, value, color) in enumerate(metrics):
            ax = fig.add_subplot(gs[0, i])
            ax.text(0.5, 0.5, value, ha='center', va='center', fontsize=24, fontweight='bold', color=color)
            ax.text(0.5, 0.2, metric, ha='center', va='center', fontsize=12)
            ax.set_xlim(0, 1)
            ax.set_ylim(0, 1)
            ax.axis('off')

            # 添加边框
            for spine in ax.spines.values():
                spine.set_visible(True)
                spine.set_color(color)
                spine.set_linewidth(2)

        # 性能分布图
        ax1 = fig.add_subplot(gs[1, :2])
        ax1.hist(improvements, bins=15, alpha=0.7, color='#2196F3', edgecolor='black')
        ax1.axvline(x=0, color='red', linestyle='--', alpha=0.7, label='No Improvement')
        ax1.axvline(x=avg_improvement, color='orange', linestyle='-', alpha=0.7,
                   label=f'Mean: {avg_improvement:.1f}%')
        ax1.set_xlabel('Improvement (%)')
        ax1.set_ylabel('Frequency')
        ax1.set_title('Improvement Distribution', fontweight='bold')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 算法可靠性对比
        ax2 = fig.add_subplot(gs[1, 2:])
        algorithms = ['Neural Network', 'SPT', 'FIFO', 'LPT']
        reliability = [success_rate]

        for alg in ['SPT', 'FIFO', 'LPT']:
            successes = sum(1 for r in self.results
                          if alg in r.get('baselines', {}) and r['baselines'][alg]['feasible'])
            reliability.append(successes / total_instances * 100)

        colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
        bars = ax2.bar(algorithms, reliability, color=colors, alpha=0.8)
        ax2.set_ylabel('Success Rate (%)')
        ax2.set_title('Algorithm Reliability Comparison', fontweight='bold')
        ax2.set_ylim(0, 105)
        ax2.grid(True, alpha=0.3)

        for bar, rate in zip(bars, reliability):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                   f'{rate:.1f}%', ha='center', va='bottom', fontweight='bold')

        # 按规模的性能表现
        ax3 = fig.add_subplot(gs[2, :])

        scale_groups = defaultdict(list)
        for result in neural_successes:
            scale_groups[result['size_label']].append(result)

        scales = list(scale_groups.keys())
        scale_improvements = []
        scale_counts = []

        for scale in scales:
            results = scale_groups[scale]
            improvements_scale = [r['improvement'] for r in results if r['improvement'] is not None]
            scale_improvements.append(np.mean(improvements_scale) if improvements_scale else 0)
            scale_counts.append(len(results))

        # 创建组合图
        ax3_twin = ax3.twinx()

        # 柱状图：平均改进
        colors_scale = ['#FF6B6B' if imp >= 0 else '#FFA07A' for imp in scale_improvements]
        bars = ax3.bar(scales, scale_improvements, color=colors_scale, alpha=0.7, label='Avg Improvement')

        # 折线图：实例数量
        line = ax3_twin.plot(scales, scale_counts, color='#4CAF50', marker='o', linewidth=2,
                           markersize=8, label='Instance Count')

        ax3.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax3.set_xlabel('Instance Scale')
        ax3.set_ylabel('Average Improvement (%)', color='#FF6B6B')
        ax3_twin.set_ylabel('Instance Count', color='#4CAF50')
        ax3.set_title('Performance by Instance Scale', fontweight='bold')
        ax3.grid(True, alpha=0.3)

        # 添加图例
        lines1, labels1 = ax3.get_legend_handles_labels()
        lines2, labels2 = ax3_twin.get_legend_handles_labels()
        ax3.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

        # 添加数值标签
        for bar, imp, count in zip(bars, scale_improvements, scale_counts):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + (0.5 if height >= 0 else -0.5),
                    f'{imp:.1f}%', ha='center', va='bottom' if height >= 0 else 'top', fontsize=9)

        # 添加标题
        fig.suptitle('Neural Column Generation - Comprehensive Performance Dashboard',
                    fontsize=16, fontweight='bold', y=0.95)

        self.figures.append(('summary_dashboard', fig))
        return fig

    def save_all_figures(self, output_dir='neural_column_generation/visualizations'):
        """保存所有图表"""
        print(f"💾 Saving all figures to {output_dir}...")

        os.makedirs(output_dir, exist_ok=True)

        for name, fig in self.figures:
            # 保存为PNG
            png_path = os.path.join(output_dir, f'{name}.png')
            fig.savefig(png_path, dpi=300, bbox_inches='tight', facecolor='white')

            # 保存为PDF
            pdf_path = os.path.join(output_dir, f'{name}.pdf')
            fig.savefig(pdf_path, bbox_inches='tight', facecolor='white')

            print(f"  ✅ Saved {name}")

        print(f"✅ All figures saved to {output_dir}")

    def generate_all_visualizations(self):
        """生成所有可视化图表"""
        print("🎨 ALGORITHM COMPARISON VISUALIZATION")
        print("=" * 60)

        # 加载测试结果
        if not self.load_test_results():
            return

        # 生成各种图表
        print("\n📊 Generating visualization charts...")

        self.create_performance_comparison_chart()
        self.create_algorithm_reliability_chart()
        self.create_performance_by_scale_chart()
        self.create_data_source_comparison_chart()
        self.create_distribution_analysis_chart()
        self.create_detailed_instance_analysis_chart()
        self.create_summary_dashboard()

        # 保存所有图表
        self.save_all_figures()

        # 显示图表
        print(f"\n🎨 Displaying {len(self.figures)} visualization charts...")
        plt.show()

        print(f"\n✅ Visualization generation completed!")
        print(f"📊 Generated {len(self.figures)} comprehensive charts")
        print(f"💾 All charts saved to neural_column_generation/visualizations/")


def main():
    """主函数"""
    print("🎨 NEURAL COLUMN GENERATION - ALGORITHM COMPARISON VISUALIZATION")
    print("=" * 80)

    visualizer = AlgorithmComparisonVisualizer()
    visualizer.generate_all_visualizations()


if __name__ == "__main__":
    main()