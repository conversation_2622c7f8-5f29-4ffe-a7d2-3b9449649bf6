#!/usr/bin/env python3
"""
修复机器选择决策问题
重新设计机器选择逻辑，确保神经网络能够做出正确的决策
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass


@dataclass
class ImprovedScheduleOutput:
    """改进的调度输出"""
    schedule: List[Tuple[int, int]]
    makespan: float
    feasible: bool
    quality_score: float
    decision_quality: float = 0.0  # 决策质量评分


class ProcessingTimeAwareDecoder(nn.Module):
    """处理时间感知的解码器"""
    
    def __init__(self, d_model=256, n_heads=8):
        super().__init__()
        self.d_model = d_model
        
        # 操作选择器（保持原有逻辑）
        self.operation_selector = nn.Sequential(
            nn.Linear(d_model + 10, d_model),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, 1)
        )
        
        # 改进的机器选择器 - 直接使用处理时间
        self.machine_selector = nn.Sequential(
            nn.Linear(d_model + 3, d_model // 2),  # +3 for processing time features
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, d_model // 4),
            nn.ReLU(),
            nn.Linear(d_model // 4, 1)
        )
        
        # 处理时间编码器
        self.processing_time_encoder = nn.Sequential(
            nn.Linear(1, 16),
            nn.ReLU(),
            nn.Linear(16, 32),
            nn.ReLU(),
            nn.Linear(32, 16)
        )
    
    def forward(self, operation_embeddings, machine_embeddings, processing_matrix, job_lengths):
        """改进的前向传播"""
        batch_size = operation_embeddings.size(0)
        schedules = []
        
        for b in range(batch_size):
            schedule = self._generate_improved_schedule(
                operation_embeddings[b],
                machine_embeddings[b],
                processing_matrix[b],
                job_lengths
            )
            schedules.append(schedule)
        
        return schedules
    
    def _generate_improved_schedule(self, op_embeddings, machine_embeddings, processing_matrix, job_lengths):
        """生成改进的调度"""
        n_operations = op_embeddings.size(0)
        n_machines = machine_embeddings.size(0)
        
        schedule = []
        scheduled_ops = set()
        job_progress = [0] * len(job_lengths)
        decision_quality_score = 0.0
        
        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_lengths):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops
        
        for step in range(n_operations):
            # 获取可调度的操作
            available_operations = self._get_available_operations(
                job_operations, job_progress, scheduled_ops
            )
            
            if not available_operations:
                break
            
            # 选择操作（保持原有逻辑）
            selected_op = self._select_operation(available_operations, op_embeddings, processing_matrix)
            
            # 改进的机器选择
            selected_machine, decision_score = self._improved_machine_selection(
                selected_op, machine_embeddings, processing_matrix
            )
            
            if selected_machine is not None:
                schedule.append((selected_op, selected_machine))
                scheduled_ops.add(selected_op)
                decision_quality_score += decision_score
                
                # 更新作业进度
                for job_id, ops in enumerate(job_operations):
                    if selected_op in ops:
                        job_progress[job_id] += 1
                        break
        
        # 计算平均决策质量
        avg_decision_quality = decision_quality_score / len(schedule) if schedule else 0.0
        
        return ImprovedScheduleOutput(
            schedule=schedule,
            makespan=0.0,  # 将在后续计算
            feasible=len(scheduled_ops) == n_operations,
            quality_score=0.0,  # 将在后续计算
            decision_quality=avg_decision_quality
        )
    
    def _improved_machine_selection(self, operation_id, machine_embeddings, processing_matrix):
        """改进的机器选择逻辑"""
        # 找到可用的机器
        valid_machines = []
        processing_times = []
        
        for m in range(processing_matrix.size(1)):
            proc_time = processing_matrix[operation_id, m]
            if proc_time > 0:
                valid_machines.append(m)
                processing_times.append(proc_time.item())
        
        if not valid_machines:
            return None, 0.0
        
        if len(valid_machines) == 1:
            return valid_machines[0], 1.0
        
        # 计算处理时间特征
        min_time = min(processing_times)
        max_time = max(processing_times)
        time_range = max_time - min_time if max_time > min_time else 1.0
        
        machine_scores = []
        
        for i, machine_id in enumerate(valid_machines):
            proc_time = processing_times[i]
            
            # 处理时间特征
            normalized_time = proc_time / max(max_time, 1.0)  # 归一化处理时间
            relative_efficiency = (max_time - proc_time) / max(time_range, 1.0)  # 相对效率
            is_optimal = 1.0 if proc_time == min_time else 0.0  # 是否最优
            
            # 组合特征
            machine_embedding = machine_embeddings[machine_id]
            time_features = torch.tensor([normalized_time, relative_efficiency, is_optimal], 
                                       dtype=torch.float32, device=machine_embedding.device)
            
            # 结合机器嵌入和时间特征
            combined_input = torch.cat([machine_embedding, time_features], dim=0)
            
            # 计算评分
            score = self.machine_selector(combined_input.unsqueeze(0)).squeeze()
            
            # 添加处理时间偏置 - 强烈偏向最短时间
            time_bias = -10.0 * normalized_time  # 处理时间越短，偏置越大
            optimal_bonus = 5.0 if is_optimal else 0.0  # 最优机器额外奖励
            
            final_score = score.item() + time_bias + optimal_bonus
            
            machine_scores.append((final_score, machine_id, relative_efficiency))
        
        # 选择评分最高的机器
        machine_scores.sort(reverse=True, key=lambda x: x[0])
        selected_machine = machine_scores[0][1]
        decision_quality = machine_scores[0][2]  # 使用相对效率作为决策质量
        
        return selected_machine, decision_quality
    
    def _select_operation(self, available_operations, op_embeddings, processing_matrix):
        """选择操作（保持原有逻辑）"""
        if len(available_operations) == 1:
            return available_operations[0]
        
        # 简化的操作选择：选择最短处理时间的操作
        op_scores = []
        for op_id in available_operations:
            valid_times = [processing_matrix[op_id, m].item() 
                          for m in range(processing_matrix.size(1)) 
                          if processing_matrix[op_id, m] > 0]
            
            if valid_times:
                min_time = min(valid_times)
                op_scores.append((min_time, op_id))
        
        if op_scores:
            op_scores.sort()  # 按最短时间排序
            return op_scores[0][1]
        
        return available_operations[0]
    
    def _get_available_operations(self, job_operations, job_progress, scheduled_ops):
        """获取可调度的操作"""
        available_ops = []
        for job_id, operations in enumerate(job_operations):
            if job_progress[job_id] < len(operations):
                next_op = operations[job_progress[job_id]]
                if next_op not in scheduled_ops:
                    available_ops.append(next_op)
        return available_ops


class ImprovedNeuralModel(nn.Module):
    """改进的神经网络模型"""
    
    def __init__(self, d_model=256, n_heads=8, n_layers=4, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        
        # 特征编码器
        self.job_encoder = nn.Sequential(
            nn.Linear(10, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, d_model),
            nn.LayerNorm(d_model)
        )
        
        self.operation_encoder = nn.Sequential(
            nn.Linear(15, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, d_model),
            nn.LayerNorm(d_model)
        )
        
        self.machine_encoder = nn.Sequential(
            nn.Linear(8, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, d_model),
            nn.LayerNorm(d_model)
        )
        
        # Transformer层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 改进的解码器
        self.decoder = ProcessingTimeAwareDecoder(d_model, n_heads)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.LayerNorm):
                nn.init.ones_(module.weight)
                nn.init.zeros_(module.bias)
    
    def forward(self, job_features, operation_features, machine_features, 
                processing_matrix, job_lengths, num_columns=1):
        """前向传播"""
        # 编码特征
        job_embeddings = self.job_encoder(job_features)
        operation_embeddings = self.operation_encoder(operation_features)
        machine_embeddings = self.machine_encoder(machine_features)
        
        # Transformer增强
        operation_embeddings = self.transformer(operation_embeddings)
        machine_embeddings = self.transformer(machine_embeddings)
        
        # 生成改进的列
        all_columns = []
        
        for col_idx in range(num_columns):
            # 生成调度
            schedules = self.decoder(
                operation_embeddings,
                machine_embeddings,
                processing_matrix,
                job_lengths
            )
            
            # 评估并添加到结果
            for schedule_output in schedules:
                if schedule_output.feasible:
                    # 计算makespan和质量评分
                    makespan, feasible = self._evaluate_schedule(
                        schedule_output.schedule, processing_matrix[0].cpu().numpy(), job_lengths
                    )
                    
                    if feasible:
                        schedule_output.makespan = makespan
                        schedule_output.quality_score = 1.0 / (1.0 + makespan / 100.0)
                        all_columns.append(schedule_output)
        
        return all_columns
    
    def _evaluate_schedule(self, schedule, processing_matrix, job_lengths):
        """评估调度"""
        if not schedule:
            return float('inf'), False
        
        n_machines = processing_matrix.shape[1]
        machine_times = [0] * n_machines
        
        for op_id, machine_id in schedule:
            if (machine_id < n_machines and 
                op_id < processing_matrix.shape[0] and 
                processing_matrix[op_id, machine_id] > 0):
                machine_times[machine_id] += processing_matrix[op_id, machine_id]
        
        makespan = max(machine_times) if machine_times else float('inf')
        return makespan, makespan < float('inf')
    
    def generate_improved_columns(self, job_features, operation_features, machine_features,
                                 processing_matrix, job_lengths, num_columns=5):
        """生成改进的调度列"""
        self.eval()
        with torch.no_grad():
            columns = self.forward(
                job_features, operation_features, machine_features,
                processing_matrix, job_lengths, num_columns
            )
        
        # 按makespan排序
        valid_columns = [col for col in columns if col.feasible]
        valid_columns.sort(key=lambda x: x.makespan)
        
        return valid_columns


def test_improved_model():
    """测试改进的模型"""
    print("🧪 Testing Improved Machine Selection Model")
    print("=" * 60)
    
    # 创建简单测试实例
    job_lengths = [2, 2, 2]
    processing_times = np.array([
        [10, 20, 30],  # Op 0: M0最优
        [30, 10, 20],  # Op 1: M1最优
        [20, 30, 10],  # Op 2: M2最优
        [10, 20, 30],  # Op 3: M0最优
        [30, 10, 20],  # Op 4: M1最优
        [20, 30, 10],  # Op 5: M2最优
    ])
    
    print(f"📊 Test Instance:")
    print(f"  Jobs: {job_lengths}")
    print(f"  Processing times:")
    for i, row in enumerate(processing_times):
        optimal_machine = np.argmin(row)
        print(f"    Op {i}: {row} → optimal M{optimal_machine}({row[optimal_machine]})")
    
    # 创建模型
    model = ImprovedNeuralModel(d_model=128, n_heads=4, n_layers=2)
    
    # 创建特征
    batch_size = 1
    n_jobs = 3
    n_operations = 6
    n_machines = 3
    
    job_features = torch.randn(batch_size, n_jobs, 10)
    operation_features = torch.randn(batch_size, n_operations, 15)
    machine_features = torch.randn(batch_size, n_machines, 8)
    processing_matrix = torch.FloatTensor(processing_times).unsqueeze(0)
    
    print(f"\n🧠 Testing improved model...")
    
    # 生成调度
    columns = model.generate_improved_columns(
        job_features, operation_features, machine_features,
        processing_matrix, job_lengths, num_columns=3
    )
    
    print(f"✅ Generated {len(columns)} columns:")
    
    for i, column in enumerate(columns):
        print(f"\n  Column {i+1}:")
        print(f"    Schedule: {column.schedule}")
        print(f"    Makespan: {column.makespan}")
        print(f"    Decision Quality: {column.decision_quality:.3f}")
        
        # 分析决策质量
        correct_decisions = 0
        total_decisions = len(column.schedule)
        
        for op_id, machine_id in column.schedule:
            optimal_machine = np.argmin(processing_times[op_id])
            if machine_id == optimal_machine:
                correct_decisions += 1
            else:
                optimal_time = processing_times[op_id, optimal_machine]
                actual_time = processing_times[op_id, machine_id]
                waste = actual_time - optimal_time
                print(f"      Op {op_id}: chose M{machine_id}({actual_time}) vs optimal M{optimal_machine}({optimal_time}) → waste {waste}")
        
        decision_accuracy = correct_decisions / total_decisions * 100
        print(f"    Decision Accuracy: {decision_accuracy:.1f}% ({correct_decisions}/{total_decisions})")
    
    # 比较与理论最优
    theoretical_optimal = 20  # 每个机器负载20
    if columns:
        best_makespan = min(col.makespan for col in columns)
        gap = (best_makespan - theoretical_optimal) / theoretical_optimal * 100
        print(f"\n📈 Performance Analysis:")
        print(f"  Theoretical Optimal: {theoretical_optimal}")
        print(f"  Best Neural: {best_makespan}")
        print(f"  Gap: {gap:.1f}%")
        
        if gap < 10:
            print(f"  🎉 Excellent! Very close to optimal")
        elif gap < 50:
            print(f"  😊 Good improvement")
        else:
            print(f"  ⚠️  Still needs work")


def main():
    """主函数"""
    print("🔧 FIXED MACHINE SELECTION MODEL")
    print("=" * 50)
    
    test_improved_model()


if __name__ == "__main__":
    main()
