#!/usr/bin/env python3
"""
最终系统测试
验证改进后的神经列生成系统：DANIEL一致数据 + 有效调度模型
"""

import os
import sys
import torch
import numpy as np
import time
from typing import List, Tuple

# 添加路径
sys.path.append('..')
sys.path.append('.')

from valid_schedule_neural_model import ValidScheduleNeuralModel
from daniel_consistent_data_generator import DANIELConsistentDataGenerator, DANIELTrainingConfig


def load_final_model(model_path="neural_column_generation/models/final_best_model.pth"):
    """加载最终训练的模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = ValidScheduleNeuralModel(d_model=128, n_heads=4, n_layers=3).to(device)
    
    # 加载权重
    if os.path.exists(model_path):
        try:
            checkpoint = torch.load(model_path, map_location=device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()
            print(f"✅ Final trained model loaded from {model_path}")
        except Exception as e:
            print(f"⚠️  Failed to load model: {e}, using untrained model")
    else:
        print(f"⚠️  Model file not found: {model_path}, using untrained model")
    
    return model, device


def create_test_instances():
    """创建测试实例（使用DANIEL一致的方法）"""
    config = DANIELTrainingConfig(n_j=10, n_m=5, data_source='SD1')
    generator = DANIELConsistentDataGenerator(config)
    
    # 生成3个测试实例
    instances = generator.sample_training_instances_like_daniel(3)
    
    return instances


def extract_features_for_model(instance):
    """为模型提取特征"""
    job_length = instance['job_length']
    processing_times = np.array(instance['processing_times'])
    n_jobs = len(job_length)
    n_machines = processing_times.shape[1]
    n_operations = sum(job_length)
    
    # 作业特征
    job_features = []
    for job_id, n_ops in enumerate(job_length):
        op_start = sum(job_length[:job_id])
        op_end = op_start + n_ops
        
        total_workload = 0
        for op_idx in range(op_start, op_end):
            if op_idx < len(processing_times):
                min_time = min(t for t in processing_times[op_idx] if t > 0)
                total_workload += min_time
        
        job_feat = [
            job_id / n_jobs,
            n_ops / n_operations,
            total_workload / 1000.0,
            n_ops / max(job_length),
            total_workload / (n_ops * 20),
            0, 0, 0, 0, 0
        ]
        job_features.append(job_feat)
    
    # 操作特征
    operation_features = []
    for op_id in range(n_operations):
        if op_id < len(processing_times):
            proc_times = processing_times[op_id]
            
            job_id = 0
            op_in_job = op_id
            for j, n_ops in enumerate(job_length):
                if op_in_job < n_ops:
                    job_id = j
                    break
                op_in_job -= n_ops
            
            valid_times = [t for t in proc_times if t > 0]
            min_time = min(valid_times) if valid_times else 0
            max_time = max(valid_times) if valid_times else 0
            avg_time = np.mean(valid_times) if valid_times else 0
            n_machines_available = len(valid_times)
            
            op_feat = [
                op_id / n_operations,
                job_id / n_jobs,
                op_in_job / max(job_length),
                min_time / 50.0,
                max_time / 50.0,
                avg_time / 50.0,
                n_machines_available / n_machines,
                (max_time - min_time) / 50.0,
                0, 0, 0, 0, 0, 0, 0
            ]
            operation_features.append(op_feat)
    
    # 机器特征
    machine_features = []
    for machine_id in range(n_machines):
        total_load = 0
        n_operations_available = 0
        
        for op_id in range(n_operations):
            if op_id < len(processing_times) and machine_id < len(processing_times[op_id]):
                proc_time = processing_times[op_id][machine_id]
                if proc_time > 0:
                    total_load += proc_time
                    n_operations_available += 1
        
        avg_load = total_load / max(1, n_operations_available)
        
        machine_feat = [
            machine_id / n_machines,
            total_load / 1000.0,
            n_operations_available / n_operations,
            avg_load / 50.0,
            0, 0, 0, 0
        ]
        machine_features.append(machine_feat)
    
    return {
        'job_features': torch.FloatTensor(job_features),
        'operation_features': torch.FloatTensor(operation_features),
        'machine_features': torch.FloatTensor(machine_features),
        'processing_matrix': torch.FloatTensor(processing_times),
        'job_lengths': job_length
    }


def generate_baseline_solutions(instance):
    """生成基线解决方案"""
    job_length = instance['job_length']
    processing_times = np.array(instance['processing_times'])
    n_operations = sum(job_length)
    
    # SPT启发式
    operations = list(range(n_operations))
    operations.sort(key=lambda op: min(processing_times[op, m] 
                                     for m in range(processing_times.shape[1]) 
                                     if processing_times[op, m] > 0))
    
    spt_schedule = create_feasible_schedule(operations, job_length, processing_times)
    
    # FIFO启发式
    fifo_operations = list(range(n_operations))
    fifo_schedule = create_feasible_schedule(fifo_operations, job_length, processing_times)
    
    return {
        'SPT': spt_schedule,
        'FIFO': fifo_schedule
    }


def create_feasible_schedule(operation_order, job_length, processing_times):
    """创建可行的调度"""
    schedule = []
    job_progress = [0] * len(job_length)
    scheduled_ops = set()
    
    # 构建作业-操作映射
    job_operations = []
    op_id = 0
    for job_id, n_ops in enumerate(job_length):
        job_operations.append(list(range(op_id, op_id + n_ops)))
        op_id += n_ops
    
    # 按优先级调度可用操作
    for target_op in operation_order:
        if target_op in scheduled_ops:
            continue
        
        # 找到该操作属于哪个作业
        target_job = None
        for job_id, ops in enumerate(job_operations):
            if target_op in ops:
                target_job = job_id
                break
        
        if target_job is None:
            continue
        
        # 检查是否可以调度该操作
        op_index_in_job = job_operations[target_job].index(target_op)
        if job_progress[target_job] == op_index_in_job:
            # 可以调度
            valid_machines = [m for m in range(processing_times.shape[1]) 
                             if processing_times[target_op, m] > 0]
            
            if valid_machines:
                best_machine = min(valid_machines, 
                                 key=lambda m: processing_times[target_op, m])
                schedule.append((target_op, best_machine))
                scheduled_ops.add(target_op)
                job_progress[target_job] += 1
    
    return schedule


def evaluate_schedule(schedule, job_length, processing_times):
    """评估调度"""
    try:
        from schedule_evaluator import AccurateScheduleEvaluator
        evaluator = AccurateScheduleEvaluator(job_length, processing_times)
        result = evaluator.evaluate_schedule(schedule)
        return result.makespan, result.feasible
    except:
        # 简单评估
        if not schedule:
            return float('inf'), False
        
        n_machines = processing_times.shape[1]
        machine_times = [0] * n_machines
        
        for op_id, machine_id in schedule:
            if (machine_id < n_machines and 
                op_id < processing_times.shape[0] and 
                processing_times[op_id, machine_id] > 0):
                machine_times[machine_id] += processing_times[op_id, machine_id]
        
        makespan = max(machine_times) if machine_times else float('inf')
        return makespan, makespan < float('inf')


def test_final_system():
    """测试最终系统"""
    print("🎯 FINAL SYSTEM TEST - Neural Column Generation")
    print("=" * 60)
    print("📋 Testing: DANIEL-consistent data + Valid schedule model")
    
    # 加载最终训练的模型
    model, device = load_final_model()
    
    # 创建测试实例
    print(f"\n📊 Creating test instances...")
    instances = create_test_instances()
    print(f"✅ Created {len(instances)} test instances")
    
    # 测试每个实例
    total_neural_time = 0
    total_baseline_time = 0
    neural_results = []
    baseline_results = []
    
    for i, instance in enumerate(instances):
        print(f"\n🧪 Testing Instance {i+1}: {instance['n_jobs']} jobs, {instance['n_machines']} machines, {instance['n_operations']} operations")
        
        # 提取特征
        features = extract_features_for_model(instance)
        
        # 神经网络生成
        print(f"  🧠 Neural network generation...")
        start_time = time.time()
        
        try:
            columns = model.generate_valid_columns(
                features['job_features'].unsqueeze(0).to(device),
                features['operation_features'].unsqueeze(0).to(device),
                features['machine_features'].unsqueeze(0).to(device),
                features['processing_matrix'].unsqueeze(0).to(device),
                features['job_lengths'],
                num_columns=5
            )
            
            neural_time = time.time() - start_time
            total_neural_time += neural_time
            
            # 评估神经网络结果
            neural_makespans = []
            neural_feasible = 0
            
            for column in columns:
                if column.feasible:
                    neural_makespans.append(column.makespan)
                    neural_feasible += 1
            
            best_neural_makespan = min(neural_makespans) if neural_makespans else float('inf')
            
            print(f"    ✅ Generated {len(columns)} columns in {neural_time:.3f}s")
            print(f"    📊 Feasible: {neural_feasible}/{len(columns)}")
            print(f"    🎯 Best makespan: {best_neural_makespan:.1f}")
            
            neural_results.append({
                'feasible_count': neural_feasible,
                'total_count': len(columns),
                'best_makespan': best_neural_makespan,
                'time': neural_time
            })
            
        except Exception as e:
            print(f"    ❌ Neural generation failed: {e}")
            neural_results.append({
                'feasible_count': 0,
                'total_count': 0,
                'best_makespan': float('inf'),
                'time': 0
            })
        
        # 基线方法生成
        print(f"  📊 Baseline generation...")
        start_time = time.time()
        
        baseline_schedules = generate_baseline_solutions(instance)
        baseline_time = time.time() - start_time
        total_baseline_time += baseline_time
        
        baseline_makespans = []
        for method, schedule in baseline_schedules.items():
            makespan, feasible = evaluate_schedule(schedule, instance['job_length'], 
                                                 np.array(instance['processing_times']))
            if feasible:
                baseline_makespans.append(makespan)
            print(f"    {method}: makespan={makespan:.1f}, feasible={feasible}")
        
        best_baseline_makespan = min(baseline_makespans) if baseline_makespans else float('inf')
        
        baseline_results.append({
            'best_makespan': best_baseline_makespan,
            'time': baseline_time
        })
        
        # 比较结果
        if best_neural_makespan < float('inf') and best_baseline_makespan < float('inf'):
            improvement = (best_baseline_makespan - best_neural_makespan) / best_baseline_makespan * 100
            print(f"    📈 Neural improvement: {improvement:+.1f}%")
        else:
            print(f"    ⚠️  Cannot compare due to infeasible solutions")
    
    # 总结结果
    print(f"\n📈 FINAL SYSTEM PERFORMANCE SUMMARY")
    print(f"=" * 50)
    
    # 神经网络统计
    total_neural_feasible = sum(r['feasible_count'] for r in neural_results)
    total_neural_generated = sum(r['total_count'] for r in neural_results)
    neural_feasibility_rate = total_neural_feasible / max(1, total_neural_generated) * 100
    
    valid_neural_makespans = [r['best_makespan'] for r in neural_results if r['best_makespan'] < float('inf')]
    avg_neural_makespan = np.mean(valid_neural_makespans) if valid_neural_makespans else 0
    
    print(f"🧠 Neural Network Results:")
    print(f"  📊 Feasibility rate: {neural_feasibility_rate:.1f}% ({total_neural_feasible}/{total_neural_generated})")
    print(f"  🎯 Average makespan: {avg_neural_makespan:.1f}")
    print(f"  ⚡ Average time: {total_neural_time/len(instances):.3f}s")
    
    # 基线统计
    valid_baseline_makespans = [r['best_makespan'] for r in baseline_results if r['best_makespan'] < float('inf')]
    avg_baseline_makespan = np.mean(valid_baseline_makespans) if valid_baseline_makespans else 0
    
    print(f"📊 Baseline Results:")
    print(f"  🎯 Average makespan: {avg_baseline_makespan:.1f}")
    print(f"  ⚡ Average time: {total_baseline_time/len(instances):.3f}s")
    
    # 整体比较
    if valid_neural_makespans and valid_baseline_makespans:
        overall_improvement = (avg_baseline_makespan - avg_neural_makespan) / avg_baseline_makespan * 100
        print(f"\n🎉 OVERALL PERFORMANCE:")
        print(f"  📈 Quality improvement: {overall_improvement:+.1f}%")
        print(f"  ⚡ Speed ratio: {total_baseline_time/total_neural_time:.1f}x")
        
        if overall_improvement > 5:
            print(f"  🏆 EXCELLENT: Neural network significantly outperforms baselines!")
        elif overall_improvement > 0:
            print(f"  😊 GOOD: Neural network outperforms baselines")
        elif overall_improvement > -10:
            print(f"  😐 ACCEPTABLE: Neural network is competitive")
        else:
            print(f"  😞 NEEDS IMPROVEMENT: Neural network underperforms")
    
    # 系统状态
    print(f"\n🎯 SYSTEM STATUS:")
    if neural_feasibility_rate >= 90:
        print(f"  ✅ CONSTRAINT SATISFACTION: EXCELLENT ({neural_feasibility_rate:.1f}%)")
    elif neural_feasibility_rate >= 70:
        print(f"  😊 CONSTRAINT SATISFACTION: GOOD ({neural_feasibility_rate:.1f}%)")
    else:
        print(f"  ⚠️  CONSTRAINT SATISFACTION: NEEDS IMPROVEMENT ({neural_feasibility_rate:.1f}%)")
    
    if valid_neural_makespans:
        print(f"  ✅ SOLUTION QUALITY: FUNCTIONAL")
    else:
        print(f"  ❌ SOLUTION QUALITY: NOT FUNCTIONAL")
    
    print(f"\n🏁 FINAL CONCLUSION:")
    if neural_feasibility_rate >= 90 and valid_neural_makespans:
        print(f"  🎉 SUCCESS: Neural column generation system is WORKING and EFFECTIVE!")
        print(f"  🚀 Ready for integration with branch-and-price algorithms")
    elif neural_feasibility_rate >= 70:
        print(f"  😊 PARTIAL SUCCESS: System works but needs fine-tuning")
    else:
        print(f"  ⚠️  NEEDS WORK: System requires further development")
    
    return {
        'neural_feasibility_rate': neural_feasibility_rate,
        'avg_neural_makespan': avg_neural_makespan,
        'avg_baseline_makespan': avg_baseline_makespan,
        'overall_improvement': overall_improvement if 'overall_improvement' in locals() else None
    }


def main():
    """主函数"""
    print("🚀 NEURAL COLUMN GENERATION - FINAL SYSTEM TEST")
    print("=" * 70)
    
    results = test_final_system()
    
    print(f"\n📋 TEST COMPLETED")
    print(f"🎯 System is ready for production use!" if results['neural_feasibility_rate'] >= 90 else "🔧 System needs further development")


if __name__ == "__main__":
    main()
