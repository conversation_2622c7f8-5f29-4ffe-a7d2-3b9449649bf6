#!/usr/bin/env python3
"""
质量优化训练管道
使用改进的损失函数和优化的神经网络架构进行训练
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader
from tqdm import tqdm
import time
from datetime import datetime

# 添加路径
sys.path.append('..')
sys.path.append('.')

from improved_loss_functions import create_improved_loss, MakespanFocusedLoss, MultiObjectiveLoss
from valid_schedule_neural_model import ValidScheduleNeuralModel
from final_training_pipeline import DANIELDataset


class QualityOptimizedTrainer:
    """质量优化训练器"""
    
    def __init__(self, model, device, config=None):
        self.model = model.to(device)
        self.device = device
        
        # 训练配置
        self.config = config or {
            'learning_rate': 0.0005,  # 降低学习率
            'weight_decay': 0.02,     # 增加正则化
            'num_epochs': 40,         # 增加训练轮数
            'patience': 10,           # 早停耐心
            'loss_type': 'makespan_focused',  # 使用makespan导向损失
            'gradient_clip': 1.0,
            'scheduler_type': 'cosine'
        }
        
        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.config['learning_rate'],
            weight_decay=self.config['weight_decay'],
            betas=(0.9, 0.999),
            eps=1e-8
        )
        
        # 学习率调度器
        if self.config['scheduler_type'] == 'cosine':
            self.scheduler = optim.lr_scheduler.CosineAnnealingWarmRestarts(
                self.optimizer, T_0=10, T_mult=2, eta_min=1e-6
            )
        else:
            self.scheduler = optim.lr_scheduler.ReduceLROnPlateau(
                self.optimizer, mode='min', factor=0.5, patience=5, min_lr=1e-6
            )
        
        # 改进的损失函数
        self.criterion = create_improved_loss(
            self.config['loss_type'],
            alpha=2.0,  # 增加makespan权重
            beta=1.5,   # 增加可行性奖励
            gamma=0.8,  # 负载均衡权重
            delta=0.5   # 利用率权重
        )
        
        # 训练历史
        self.train_history = {
            'loss': [],
            'makespan_error': [],
            'feasibility_rate': [],
            'neural_decision_ratio': [],
            'learning_rate': []
        }
        
        # 早停
        self.best_loss = float('inf')
        self.patience_counter = 0
        
        print(f"🚀 Quality Optimized Trainer initialized")
        print(f"📱 Device: {self.device}")
        print(f"📊 Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        print(f"🎯 Loss function: {self.config['loss_type']}")
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        epoch_loss = 0.0
        epoch_makespan_error = 0.0
        epoch_feasibility = 0.0
        epoch_neural_ratio = 0.0
        n_batches = 0
        
        for batch in tqdm(train_loader, desc="Training"):
            self.optimizer.zero_grad()
            
            try:
                # 准备数据
                features = batch['features'][0]
                target_makespan = batch['target_makespan'][0]
                target_schedule = batch['target_schedule'][0]
                
                # 前向传播
                predicted_columns = self.model.generate_valid_columns(
                    features['job_features'].unsqueeze(0).to(self.device),
                    features['operation_features'].unsqueeze(0).to(self.device),
                    features['machine_features'].unsqueeze(0).to(self.device),
                    features['processing_matrix'].unsqueeze(0).to(self.device),
                    features['job_lengths'],
                    num_columns=3
                )
                
                # 计算损失
                if isinstance(self.criterion, (MakespanFocusedLoss, MultiObjectiveLoss)):
                    loss = self.criterion(
                        predicted_columns,
                        target_makespan,
                        features['processing_matrix'].unsqueeze(0),
                        features['job_lengths']
                    )
                else:
                    # 简化损失计算
                    loss = self._calculate_simple_loss(predicted_columns, target_makespan)
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    max_norm=self.config['gradient_clip']
                )
                
                self.optimizer.step()
                
                # 统计指标
                epoch_loss += loss.item()
                
                if predicted_columns:
                    # 计算makespan误差
                    valid_makespans = [col.makespan for col in predicted_columns if col.feasible]
                    if valid_makespans:
                        best_makespan = min(valid_makespans)
                        makespan_error = abs(best_makespan - target_makespan) / target_makespan
                        epoch_makespan_error += makespan_error
                    
                    # 计算可行性率
                    feasible_count = sum(1 for col in predicted_columns if col.feasible)
                    feasibility_rate = feasible_count / len(predicted_columns)
                    epoch_feasibility += feasibility_rate
                    
                    # 计算神经决策比例（如果可用）
                    neural_decisions = sum(getattr(col, 'neural_decisions', 0) for col in predicted_columns)
                    total_decisions = sum(
                        getattr(col, 'neural_decisions', 0) + getattr(col, 'fallback_decisions', 0) 
                        for col in predicted_columns
                    )
                    if total_decisions > 0:
                        neural_ratio = neural_decisions / total_decisions
                        epoch_neural_ratio += neural_ratio
                
                n_batches += 1
                
            except Exception as e:
                print(f"Training error: {e}")
                continue
        
        # 更新学习率
        if self.config['scheduler_type'] == 'cosine':
            self.scheduler.step()
        
        return {
            'loss': epoch_loss / max(1, n_batches),
            'makespan_error': epoch_makespan_error / max(1, n_batches),
            'feasibility_rate': epoch_feasibility / max(1, n_batches),
            'neural_decision_ratio': epoch_neural_ratio / max(1, n_batches)
        }
    
    def _calculate_simple_loss(self, predicted_columns, target_makespan):
        """简化损失计算"""
        if not predicted_columns:
            return torch.tensor(1000.0, requires_grad=True)
        
        total_loss = 0.0
        valid_count = 0
        
        for column in predicted_columns:
            if column.feasible:
                makespan_error = abs(column.makespan - target_makespan) / (target_makespan + 1e-6)
                loss = makespan_error ** 2 - 1.0  # 可行性奖励
                total_loss += loss
                valid_count += 1
            else:
                total_loss += 100.0
        
        if valid_count == 0:
            return torch.tensor(1000.0, requires_grad=True)
        
        return torch.tensor(total_loss / len(predicted_columns), requires_grad=True)
    
    def validate(self, val_loader):
        """验证"""
        self.model.eval()
        val_loss = 0.0
        val_makespan_error = 0.0
        val_feasibility = 0.0
        n_batches = 0
        
        with torch.no_grad():
            for batch in val_loader:
                try:
                    features = batch['features'][0]
                    target_makespan = batch['target_makespan'][0]
                    
                    predicted_columns = self.model.generate_valid_columns(
                        features['job_features'].unsqueeze(0).to(self.device),
                        features['operation_features'].unsqueeze(0).to(self.device),
                        features['machine_features'].unsqueeze(0).to(self.device),
                        features['processing_matrix'].unsqueeze(0).to(self.device),
                        features['job_lengths'],
                        num_columns=3
                    )
                    
                    # 计算验证指标
                    if predicted_columns:
                        valid_makespans = [col.makespan for col in predicted_columns if col.feasible]
                        if valid_makespans:
                            best_makespan = min(valid_makespans)
                            makespan_error = abs(best_makespan - target_makespan) / target_makespan
                            val_makespan_error += makespan_error
                        
                        feasible_count = sum(1 for col in predicted_columns if col.feasible)
                        feasibility_rate = feasible_count / len(predicted_columns)
                        val_feasibility += feasibility_rate
                        
                        loss = self._calculate_simple_loss(predicted_columns, target_makespan)
                        val_loss += loss.item()
                    
                    n_batches += 1
                    
                except Exception as e:
                    continue
        
        return {
            'val_loss': val_loss / max(1, n_batches),
            'val_makespan_error': val_makespan_error / max(1, n_batches),
            'val_feasibility_rate': val_feasibility / max(1, n_batches)
        }
    
    def train(self, train_loader, val_loader=None):
        """完整训练过程"""
        print(f"🚀 Starting quality-optimized training for {self.config['num_epochs']} epochs...")
        
        for epoch in range(self.config['num_epochs']):
            print(f"\n📊 Epoch {epoch + 1}/{self.config['num_epochs']}")
            
            # 训练
            train_stats = self.train_epoch(train_loader)
            
            # 验证
            val_stats = {}
            if val_loader:
                val_stats = self.validate(val_loader)
                
                # 学习率调度（基于验证损失）
                if self.config['scheduler_type'] == 'plateau':
                    self.scheduler.step(val_stats['val_loss'])
            
            # 记录历史
            self.train_history['loss'].append(train_stats['loss'])
            self.train_history['makespan_error'].append(train_stats['makespan_error'])
            self.train_history['feasibility_rate'].append(train_stats['feasibility_rate'])
            self.train_history['neural_decision_ratio'].append(train_stats['neural_decision_ratio'])
            self.train_history['learning_rate'].append(self.optimizer.param_groups[0]['lr'])
            
            # 打印统计
            print(f"  📈 Loss: {train_stats['loss']:.4f}")
            print(f"  📊 Makespan Error: {train_stats['makespan_error']:.4f}")
            print(f"  ✅ Feasibility Rate: {train_stats['feasibility_rate']:.3f}")
            print(f"  🧠 Neural Decision Ratio: {train_stats['neural_decision_ratio']:.3f}")
            print(f"  📊 LR: {self.optimizer.param_groups[0]['lr']:.6f}")
            
            if val_stats:
                print(f"  🔍 Val Loss: {val_stats['val_loss']:.4f}")
                print(f"  🔍 Val Makespan Error: {val_stats['val_makespan_error']:.4f}")
                print(f"  🔍 Val Feasibility: {val_stats['val_feasibility_rate']:.3f}")
            
            # 早停检查
            current_loss = val_stats.get('val_loss', train_stats['loss'])
            if current_loss < self.best_loss:
                self.best_loss = current_loss
                self.patience_counter = 0
                self.save_model("neural_column_generation/models/quality_optimized_best.pth")
                print(f"  🎯 New best model saved!")
            else:
                self.patience_counter += 1
                if self.patience_counter >= self.config['patience']:
                    print(f"  ⏹️  Early stopping triggered!")
                    break
            
            # 定期保存
            if (epoch + 1) % 10 == 0:
                self.save_checkpoint(epoch + 1)
        
        print(f"✅ Quality-optimized training completed!")
        return self.train_history
    
    def save_model(self, path):
        """保存模型"""
        model_data = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'train_history': self.train_history,
            'config': self.config,
            'best_loss': self.best_loss,
            'timestamp': datetime.now().isoformat()
        }
        
        os.makedirs(os.path.dirname(path), exist_ok=True)
        torch.save(model_data, path)
    
    def save_checkpoint(self, epoch):
        """保存检查点"""
        checkpoint_path = f"neural_column_generation/models/checkpoints/quality_optimized_epoch_{epoch}.pth"
        self.save_model(checkpoint_path)
        print(f"  💾 Checkpoint saved: epoch {epoch}")


def main():
    """主函数"""
    print("🎯 Quality Optimized Training Pipeline")
    print("=" * 60)
    
    # 检查数据集
    dataset_path = "neural_column_generation/daniel_data/daniel_consistent_dataset.pkl"
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset not found: {dataset_path}")
        print(f"Please run daniel_consistent_data_generator.py first")
        return
    
    # 创建数据集
    dataset = DANIELDataset(dataset_path)
    
    # 分割训练和验证集
    train_size = int(0.8 * len(dataset))
    val_size = len(dataset) - train_size
    train_dataset, val_dataset = torch.utils.data.random_split(dataset, [train_size, val_size])
    
    def custom_collate_fn(batch):
        return {
            'features': [item['features'] for item in batch],
            'target_schedule': [item['target_schedule'] for item in batch],
            'target_makespan': [item['target_makespan'] for item in batch],
            'instance_id': [item['instance_id'] for item in batch]
        }
    
    train_loader = DataLoader(
        train_dataset, batch_size=1, shuffle=True, collate_fn=custom_collate_fn
    )
    val_loader = DataLoader(
        val_dataset, batch_size=1, shuffle=False, collate_fn=custom_collate_fn
    )
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = ValidScheduleNeuralModel(d_model=128, n_heads=4, n_layers=3)
    
    print(f"📱 Device: {device}")
    print(f"📊 Training samples: {len(train_dataset)}")
    print(f"📊 Validation samples: {len(val_dataset)}")
    
    # 训练配置
    config = {
        'learning_rate': 0.0005,
        'weight_decay': 0.02,
        'num_epochs': 30,
        'patience': 8,
        'loss_type': 'makespan_focused',
        'gradient_clip': 1.0,
        'scheduler_type': 'cosine'
    }
    
    # 创建训练器
    trainer = QualityOptimizedTrainer(model, device, config)
    
    # 开始训练
    history = trainer.train(train_loader, val_loader)
    
    print(f"\n✅ Quality-optimized training completed!")
    print(f"📊 Best loss: {trainer.best_loss:.4f}")
    print(f"📊 Final feasibility rate: {history['feasibility_rate'][-1]:.3f}")
    print(f"📊 Final neural decision ratio: {history['neural_decision_ratio'][-1]:.3f}")
    print(f"🎯 Model saved to: neural_column_generation/models/quality_optimized_best.pth")


if __name__ == "__main__":
    main()
