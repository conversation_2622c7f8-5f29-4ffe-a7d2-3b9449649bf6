#!/usr/bin/env python3
"""
与DANIEL一致的数据生成器
使用与DANIEL相同的数据生成方式和训练数据来源
"""

import os
import sys
import json
import pickle
import numpy as np
import random
import torch
from tqdm import tqdm
from typing import List, Dict, Tuple
from dataclasses import dataclass

# 添加路径
sys.path.append('..')
sys.path.append('.')

# 导入DANIEL的数据生成组件（简化版本）
try:
    from data_utils import CaseGenerator
    DANIEL_AVAILABLE = True
except ImportError:
    DANIEL_AVAILABLE = False
    print("⚠️  DANIEL modules not available, using simplified data generation")


@dataclass
class DANIELTrainingConfig:
    """DANIEL训练配置（与原始保持一致）"""
    n_j: int = 10           # 作业数
    n_m: int = 5            # 机器数
    op_per_job_min: int = 1 # 每个作业最小操作数
    op_per_job_max: int = 10 # 每个作业最大操作数
    num_envs: int = 20      # 训练环境数量
    data_source: str = 'SD1' # 数据源
    
    # SD2数据生成参数
    op_per_job: float = 0   # 每个作业操作数（0表示等于机器数）
    op_per_mch_min: int = 1 # 每个操作最小兼容机器数
    op_per_mch_max: int = 5 # 每个操作最大兼容机器数


class DANIELConsistentDataGenerator:
    """与DANIEL一致的数据生成器"""
    
    def __init__(self, config: DANIELTrainingConfig = None, daniel_model_path="trained_network/SD1/10x5.pth"):
        self.config = config or DANIELTrainingConfig()
        self.device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

        # 简化：不加载DANIEL模型，使用启发式方法生成目标解
        self.daniel_model = None
        print(f"ℹ️  Using heuristic methods for target solution generation")
    
    def sample_training_instances_like_daniel(self, num_instances: int = 100):
        """
        按照DANIEL的方式采样训练实例
        这与train.py中的sample_training_instances方法完全一致
        """
        print(f"🔄 Sampling {num_instances} training instances like DANIEL...")
        
        instances = []
        
        for i in tqdm(range(num_instances), desc="Generating instances"):
            # 准备作业长度（与DANIEL一致）
            prepare_JobLength = [random.randint(self.config.op_per_job_min, self.config.op_per_job_max) 
                               for _ in range(self.config.n_j)]
            
            if DANIEL_AVAILABLE and self.config.data_source == 'SD1':
                try:
                    # 使用SD1数据生成器（与DANIEL完全一致）
                    case = CaseGenerator(
                        self.config.n_j,
                        self.config.n_m,
                        self.config.op_per_job_min,
                        self.config.op_per_job_max,
                        nums_ope=prepare_JobLength,
                        path='./temp',
                        flag_doc=False
                    )
                    job_length, op_pt, _ = case.get_case(i)
                except Exception as e:
                    print(f"DANIEL data generation failed: {e}, using simplified generation")
                    job_length, op_pt = self._generate_simplified_instance(prepare_JobLength)
            else:
                # 使用简化的数据生成器
                job_length, op_pt = self._generate_simplified_instance(prepare_JobLength)
            
            # 确保所有数据都是JSON可序列化的
            job_length_list = job_length.tolist() if isinstance(job_length, np.ndarray) else [int(x) for x in job_length]
            op_pt_list = op_pt.tolist() if isinstance(op_pt, np.ndarray) else [[float(x) for x in row] for row in op_pt]

            instance = {
                'instance_id': f"{self.config.data_source}_{i:04d}",
                'job_length': job_length_list,
                'processing_times': op_pt_list,
                'n_jobs': int(len(job_length)),
                'n_machines': int(op_pt.shape[1] if hasattr(op_pt, 'shape') else len(op_pt[0])),
                'n_operations': int(sum(job_length)),
                'data_source': self.config.data_source
            }
            
            instances.append(instance)
        
        print(f"✅ Generated {len(instances)} instances using DANIEL's method")
        return instances

    def _generate_simplified_instance(self, job_length):
        """简化的实例生成（当DANIEL不可用时）"""
        n_jobs = len(job_length)
        n_machines = self.config.n_m
        n_operations = sum(job_length)

        # 生成处理时间矩阵
        processing_times = np.zeros((n_operations, n_machines))

        for op_id in range(n_operations):
            # 每个操作在随机数量的机器上可用
            n_available = random.randint(1, min(n_machines, 3))
            available_machines = random.sample(range(n_machines), n_available)

            for machine_id in available_machines:
                # 处理时间在1-99之间（与DANIEL一致）
                processing_times[op_id, machine_id] = random.randint(1, 99)

        return np.array(job_length), processing_times
    
    def generate_target_solutions_with_daniel(self, instances: List[Dict], 
                                            strategies=['greedy', 'random', 'epsilon_greedy']):
        """使用DANIEL模型生成目标解"""
        if self.daniel_model is None:
            print("❌ DANIEL model not available, using heuristic methods instead")
            return self._generate_heuristic_solutions(instances)
        
        print(f"🧠 Generating target solutions with DANIEL model...")
        
        all_solutions = []
        
        for instance in tqdm(instances, desc="Generating solutions"):
            job_length = np.array(instance['job_length'])
            op_pt = np.array(instance['processing_times'])
            
            instance_solutions = []
            
            for strategy in strategies:
                try:
                    solution = self._solve_with_daniel(job_length, op_pt, strategy)
                    if solution:
                        instance_solutions.append({
                            'instance_id': instance['instance_id'],
                            'method': f'DANIEL_{strategy}',
                            'schedule': solution['schedule'],
                            'makespan': float(solution['makespan']),
                            'feasible': bool(solution['feasible']),
                            'quality_score': float(1.0 / (1.0 + solution['makespan'] / 100.0))
                        })
                except Exception as e:
                    print(f"Error solving {instance['instance_id']} with {strategy}: {e}")
                    continue
            
            all_solutions.extend(instance_solutions)
        
        print(f"✅ Generated {len(all_solutions)} target solutions")
        return all_solutions
    
    def _solve_with_daniel(self, job_length, op_pt, strategy='greedy'):
        """使用DANIEL求解单个实例"""
        n_j = len(job_length)
        n_m = op_pt.shape[1]
        
        # 创建环境
        env = FJSPEnvForSameOpNums(n_j=n_j, n_m=n_m)
        state = env.set_initial_data([job_length], [op_pt])
        
        schedule = []
        step_count = 0
        max_steps = sum(job_length) * 2
        
        while not state.done_batch.all() and step_count < max_steps:
            with torch.no_grad():
                # 获取动作概率
                pi, _ = self.daniel_model.policy(
                    fea_j=state.fea_j_tensor,
                    op_mask=state.op_mask_tensor,
                    candidate=state.candidate_tensor,
                    fea_m=state.fea_m_tensor,
                    mch_mask=state.mch_mask_tensor,
                    comp_idx=state.comp_idx_tensor,
                    dynamic_pair_mask=state.dynamic_pair_mask_tensor,
                    fea_pairs=state.fea_pairs_tensor
                )
                
                # 根据策略选择动作
                if strategy == 'greedy':
                    action = torch.argmax(pi, dim=1)
                elif strategy == 'random':
                    action = torch.multinomial(pi, 1).squeeze()
                elif strategy == 'epsilon_greedy':
                    if random.random() < 0.1:  # 10%探索
                        action = torch.multinomial(pi, 1).squeeze()
                    else:
                        action = torch.argmax(pi, dim=1)
                else:
                    action = torch.argmax(pi, dim=1)
                
                # 执行动作
                state, reward, done = env.step(action.cpu().numpy())
                
                # 记录调度决策
                if not done.all():
                    # 解析动作为(operation, machine)对
                    action_val = action.item()
                    # 这里需要根据环境的动作编码方式来解析
                    # 简化处理：假设动作直接对应操作-机器对
                    schedule.append((step_count % sum(job_length), action_val % n_m))
                
                step_count += 1
        
        # 计算makespan
        makespan = env.current_makespan.item() if hasattr(env, 'current_makespan') else 0
        
        return {
            'schedule': schedule,
            'makespan': makespan,
            'feasible': len(schedule) > 0 and makespan > 0
        }
    
    def _generate_heuristic_solutions(self, instances: List[Dict]):
        """生成启发式解（当DANIEL模型不可用时）"""
        print("🔄 Generating heuristic solutions...")
        
        all_solutions = []
        
        for instance in tqdm(instances, desc="Generating heuristic solutions"):
            job_length = instance['job_length']
            processing_times = np.array(instance['processing_times'])
            
            # SPT启发式
            spt_schedule = self._generate_spt_schedule(job_length, processing_times)
            if spt_schedule:
                makespan = self._evaluate_schedule(spt_schedule, job_length, processing_times)
                all_solutions.append({
                    'instance_id': instance['instance_id'],
                    'method': 'SPT',
                    'schedule': spt_schedule,
                    'makespan': float(makespan),
                    'feasible': bool(makespan < float('inf')),
                    'quality_score': float(1.0 / (1.0 + makespan / 100.0))
                })
            
            # FIFO启发式
            fifo_schedule = self._generate_fifo_schedule(job_length, processing_times)
            if fifo_schedule:
                makespan = self._evaluate_schedule(fifo_schedule, job_length, processing_times)
                all_solutions.append({
                    'instance_id': instance['instance_id'],
                    'method': 'FIFO',
                    'schedule': fifo_schedule,
                    'makespan': float(makespan),
                    'feasible': bool(makespan < float('inf')),
                    'quality_score': float(1.0 / (1.0 + makespan / 100.0))
                })
        
        return all_solutions
    
    def _generate_spt_schedule(self, job_length, processing_times):
        """生成SPT调度"""
        n_operations = sum(job_length)
        operations = list(range(n_operations))
        
        # 按最短处理时间排序
        operations.sort(key=lambda op: min(processing_times[op, m] 
                                         for m in range(processing_times.shape[1]) 
                                         if processing_times[op, m] > 0))
        
        return self._create_feasible_schedule(operations, job_length, processing_times)
    
    def _generate_fifo_schedule(self, job_length, processing_times):
        """生成FIFO调度"""
        n_operations = sum(job_length)
        operations = list(range(n_operations))
        
        return self._create_feasible_schedule(operations, job_length, processing_times)
    
    def _create_feasible_schedule(self, operation_order, job_length, processing_times):
        """创建可行的调度（确保满足作业内操作顺序约束）"""
        schedule = []
        job_progress = [0] * len(job_length)
        scheduled_ops = set()
        
        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_length):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops
        
        # 按优先级调度可用操作
        for target_op in operation_order:
            if target_op in scheduled_ops:
                continue
            
            # 找到该操作属于哪个作业
            target_job = None
            for job_id, ops in enumerate(job_operations):
                if target_op in ops:
                    target_job = job_id
                    break
            
            if target_job is None:
                continue
            
            # 检查是否可以调度该操作（必须满足作业内顺序）
            op_index_in_job = job_operations[target_job].index(target_op)
            if job_progress[target_job] == op_index_in_job:
                # 可以调度
                valid_machines = [m for m in range(processing_times.shape[1]) 
                                 if processing_times[target_op, m] > 0]
                
                if valid_machines:
                    # 选择处理时间最短的机器
                    best_machine = min(valid_machines, 
                                     key=lambda m: processing_times[target_op, m])
                    
                    schedule.append((target_op, best_machine))
                    scheduled_ops.add(target_op)
                    job_progress[target_job] += 1
        
        return schedule
    
    def _evaluate_schedule(self, schedule, job_length, processing_times):
        """评估调度的makespan"""
        if not schedule:
            return float('inf')
        
        try:
            from schedule_evaluator import AccurateScheduleEvaluator
            evaluator = AccurateScheduleEvaluator(job_length, processing_times)
            result = evaluator.evaluate_schedule(schedule)
            return result.makespan if result.feasible else float('inf')
        except:
            # 简单评估
            n_machines = processing_times.shape[1]
            machine_times = [0] * n_machines
            
            for op_id, machine_id in schedule:
                if (machine_id < n_machines and 
                    op_id < processing_times.shape[0] and 
                    processing_times[op_id, machine_id] > 0):
                    machine_times[machine_id] += processing_times[op_id, machine_id]
            
            return max(machine_times) if machine_times else float('inf')
    
    def create_training_dataset(self, num_instances: int = 100, output_dir: str = "neural_column_generation/daniel_data"):
        """创建完整的训练数据集"""
        print(f"🚀 Creating DANIEL-consistent training dataset...")
        
        # 设置随机种子（与DANIEL一致）
        random.seed(300)
        np.random.seed(300)
        torch.manual_seed(300)
        
        # 生成实例
        instances = self.sample_training_instances_like_daniel(num_instances)
        
        # 生成目标解
        solutions = self.generate_target_solutions_with_daniel(instances)
        
        # 创建训练数据集
        dataset = {
            'instances': instances,
            'solutions': solutions,
            'metadata': {
                'num_instances': len(instances),
                'num_solutions': len(solutions),
                'data_source': self.config.data_source,
                'generation_method': 'DANIEL_consistent',
                'config': self.config.__dict__
            }
        }
        
        # 保存数据集
        os.makedirs(output_dir, exist_ok=True)
        
        # JSON格式
        json_file = os.path.join(output_dir, "daniel_consistent_dataset.json")
        with open(json_file, 'w') as f:
            json.dump(dataset, f, indent=2)
        
        # Pickle格式
        pickle_file = os.path.join(output_dir, "daniel_consistent_dataset.pkl")
        with open(pickle_file, 'wb') as f:
            pickle.dump(dataset, f)
        
        print(f"💾 Dataset saved:")
        print(f"  📄 JSON: {json_file}")
        print(f"  🗜️  Pickle: {pickle_file}")
        print(f"  📊 Instances: {len(instances)}")
        print(f"  📊 Solutions: {len(solutions)}")
        
        # 分析数据质量
        self._analyze_dataset_quality(solutions)
        
        return dataset
    
    def _analyze_dataset_quality(self, solutions):
        """分析数据集质量"""
        if not solutions:
            return
        
        print(f"\n📈 Dataset Quality Analysis:")
        
        # 按方法统计
        method_stats = {}
        for solution in solutions:
            method = solution['method']
            if method not in method_stats:
                method_stats[method] = {'count': 0, 'feasible': 0, 'makespans': []}
            
            method_stats[method]['count'] += 1
            if solution['feasible']:
                method_stats[method]['feasible'] += 1
                method_stats[method]['makespans'].append(solution['makespan'])
        
        for method, stats in method_stats.items():
            feasibility_rate = stats['feasible'] / stats['count'] * 100
            avg_makespan = np.mean(stats['makespans']) if stats['makespans'] else 0
            
            print(f"  - {method}: {stats['feasible']}/{stats['count']} feasible ({feasibility_rate:.1f}%), "
                  f"avg makespan: {avg_makespan:.1f}")


def main():
    """主函数"""
    print("🎯 DANIEL-Consistent Data Generator")
    print("=" * 50)
    
    # 创建配置
    config = DANIELTrainingConfig(
        n_j=10,
        n_m=5,
        op_per_job_min=1,
        op_per_job_max=10,
        data_source='SD1'
    )
    
    # 创建数据生成器
    generator = DANIELConsistentDataGenerator(config)
    
    # 生成数据集
    dataset = generator.create_training_dataset(
        num_instances=50,  # 先生成50个实例测试
        output_dir="neural_column_generation/daniel_data"
    )
    
    if dataset:
        print(f"\n✅ DANIEL-consistent dataset created successfully!")
        print(f"🎯 Ready for neural network training with valid schedules")
    else:
        print(f"\n❌ Dataset creation failed!")


if __name__ == "__main__":
    main()
