#!/usr/bin/env python3
"""
SD数据集基准测试
在SD数据集上测试改进的神经列生成系统，并与基线算法对比
"""

import os
import sys
import torch
import numpy as np
import time
from typing import List, Dict, Tuple
import pickle
from collections import defaultdict

# 添加路径
sys.path.append('..')
sys.path.append('.')

from fixed_machine_selection import ImprovedNeuralModel
from daniel_consistent_data_generator import DANIELConsistentDataGenerator, DANIELTrainingConfig


def load_sd_dataset():
    """加载SD数据集"""
    print("📊 Loading SD dataset...")
    
    # 创建不同规模的SD实例
    test_instances = []
    
    # SD1: 小规模 (3-5 jobs, 2-4 machines)
    config_sd1_small = DANIELTrainingConfig(n_j=3, n_m=3, data_source='SD1')
    generator_sd1_small = DANIELConsistentDataGenerator(config_sd1_small)
    sd1_small = generator_sd1_small.sample_training_instances_like_daniel(3)
    
    config_sd1_medium = DANIELTrainingConfig(n_j=5, n_m=4, data_source='SD1')
    generator_sd1_medium = DANIELConsistentDataGenerator(config_sd1_medium)
    sd1_medium = generator_sd1_medium.sample_training_instances_like_daniel(3)
    
    # SD2: 中等规模 (6-10 jobs, 4-6 machines)
    config_sd2_small = DANIELTrainingConfig(n_j=6, n_m=4, data_source='SD2')
    generator_sd2_small = DANIELConsistentDataGenerator(config_sd2_small)
    sd2_small = generator_sd2_small.sample_training_instances_like_daniel(2)
    
    config_sd2_medium = DANIELTrainingConfig(n_j=8, n_m=5, data_source='SD2')
    generator_sd2_medium = DANIELConsistentDataGenerator(config_sd2_medium)
    sd2_medium = generator_sd2_medium.sample_training_instances_like_daniel(2)
    
    config_sd2_large = DANIELTrainingConfig(n_j=10, n_m=6, data_source='SD2')
    generator_sd2_large = DANIELConsistentDataGenerator(config_sd2_large)
    sd2_large = generator_sd2_large.sample_training_instances_like_daniel(2)
    
    # 合并所有实例
    all_instances = sd1_small + sd1_medium + sd2_small + sd2_medium + sd2_large
    
    print(f"✅ Loaded {len(all_instances)} SD instances:")
    for i, instance in enumerate(all_instances):
        print(f"  Instance {i+1}: {instance['n_jobs']}J×{instance['n_machines']}M ({instance['n_operations']} ops) - {instance.get('data_source', 'SD')}")
    
    return all_instances


def load_high_quality_model():
    """加载高质量模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = ImprovedNeuralModel(d_model=128, n_heads=4, n_layers=3).to(device)
    
    # 加载权重
    model_path = "neural_column_generation/models/high_quality_best.pth"
    if os.path.exists(model_path):
        try:
            checkpoint = torch.load(model_path, map_location=device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()
            print(f"✅ High quality model loaded")
            return model, device
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            return None, device
    else:
        print(f"❌ Model file not found: {model_path}")
        return None, device


def extract_features_for_model(instance):
    """提取模型特征"""
    job_length = instance['job_length']
    processing_times = np.array(instance['processing_times'])
    n_jobs = instance['n_jobs']
    n_machines = instance['n_machines']
    n_operations = instance['n_operations']
    
    # 作业特征
    job_features = []
    for job_id, n_ops in enumerate(job_length):
        op_start = sum(job_length[:job_id])
        op_end = op_start + n_ops
        
        total_workload = 0
        for op_idx in range(op_start, op_end):
            if op_idx < len(processing_times):
                valid_times = [t for t in processing_times[op_idx] if t > 0]
                if valid_times:
                    min_time = min(valid_times)
                    total_workload += min_time
        
        job_feat = [
            job_id / n_jobs, n_ops / n_operations, total_workload / 1000.0,
            n_ops / max(job_length), total_workload / max(1, n_ops * 20),
            0, 0, 0, 0, 0
        ]
        job_features.append(job_feat)
    
    # 操作特征
    operation_features = []
    for op_id in range(n_operations):
        if op_id < len(processing_times):
            proc_times = processing_times[op_id]
            
            job_id = 0
            op_in_job = op_id
            for j, n_ops in enumerate(job_length):
                if op_in_job < n_ops:
                    job_id = j
                    break
                op_in_job -= n_ops
            
            valid_times = [t for t in proc_times if t > 0]
            min_time = min(valid_times) if valid_times else 0
            max_time = max(valid_times) if valid_times else 0
            avg_time = np.mean(valid_times) if valid_times else 0
            n_machines_available = len(valid_times)
            
            op_feat = [
                op_id / n_operations, job_id / n_jobs, op_in_job / max(job_length),
                min_time / 50.0, max_time / 50.0, avg_time / 50.0,
                n_machines_available / n_machines, (max_time - min_time) / max(1, 50.0),
                0, 0, 0, 0, 0, 0, 0
            ]
            operation_features.append(op_feat)
    
    # 机器特征
    machine_features = []
    for machine_id in range(n_machines):
        total_load = 0
        n_operations_available = 0
        
        for op_id in range(n_operations):
            if op_id < len(processing_times) and machine_id < len(processing_times[op_id]):
                proc_time = processing_times[op_id][machine_id]
                if proc_time > 0:
                    total_load += proc_time
                    n_operations_available += 1
        
        avg_load = total_load / max(1, n_operations_available)
        
        machine_feat = [
            machine_id / n_machines, total_load / 1000.0,
            n_operations_available / n_operations, avg_load / 50.0,
            0, 0, 0, 0
        ]
        machine_features.append(machine_feat)
    
    return {
        'job_features': torch.FloatTensor(job_features),
        'operation_features': torch.FloatTensor(operation_features),
        'machine_features': torch.FloatTensor(machine_features),
        'processing_matrix': torch.FloatTensor(processing_times),
        'job_lengths': job_length
    }


def evaluate_schedule(schedule, instance):
    """评估调度"""
    if not schedule:
        return float('inf'), False
    
    processing_times = np.array(instance['processing_times'])
    job_lengths = instance['job_length']
    n_machines = instance['n_machines']
    n_operations = instance['n_operations']
    
    # 检查调度可行性
    job_progress = [0] * len(job_lengths)
    scheduled_ops = set()
    
    # 构建作业-操作映射
    job_operations = []
    op_id = 0
    for job_id, n_ops in enumerate(job_lengths):
        job_operations.append(list(range(op_id, op_id + n_ops)))
        op_id += n_ops
    
    machine_times = [0] * n_machines
    
    for op_id, machine_id in schedule:
        # 检查操作是否存在
        if op_id >= n_operations or machine_id >= n_machines:
            return float('inf'), False
        
        # 检查处理时间是否有效
        if processing_times[op_id, machine_id] <= 0:
            return float('inf'), False
        
        # 检查作业顺序约束
        target_job = None
        for job_id, ops in enumerate(job_operations):
            if op_id in ops:
                target_job = job_id
                break
        
        if target_job is None:
            return float('inf'), False
        
        op_index_in_job = job_operations[target_job].index(op_id)
        if job_progress[target_job] != op_index_in_job:
            return float('inf'), False
        
        # 更新机器时间和作业进度
        machine_times[machine_id] += processing_times[op_id, machine_id]
        job_progress[target_job] += 1
        scheduled_ops.add(op_id)
    
    # 检查是否所有操作都被调度
    if len(scheduled_ops) != n_operations:
        return float('inf'), False
    
    makespan = max(machine_times)
    return makespan, True


def generate_baseline_solutions(instance):
    """生成基线解决方案"""
    job_lengths = instance['job_length']
    processing_times = np.array(instance['processing_times'])
    n_operations = instance['n_operations']
    n_machines = instance['n_machines']
    
    # SPT启发式 (Shortest Processing Time)
    operations = list(range(n_operations))
    operations.sort(key=lambda op: min(processing_times[op, m] 
                                     for m in range(n_machines) 
                                     if processing_times[op, m] > 0))
    
    spt_schedule = create_feasible_schedule(operations, job_lengths, processing_times)
    spt_makespan, spt_feasible = evaluate_schedule(spt_schedule, instance)
    
    # FIFO启发式 (First In First Out)
    fifo_operations = list(range(n_operations))
    fifo_schedule = create_feasible_schedule(fifo_operations, job_lengths, processing_times)
    fifo_makespan, fifo_feasible = evaluate_schedule(fifo_schedule, instance)
    
    # LPT启发式 (Longest Processing Time)
    lpt_operations = list(range(n_operations))
    lpt_operations.sort(key=lambda op: max(processing_times[op, m] 
                                         for m in range(n_machines) 
                                         if processing_times[op, m] > 0), reverse=True)
    
    lpt_schedule = create_feasible_schedule(lpt_operations, job_lengths, processing_times)
    lpt_makespan, lpt_feasible = evaluate_schedule(lpt_schedule, instance)
    
    return {
        'SPT': {'makespan': spt_makespan, 'feasible': spt_feasible, 'schedule': spt_schedule},
        'FIFO': {'makespan': fifo_makespan, 'feasible': fifo_feasible, 'schedule': fifo_schedule},
        'LPT': {'makespan': lpt_makespan, 'feasible': lpt_feasible, 'schedule': lpt_schedule}
    }


def create_feasible_schedule(operation_order, job_lengths, processing_times):
    """创建可行调度"""
    schedule = []
    job_progress = [0] * len(job_lengths)
    scheduled_ops = set()
    
    # 构建作业-操作映射
    job_operations = []
    op_id = 0
    for job_id, n_ops in enumerate(job_lengths):
        job_operations.append(list(range(op_id, op_id + n_ops)))
        op_id += n_ops
    
    for target_op in operation_order:
        if target_op in scheduled_ops:
            continue
        
        # 找到操作所属作业
        target_job = None
        for job_id, ops in enumerate(job_operations):
            if target_op in ops:
                target_job = job_id
                break
        
        if target_job is None:
            continue
        
        # 检查是否可调度
        op_index_in_job = job_operations[target_job].index(target_op)
        if job_progress[target_job] == op_index_in_job:
            valid_machines = [m for m in range(processing_times.shape[1]) 
                             if processing_times[target_op, m] > 0]
            
            if valid_machines:
                # 选择处理时间最短的机器
                best_machine = min(valid_machines, 
                                 key=lambda m: processing_times[target_op, m])
                
                schedule.append((target_op, best_machine))
                scheduled_ops.add(target_op)
                job_progress[target_job] += 1
    
    return schedule


def test_neural_model(model, instance, device):
    """测试神经网络模型"""
    features = extract_features_for_model(instance)
    
    start_time = time.time()
    
    try:
        with torch.no_grad():
            columns = model.generate_improved_columns(
                features['job_features'].unsqueeze(0).to(device),
                features['operation_features'].unsqueeze(0).to(device),
                features['machine_features'].unsqueeze(0).to(device),
                features['processing_matrix'].unsqueeze(0).to(device),
                features['job_lengths'],
                num_columns=5
            )
        
        generation_time = time.time() - start_time
        
        # 评估生成的列
        valid_makespans = []
        feasible_count = 0
        decision_qualities = []
        
        for column in columns:
            if hasattr(column, 'feasible') and column.feasible:
                makespan, feasible = evaluate_schedule(column.schedule, instance)
                if feasible and makespan < float('inf'):
                    valid_makespans.append(makespan)
                    feasible_count += 1
                    
                    decision_quality = getattr(column, 'decision_quality', 0.0)
                    decision_qualities.append(decision_quality)
        
        return {
            'success': feasible_count > 0,
            'best_makespan': min(valid_makespans) if valid_makespans else float('inf'),
            'avg_makespan': np.mean(valid_makespans) if valid_makespans else float('inf'),
            'feasible_count': feasible_count,
            'total_columns': len(columns),
            'generation_time': generation_time,
            'avg_decision_quality': np.mean(decision_qualities) if decision_qualities else 0.0
        }
        
    except Exception as e:
        return {
            'success': False,
            'error': str(e),
            'generation_time': time.time() - start_time
        }


def run_sd_benchmark():
    """运行SD数据集基准测试"""
    print("🎯 SD DATASET BENCHMARK - Neural Column Generation")
    print("=" * 70)
    
    # 加载数据集
    instances = load_sd_dataset()
    
    # 加载模型
    model, device = load_high_quality_model()
    
    if model is None:
        print("❌ Cannot proceed without model")
        return
    
    # 测试结果
    all_results = []
    
    print(f"\n🧪 Testing on {len(instances)} SD instances...")
    
    for i, instance in enumerate(instances):
        print(f"\n📊 Instance {i+1}: {instance['n_jobs']}J×{instance['n_machines']}M ({instance['n_operations']} ops)")
        
        instance_result = {
            'instance_id': instance['instance_id'],
            'size': f"{instance['n_jobs']}J×{instance['n_machines']}M",
            'n_operations': instance['n_operations'],
            'data_source': instance.get('data_source', 'SD')
        }
        
        # 测试神经网络
        print(f"  🧠 Testing neural model...")
        neural_start = time.time()
        neural_result = test_neural_model(model, instance, device)
        neural_time = time.time() - neural_start
        
        if neural_result['success']:
            print(f"    ✅ Best makespan: {neural_result['best_makespan']:.1f}")
            print(f"    📊 Feasible: {neural_result['feasible_count']}/{neural_result['total_columns']}")
            print(f"    🎯 Decision quality: {neural_result['avg_decision_quality']:.3f}")
            print(f"    ⚡ Time: {neural_result['generation_time']:.3f}s")
        else:
            print(f"    ❌ Failed: {neural_result.get('error', 'Unknown error')}")
        
        instance_result['neural'] = neural_result
        
        # 测试基线算法
        print(f"  📊 Testing baseline algorithms...")
        baseline_start = time.time()
        baseline_results = generate_baseline_solutions(instance)
        baseline_time = time.time() - baseline_start
        
        best_baseline_makespan = float('inf')
        for method, result in baseline_results.items():
            if result['feasible'] and result['makespan'] < float('inf'):
                print(f"    {method}: {result['makespan']:.1f}")
                best_baseline_makespan = min(best_baseline_makespan, result['makespan'])
            else:
                print(f"    {method}: infeasible")
        
        instance_result['baselines'] = baseline_results
        instance_result['best_baseline'] = best_baseline_makespan
        instance_result['baseline_time'] = baseline_time
        
        # 比较结果
        if neural_result['success'] and best_baseline_makespan < float('inf'):
            improvement = (best_baseline_makespan - neural_result['best_makespan']) / best_baseline_makespan * 100
            instance_result['improvement'] = improvement
            print(f"  📈 Neural vs Baseline: {improvement:+.1f}%")
        else:
            instance_result['improvement'] = None
            print(f"  ⚠️  Cannot compare due to failures")
        
        all_results.append(instance_result)
    
    # 综合分析
    print(f"\n📈 COMPREHENSIVE SD BENCHMARK RESULTS")
    print("=" * 60)
    
    # 成功率统计
    neural_successes = [r for r in all_results if r['neural']['success']]
    neural_success_rate = len(neural_successes) / len(all_results) * 100
    
    print(f"🎯 Neural Network Performance:")
    print(f"  Success Rate: {neural_success_rate:.1f}% ({len(neural_successes)}/{len(all_results)})")
    
    if neural_successes:
        # 质量分析
        neural_makespans = [r['neural']['best_makespan'] for r in neural_successes]
        avg_neural_makespan = np.mean(neural_makespans)
        
        # 基线分析
        baseline_makespans = [r['best_baseline'] for r in all_results if r['best_baseline'] < float('inf')]
        avg_baseline_makespan = np.mean(baseline_makespans) if baseline_makespans else float('inf')
        
        print(f"  Average Makespan: {avg_neural_makespan:.1f}")
        print(f"  Average Generation Time: {np.mean([r['neural']['generation_time'] for r in neural_successes]):.3f}s")
        print(f"  Average Decision Quality: {np.mean([r['neural']['avg_decision_quality'] for r in neural_successes]):.3f}")
        
        print(f"\n📊 Baseline Performance:")
        print(f"  Average Makespan: {avg_baseline_makespan:.1f}")
        print(f"  Average Generation Time: {np.mean([r['baseline_time'] for r in all_results]):.3f}s")
        
        # 质量比较
        valid_improvements = [r['improvement'] for r in all_results if r['improvement'] is not None]
        if valid_improvements:
            avg_improvement = np.mean(valid_improvements)
            positive_improvements = [imp for imp in valid_improvements if imp > 0]
            
            print(f"\n🏆 Quality Comparison:")
            print(f"  Average Improvement: {avg_improvement:+.1f}%")
            print(f"  Instances with Improvement: {len(positive_improvements)}/{len(valid_improvements)}")
            print(f"  Best Improvement: {max(valid_improvements):+.1f}%")
            print(f"  Worst Performance: {min(valid_improvements):+.1f}%")
        
        # 按规模分析
        print(f"\n📊 Performance by Instance Size:")
        size_groups = defaultdict(list)
        for result in all_results:
            if result['neural']['success']:
                size_groups[result['size']].append(result)
        
        for size, results in size_groups.items():
            if results:
                avg_makespan = np.mean([r['neural']['best_makespan'] for r in results])
                avg_improvement = np.mean([r['improvement'] for r in results if r['improvement'] is not None])
                print(f"  {size}: makespan={avg_makespan:.1f}, improvement={avg_improvement:+.1f}%")
    
    # 最终评估
    print(f"\n🏁 FINAL SD BENCHMARK EVALUATION:")
    
    if neural_success_rate >= 90:
        print(f"  ✅ RELIABILITY: EXCELLENT ({neural_success_rate:.1f}%)")
    elif neural_success_rate >= 70:
        print(f"  😊 RELIABILITY: GOOD ({neural_success_rate:.1f}%)")
    else:
        print(f"  ⚠️  RELIABILITY: NEEDS IMPROVEMENT ({neural_success_rate:.1f}%)")
    
    if valid_improvements and avg_improvement > 5:
        print(f"  🏆 QUALITY: SUPERIOR (avg {avg_improvement:+.1f}%)")
    elif valid_improvements and avg_improvement > 0:
        print(f"  😊 QUALITY: COMPETITIVE (avg {avg_improvement:+.1f}%)")
    elif valid_improvements:
        print(f"  😐 QUALITY: BASELINE LEVEL (avg {avg_improvement:+.1f}%)")
    else:
        print(f"  ❌ QUALITY: INSUFFICIENT DATA")
    
    return all_results


def main():
    """主函数"""
    print("🎯 SD DATASET BENCHMARK - NEURAL COLUMN GENERATION")
    print("=" * 80)
    
    results = run_sd_benchmark()
    
    print(f"\n📋 SD BENCHMARK COMPLETED")
    print(f"🎯 Neural column generation SD evaluation finished!")


if __name__ == "__main__":
    main()
