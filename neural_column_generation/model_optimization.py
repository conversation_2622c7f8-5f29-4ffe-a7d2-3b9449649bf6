#!/usr/bin/env python3
"""
模型剪枝和量化优化
实现模型压缩技术，提升推理速度，为实时分支定价做准备
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import torch.quantization as quant
import numpy as np
import time
import os
from typing import Dict, List, Tuple
from dataclasses import dataclass


@dataclass
class OptimizationMetrics:
    """优化指标"""
    original_size: float  # MB
    compressed_size: float  # MB
    compression_ratio: float
    original_inference_time: float  # seconds
    optimized_inference_time: float  # seconds
    speedup_ratio: float
    accuracy_loss: float  # percentage


class ModelPruner:
    """模型剪枝器"""
    
    def __init__(self, model, pruning_ratio=0.3):
        self.model = model
        self.pruning_ratio = pruning_ratio
        self.original_params = sum(p.numel() for p in model.parameters())
    
    def structured_pruning(self, importance_threshold=0.1):
        """结构化剪枝：移除整个神经元/通道"""
        print(f"🔧 Applying structured pruning (ratio: {self.pruning_ratio})")
        
        # 计算每层的重要性
        layer_importance = self._calculate_layer_importance()
        
        # 剪枝线性层
        for name, module in self.model.named_modules():
            if isinstance(module, nn.Linear):
                self._prune_linear_layer(module, name, layer_importance.get(name, 1.0))
        
        # 剪枝注意力层
        for name, module in self.model.named_modules():
            if isinstance(module, nn.MultiheadAttention):
                self._prune_attention_layer(module, name)
        
        pruned_params = sum(p.numel() for p in self.model.parameters())
        actual_ratio = 1 - (pruned_params / self.original_params)
        
        print(f"✅ Structured pruning completed")
        print(f"📊 Parameters: {self.original_params:,} → {pruned_params:,}")
        print(f"📊 Actual pruning ratio: {actual_ratio:.2%}")
        
        return self.model
    
    def unstructured_pruning(self):
        """非结构化剪枝：移除单个权重"""
        print(f"🔧 Applying unstructured pruning (ratio: {self.pruning_ratio})")
        
        # 计算全局阈值
        all_weights = []
        for module in self.model.modules():
            if isinstance(module, (nn.Linear, nn.Conv1d, nn.Conv2d)):
                all_weights.extend(module.weight.data.abs().flatten().tolist())
        
        all_weights.sort()
        threshold_idx = int(len(all_weights) * self.pruning_ratio)
        threshold = all_weights[threshold_idx]
        
        # 应用剪枝
        pruned_count = 0
        total_count = 0
        
        for module in self.model.modules():
            if isinstance(module, (nn.Linear, nn.Conv1d, nn.Conv2d)):
                mask = module.weight.data.abs() > threshold
                module.weight.data *= mask.float()
                
                pruned_count += (~mask).sum().item()
                total_count += mask.numel()
        
        actual_ratio = pruned_count / total_count
        
        print(f"✅ Unstructured pruning completed")
        print(f"📊 Weights pruned: {pruned_count:,} / {total_count:,}")
        print(f"📊 Actual pruning ratio: {actual_ratio:.2%}")
        
        return self.model
    
    def _calculate_layer_importance(self):
        """计算层重要性"""
        importance = {}
        
        for name, module in self.model.named_modules():
            if isinstance(module, nn.Linear):
                # 基于权重大小计算重要性
                weight_norm = torch.norm(module.weight.data, dim=1)
                importance[name] = weight_norm.mean().item()
        
        return importance
    
    def _prune_linear_layer(self, module, name, importance):
        """剪枝线性层"""
        if importance < 0.5:  # 低重要性层进行更多剪枝
            local_ratio = min(self.pruning_ratio * 1.5, 0.8)
        else:
            local_ratio = self.pruning_ratio * 0.5
        
        # 计算要保留的神经元数量
        out_features = module.out_features
        keep_neurons = int(out_features * (1 - local_ratio))
        keep_neurons = max(keep_neurons, out_features // 4)  # 至少保留1/4
        
        if keep_neurons < out_features:
            # 基于权重大小选择要保留的神经元
            weight_norms = torch.norm(module.weight.data, dim=1)
            _, indices = torch.topk(weight_norms, keep_neurons)
            
            # 创建新的权重和偏置
            new_weight = module.weight.data[indices, :]
            new_bias = module.bias.data[indices] if module.bias is not None else None
            
            # 更新模块
            module.out_features = keep_neurons
            module.weight = nn.Parameter(new_weight)
            if new_bias is not None:
                module.bias = nn.Parameter(new_bias)
    
    def _prune_attention_layer(self, module, name):
        """剪枝注意力层"""
        # 减少注意力头数量
        original_heads = module.num_heads
        new_heads = max(original_heads // 2, 1)
        
        if new_heads < original_heads:
            module.num_heads = new_heads
            # 注意：这里需要更复杂的权重重组，简化处理
            print(f"  Reduced attention heads in {name}: {original_heads} → {new_heads}")


class ModelQuantizer:
    """模型量化器"""
    
    def __init__(self, model):
        self.model = model
        self.original_size = self._calculate_model_size(model)
    
    def dynamic_quantization(self):
        """动态量化"""
        print(f"🔧 Applying dynamic quantization")
        
        # 准备模型
        self.model.eval()
        
        # 动态量化
        quantized_model = torch.quantization.quantize_dynamic(
            self.model,
            {nn.Linear, nn.LSTM, nn.GRU},  # 量化的层类型
            dtype=torch.qint8
        )
        
        quantized_size = self._calculate_model_size(quantized_model)
        compression_ratio = self.original_size / quantized_size
        
        print(f"✅ Dynamic quantization completed")
        print(f"📊 Model size: {self.original_size:.2f}MB → {quantized_size:.2f}MB")
        print(f"📊 Compression ratio: {compression_ratio:.2f}x")
        
        return quantized_model
    
    def static_quantization(self, calibration_data):
        """静态量化"""
        print(f"🔧 Applying static quantization")
        
        # 准备模型
        self.model.eval()
        self.model.qconfig = torch.quantization.get_default_qconfig('fbgemm')
        
        # 准备量化
        prepared_model = torch.quantization.prepare(self.model)
        
        # 校准
        print("📊 Calibrating model...")
        with torch.no_grad():
            for data in calibration_data:
                prepared_model(*data)
        
        # 转换为量化模型
        quantized_model = torch.quantization.convert(prepared_model)
        
        quantized_size = self._calculate_model_size(quantized_model)
        compression_ratio = self.original_size / quantized_size
        
        print(f"✅ Static quantization completed")
        print(f"📊 Model size: {self.original_size:.2f}MB → {quantized_size:.2f}MB")
        print(f"📊 Compression ratio: {compression_ratio:.2f}x")
        
        return quantized_model
    
    def mixed_precision_optimization(self):
        """混合精度优化"""
        print(f"🔧 Applying mixed precision optimization")
        
        # 转换为半精度
        self.model.half()
        
        # 保持某些层为全精度
        for module in self.model.modules():
            if isinstance(module, (nn.LayerNorm, nn.BatchNorm1d)):
                module.float()
        
        half_size = self._calculate_model_size(self.model)
        compression_ratio = self.original_size / half_size
        
        print(f"✅ Mixed precision optimization completed")
        print(f"📊 Model size: {self.original_size:.2f}MB → {half_size:.2f}MB")
        print(f"📊 Compression ratio: {compression_ratio:.2f}x")
        
        return self.model
    
    def _calculate_model_size(self, model):
        """计算模型大小（MB）"""
        param_size = 0
        buffer_size = 0
        
        for param in model.parameters():
            param_size += param.nelement() * param.element_size()
        
        for buffer in model.buffers():
            buffer_size += buffer.nelement() * buffer.element_size()
        
        size_mb = (param_size + buffer_size) / (1024 * 1024)
        return size_mb


class ModelOptimizer:
    """综合模型优化器"""
    
    def __init__(self, model):
        self.original_model = model
        self.optimization_history = []
    
    def optimize_for_inference(self, optimization_level='medium', calibration_data=None):
        """为推理优化模型"""
        print(f"🚀 Optimizing model for inference (level: {optimization_level})")
        
        # 记录原始指标
        original_metrics = self._benchmark_model(self.original_model)
        
        optimized_model = self.original_model
        
        if optimization_level in ['medium', 'aggressive']:
            # 剪枝
            pruner = ModelPruner(optimized_model, pruning_ratio=0.2 if optimization_level == 'medium' else 0.4)
            optimized_model = pruner.unstructured_pruning()
        
        if optimization_level == 'aggressive':
            # 结构化剪枝
            pruner = ModelPruner(optimized_model, pruning_ratio=0.1)
            optimized_model = pruner.structured_pruning()
        
        # 量化
        quantizer = ModelQuantizer(optimized_model)
        if calibration_data:
            optimized_model = quantizer.static_quantization(calibration_data)
        else:
            optimized_model = quantizer.dynamic_quantization()
        
        # 记录优化后指标
        optimized_metrics = self._benchmark_model(optimized_model)
        
        # 计算优化效果
        metrics = OptimizationMetrics(
            original_size=original_metrics['size'],
            compressed_size=optimized_metrics['size'],
            compression_ratio=original_metrics['size'] / optimized_metrics['size'],
            original_inference_time=original_metrics['inference_time'],
            optimized_inference_time=optimized_metrics['inference_time'],
            speedup_ratio=original_metrics['inference_time'] / optimized_metrics['inference_time'],
            accuracy_loss=0.0  # 需要在实际应用中测量
        )
        
        self.optimization_history.append(metrics)
        
        print(f"✅ Model optimization completed!")
        print(f"📊 Size reduction: {metrics.compression_ratio:.2f}x")
        print(f"⚡ Speed improvement: {metrics.speedup_ratio:.2f}x")
        
        return optimized_model, metrics
    
    def _benchmark_model(self, model):
        """基准测试模型"""
        model.eval()
        
        # 计算模型大小
        size_mb = self._calculate_model_size(model)
        
        # 测试推理时间
        dummy_input = self._create_dummy_input()
        
        # 预热
        with torch.no_grad():
            for _ in range(10):
                try:
                    _ = model(*dummy_input)
                except:
                    # 如果模型接口不同，使用简化测试
                    break
        
        # 实际测试
        start_time = time.time()
        with torch.no_grad():
            for _ in range(100):
                try:
                    _ = model(*dummy_input)
                except:
                    break
        end_time = time.time()
        
        inference_time = (end_time - start_time) / 100
        
        return {
            'size': size_mb,
            'inference_time': inference_time
        }
    
    def _calculate_model_size(self, model):
        """计算模型大小"""
        param_size = sum(p.numel() * p.element_size() for p in model.parameters())
        buffer_size = sum(b.numel() * b.element_size() for b in model.buffers())
        return (param_size + buffer_size) / (1024 * 1024)
    
    def _create_dummy_input(self):
        """创建虚拟输入"""
        batch_size = 1
        n_jobs = 3
        n_operations = 10
        n_machines = 4
        
        job_features = torch.randn(batch_size, n_jobs, 10)
        operation_features = torch.randn(batch_size, n_operations, 15)
        machine_features = torch.randn(batch_size, n_machines, 8)
        processing_matrix = torch.rand(batch_size, n_operations, n_machines) * 20
        job_lengths = [3, 3, 4]
        
        return (job_features, operation_features, machine_features, processing_matrix, job_lengths)
    
    def save_optimized_model(self, model, path, metrics):
        """保存优化后的模型"""
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        save_data = {
            'model_state_dict': model.state_dict(),
            'optimization_metrics': metrics,
            'optimization_history': self.optimization_history
        }
        
        torch.save(save_data, path)
        print(f"💾 Optimized model saved to: {path}")


def optimize_trained_model(model_path, output_path, optimization_level='medium'):
    """优化训练好的模型"""
    print(f"🔧 Loading and optimizing trained model")
    print(f"📂 Input: {model_path}")
    print(f"📂 Output: {output_path}")
    
    # 加载原始模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    try:
        # 先尝试加载原始模型架构
        from valid_schedule_neural_model import ValidScheduleNeuralModel
        model = ValidScheduleNeuralModel(d_model=128, n_heads=4, n_layers=3).to(device)

        if os.path.exists(model_path):
            checkpoint = torch.load(model_path, map_location=device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            print(f"✅ Model loaded successfully")
        else:
            print(f"⚠️  Model file not found, using untrained model for testing")
    
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        return None
    
    # 优化模型
    optimizer = ModelOptimizer(model)
    optimized_model, metrics = optimizer.optimize_for_inference(optimization_level)
    
    # 保存优化后的模型
    optimizer.save_optimized_model(optimized_model, output_path, metrics)
    
    return optimized_model, metrics


def test_optimization():
    """测试优化功能"""
    print("🧪 Testing Model Optimization")
    print("=" * 50)
    
    try:
        from valid_schedule_neural_model import ValidScheduleNeuralModel

        # 创建测试模型
        model = ValidScheduleNeuralModel(d_model=64, n_heads=2, n_layers=2)
        print(f"📊 Original model parameters: {sum(p.numel() for p in model.parameters()):,}")
        
        # 测试剪枝
        pruner = ModelPruner(model, pruning_ratio=0.3)
        pruned_model = pruner.unstructured_pruning()
        
        # 测试量化
        quantizer = ModelQuantizer(pruned_model)
        quantized_model = quantizer.dynamic_quantization()
        
        # 综合优化测试
        optimizer = ModelOptimizer(model)
        optimized_model, metrics = optimizer.optimize_for_inference('medium')
        
        print(f"✅ Optimization testing completed!")
        print(f"📊 Final compression ratio: {metrics.compression_ratio:.2f}x")
        print(f"⚡ Final speedup ratio: {metrics.speedup_ratio:.2f}x")
        
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()


def main():
    """主函数"""
    print("🚀 Model Optimization Pipeline")
    print("=" * 50)
    
    # 优化已训练的模型
    model_path = "neural_column_generation/models/final_best_model.pth"
    output_path = "neural_column_generation/models/optimized_model.pth"
    
    optimized_model, metrics = optimize_trained_model(
        model_path, output_path, optimization_level='medium'
    )
    
    if optimized_model:
        print(f"\n🎉 Model optimization successful!")
        print(f"📊 Size reduction: {metrics.compression_ratio:.2f}x")
        print(f"⚡ Speed improvement: {metrics.speedup_ratio:.2f}x")
        print(f"🚀 Ready for real-time branch-and-price integration!")
    else:
        print(f"\n❌ Model optimization failed!")


if __name__ == "__main__":
    main()
