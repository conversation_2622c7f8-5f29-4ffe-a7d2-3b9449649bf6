#!/usr/bin/env python3
"""
分支定价集成接口
创建标准化接口，使神经列生成器可以无缝集成到分支定价算法中
"""

import torch
import numpy as np
import time
from typing import List, Dict, Tuple, Optional, Any
from dataclasses import dataclass
from abc import ABC, abstractmethod
import threading
import queue


@dataclass
class FJSPInstance:
    """FJSP实例数据结构"""
    n_jobs: int
    n_machines: int
    n_operations: int
    job_lengths: List[int]
    processing_times: np.ndarray  # [n_operations, n_machines]
    instance_id: str = ""


@dataclass
class Column:
    """分支定价中的列（调度）"""
    operations: List[Tuple[int, int]]  # [(operation_id, machine_id), ...]
    cost: float  # 目标函数值（makespan）
    reduced_cost: float  # 简化成本
    feasible: bool
    generation_time: float
    source: str  # 'neural' or 'heuristic'


@dataclass
class ColumnGenerationRequest:
    """列生成请求"""
    instance: FJSPInstance
    dual_prices: Optional[np.ndarray] = None  # 对偶价格
    current_solution: Optional[List[Column]] = None  # 当前解
    time_limit: float = 1.0  # 时间限制（秒）
    num_columns: int = 5  # 请求的列数
    quality_threshold: float = 0.0  # 质量阈值


@dataclass
class ColumnGenerationResponse:
    """列生成响应"""
    columns: List[Column]
    generation_time: float
    success: bool
    error_message: str = ""
    statistics: Dict[str, Any] = None


class ColumnGenerator(ABC):
    """列生成器抽象基类"""
    
    @abstractmethod
    def generate_columns(self, request: ColumnGenerationRequest) -> ColumnGenerationResponse:
        """生成列"""
        pass
    
    @abstractmethod
    def initialize(self, **kwargs) -> bool:
        """初始化生成器"""
        pass
    
    @abstractmethod
    def cleanup(self):
        """清理资源"""
        pass


class NeuralColumnGenerator(ColumnGenerator):
    """神经列生成器"""
    
    def __init__(self, model_path: str = None, device: str = 'auto'):
        self.model_path = model_path
        self.device = torch.device('cuda' if device == 'auto' and torch.cuda.is_available() else 'cpu')
        self.model = None
        self.is_initialized = False
        
        # 性能统计
        self.stats = {
            'total_requests': 0,
            'successful_generations': 0,
            'total_columns_generated': 0,
            'average_generation_time': 0.0,
            'neural_decision_ratio': 0.0
        }
    
    def initialize(self, **kwargs) -> bool:
        """初始化神经网络模型"""
        try:
            print(f"🧠 Initializing Neural Column Generator...")
            print(f"📱 Device: {self.device}")
            
            # 加载模型
            if self.model_path and self.model_path.endswith('optimized_model.pth'):
                # 加载优化后的模型
                checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
                
                # 创建模型（需要根据实际保存的模型类型调整）
                from optimized_neural_model import OptimizedNeuralModel
                self.model = OptimizedNeuralModel(d_model=128, n_heads=4, n_layers=3).to(self.device)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                
                print(f"✅ Loaded optimized model from {self.model_path}")
                
            else:
                # 加载标准模型
                from valid_schedule_neural_model import ValidScheduleNeuralModel
                self.model = ValidScheduleNeuralModel(d_model=128, n_heads=4, n_layers=3).to(self.device)
                
                if self.model_path:
                    checkpoint = torch.load(self.model_path, map_location=self.device, weights_only=False)
                    self.model.load_state_dict(checkpoint['model_state_dict'])
                    print(f"✅ Loaded standard model from {self.model_path}")
                else:
                    print(f"⚠️  Using untrained model")
            
            self.model.eval()
            self.is_initialized = True
            
            print(f"📊 Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
            return True
            
        except Exception as e:
            print(f"❌ Failed to initialize neural model: {e}")
            return False
    
    def generate_columns(self, request: ColumnGenerationRequest) -> ColumnGenerationResponse:
        """生成神经列"""
        start_time = time.time()
        self.stats['total_requests'] += 1
        
        if not self.is_initialized:
            return ColumnGenerationResponse(
                columns=[], generation_time=0.0, success=False,
                error_message="Neural generator not initialized"
            )
        
        try:
            # 提取特征
            features = self._extract_features(request.instance)
            
            # 生成列
            with torch.no_grad():
                if hasattr(self.model, 'generate_optimized_columns'):
                    # 优化模型
                    generated_columns = self.model.generate_optimized_columns(
                        features['job_features'].unsqueeze(0).to(self.device),
                        features['operation_features'].unsqueeze(0).to(self.device),
                        features['machine_features'].unsqueeze(0).to(self.device),
                        features['processing_matrix'].unsqueeze(0).to(self.device),
                        features['job_lengths'],
                        num_columns=request.num_columns
                    )
                else:
                    # 标准模型
                    generated_columns = self.model.generate_valid_columns(
                        features['job_features'].unsqueeze(0).to(self.device),
                        features['operation_features'].unsqueeze(0).to(self.device),
                        features['machine_features'].unsqueeze(0).to(self.device),
                        features['processing_matrix'].unsqueeze(0).to(self.device),
                        features['job_lengths'],
                        num_columns=request.num_columns
                    )
            
            # 转换为标准列格式
            columns = []
            neural_decisions = 0
            fallback_decisions = 0
            
            for col in generated_columns:
                if col.feasible and col.makespan < float('inf'):
                    # 计算简化成本
                    reduced_cost = self._calculate_reduced_cost(
                        col.schedule, col.makespan, request.dual_prices
                    )
                    
                    column = Column(
                        operations=col.schedule,
                        cost=col.makespan,
                        reduced_cost=reduced_cost,
                        feasible=True,
                        generation_time=time.time() - start_time,
                        source='neural'
                    )
                    columns.append(column)
                    
                    # 统计神经决策比例
                    if hasattr(col, 'neural_decisions'):
                        neural_decisions += col.neural_decisions
                        fallback_decisions += col.fallback_decisions
            
            generation_time = time.time() - start_time
            
            # 更新统计
            if columns:
                self.stats['successful_generations'] += 1
                self.stats['total_columns_generated'] += len(columns)
                
                if neural_decisions + fallback_decisions > 0:
                    self.stats['neural_decision_ratio'] = neural_decisions / (neural_decisions + fallback_decisions)
            
            self.stats['average_generation_time'] = (
                (self.stats['average_generation_time'] * (self.stats['total_requests'] - 1) + generation_time) /
                self.stats['total_requests']
            )
            
            # 按简化成本排序
            columns.sort(key=lambda x: x.reduced_cost)
            
            return ColumnGenerationResponse(
                columns=columns,
                generation_time=generation_time,
                success=True,
                statistics={
                    'neural_decisions': neural_decisions,
                    'fallback_decisions': fallback_decisions,
                    'neural_ratio': neural_decisions / max(1, neural_decisions + fallback_decisions)
                }
            )
            
        except Exception as e:
            return ColumnGenerationResponse(
                columns=[], generation_time=time.time() - start_time,
                success=False, error_message=str(e)
            )
    
    def _extract_features(self, instance: FJSPInstance):
        """提取FJSP特征"""
        # 这里复用之前的特征提取逻辑
        job_length = instance.job_lengths
        processing_times = instance.processing_times
        n_jobs = instance.n_jobs
        n_machines = instance.n_machines
        n_operations = instance.n_operations
        
        # 作业特征
        job_features = []
        for job_id, n_ops in enumerate(job_length):
            op_start = sum(job_length[:job_id])
            op_end = op_start + n_ops
            
            total_workload = 0
            for op_idx in range(op_start, op_end):
                if op_idx < len(processing_times):
                    min_time = min(t for t in processing_times[op_idx] if t > 0)
                    total_workload += min_time
            
            job_feat = [
                job_id / n_jobs, n_ops / n_operations, total_workload / 1000.0,
                n_ops / max(job_length), total_workload / (n_ops * 20),
                0, 0, 0, 0, 0
            ]
            job_features.append(job_feat)
        
        # 操作特征
        operation_features = []
        for op_id in range(n_operations):
            if op_id < len(processing_times):
                proc_times = processing_times[op_id]
                
                job_id = 0
                op_in_job = op_id
                for j, n_ops in enumerate(job_length):
                    if op_in_job < n_ops:
                        job_id = j
                        break
                    op_in_job -= n_ops
                
                valid_times = [t for t in proc_times if t > 0]
                min_time = min(valid_times) if valid_times else 0
                max_time = max(valid_times) if valid_times else 0
                avg_time = np.mean(valid_times) if valid_times else 0
                n_machines_available = len(valid_times)
                
                op_feat = [
                    op_id / n_operations, job_id / n_jobs, op_in_job / max(job_length),
                    min_time / 50.0, max_time / 50.0, avg_time / 50.0,
                    n_machines_available / n_machines, (max_time - min_time) / 50.0,
                    0, 0, 0, 0, 0, 0, 0
                ]
                operation_features.append(op_feat)
        
        # 机器特征
        machine_features = []
        for machine_id in range(n_machines):
            total_load = 0
            n_operations_available = 0
            
            for op_id in range(n_operations):
                if op_id < len(processing_times) and machine_id < len(processing_times[op_id]):
                    proc_time = processing_times[op_id][machine_id]
                    if proc_time > 0:
                        total_load += proc_time
                        n_operations_available += 1
            
            avg_load = total_load / max(1, n_operations_available)
            
            machine_feat = [
                machine_id / n_machines, total_load / 1000.0,
                n_operations_available / n_operations, avg_load / 50.0,
                0, 0, 0, 0
            ]
            machine_features.append(machine_feat)
        
        return {
            'job_features': torch.FloatTensor(job_features),
            'operation_features': torch.FloatTensor(operation_features),
            'machine_features': torch.FloatTensor(machine_features),
            'processing_matrix': torch.FloatTensor(processing_times),
            'job_lengths': job_length
        }
    
    def _calculate_reduced_cost(self, schedule, makespan, dual_prices):
        """计算简化成本"""
        if dual_prices is None:
            return makespan
        
        # 简化的简化成本计算
        # 在实际应用中，这需要根据具体的分支定价模型来计算
        base_cost = makespan
        dual_adjustment = np.sum(dual_prices) * 0.1  # 简化处理
        
        return base_cost - dual_adjustment
    
    def cleanup(self):
        """清理资源"""
        if self.model is not None:
            del self.model
            torch.cuda.empty_cache()
        self.is_initialized = False
    
    def get_statistics(self):
        """获取统计信息"""
        return self.stats.copy()


class HeuristicColumnGenerator(ColumnGenerator):
    """启发式列生成器（作为备选）"""
    
    def __init__(self):
        self.is_initialized = False
    
    def initialize(self, **kwargs) -> bool:
        """初始化启发式生成器"""
        self.is_initialized = True
        return True
    
    def generate_columns(self, request: ColumnGenerationRequest) -> ColumnGenerationResponse:
        """生成启发式列"""
        start_time = time.time()
        
        try:
            columns = []
            
            # SPT启发式
            spt_schedule = self._generate_spt_schedule(request.instance)
            if spt_schedule:
                makespan = self._evaluate_schedule(spt_schedule, request.instance)
                reduced_cost = self._calculate_reduced_cost(makespan, request.dual_prices)
                
                columns.append(Column(
                    operations=spt_schedule,
                    cost=makespan,
                    reduced_cost=reduced_cost,
                    feasible=makespan < float('inf'),
                    generation_time=time.time() - start_time,
                    source='heuristic_spt'
                ))
            
            # FIFO启发式
            fifo_schedule = self._generate_fifo_schedule(request.instance)
            if fifo_schedule:
                makespan = self._evaluate_schedule(fifo_schedule, request.instance)
                reduced_cost = self._calculate_reduced_cost(makespan, request.dual_prices)
                
                columns.append(Column(
                    operations=fifo_schedule,
                    cost=makespan,
                    reduced_cost=reduced_cost,
                    feasible=makespan < float('inf'),
                    generation_time=time.time() - start_time,
                    source='heuristic_fifo'
                ))
            
            return ColumnGenerationResponse(
                columns=columns,
                generation_time=time.time() - start_time,
                success=True
            )
            
        except Exception as e:
            return ColumnGenerationResponse(
                columns=[], generation_time=time.time() - start_time,
                success=False, error_message=str(e)
            )
    
    def _generate_spt_schedule(self, instance: FJSPInstance):
        """生成SPT调度"""
        operations = list(range(instance.n_operations))
        operations.sort(key=lambda op: min(instance.processing_times[op, m] 
                                         for m in range(instance.n_machines) 
                                         if instance.processing_times[op, m] > 0))
        
        return self._create_feasible_schedule(operations, instance)
    
    def _generate_fifo_schedule(self, instance: FJSPInstance):
        """生成FIFO调度"""
        operations = list(range(instance.n_operations))
        return self._create_feasible_schedule(operations, instance)
    
    def _create_feasible_schedule(self, operation_order, instance: FJSPInstance):
        """创建可行调度"""
        schedule = []
        job_progress = [0] * instance.n_jobs
        scheduled_ops = set()
        
        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(instance.job_lengths):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops
        
        for target_op in operation_order:
            if target_op in scheduled_ops:
                continue
            
            # 找到操作所属作业
            target_job = None
            for job_id, ops in enumerate(job_operations):
                if target_op in ops:
                    target_job = job_id
                    break
            
            if target_job is None:
                continue
            
            # 检查是否可调度
            op_index_in_job = job_operations[target_job].index(target_op)
            if job_progress[target_job] == op_index_in_job:
                valid_machines = [m for m in range(instance.n_machines) 
                                 if instance.processing_times[target_op, m] > 0]
                
                if valid_machines:
                    best_machine = min(valid_machines, 
                                     key=lambda m: instance.processing_times[target_op, m])
                    
                    schedule.append((target_op, best_machine))
                    scheduled_ops.add(target_op)
                    job_progress[target_job] += 1
        
        return schedule
    
    def _evaluate_schedule(self, schedule, instance: FJSPInstance):
        """评估调度makespan"""
        if not schedule:
            return float('inf')
        
        machine_times = [0] * instance.n_machines
        
        for op_id, machine_id in schedule:
            if (machine_id < instance.n_machines and 
                op_id < instance.n_operations and 
                instance.processing_times[op_id, machine_id] > 0):
                machine_times[machine_id] += instance.processing_times[op_id, machine_id]
        
        return max(machine_times) if machine_times else float('inf')
    
    def _calculate_reduced_cost(self, makespan, dual_prices):
        """计算简化成本"""
        if dual_prices is None:
            return makespan
        return makespan - np.sum(dual_prices) * 0.1
    
    def cleanup(self):
        """清理资源"""
        pass


class HybridColumnGenerator(ColumnGenerator):
    """混合列生成器（神经网络 + 启发式）"""
    
    def __init__(self, neural_model_path: str = None, neural_weight: float = 0.7):
        self.neural_generator = NeuralColumnGenerator(neural_model_path)
        self.heuristic_generator = HeuristicColumnGenerator()
        self.neural_weight = neural_weight
        self.is_initialized = False
    
    def initialize(self, **kwargs) -> bool:
        """初始化混合生成器"""
        neural_success = self.neural_generator.initialize(**kwargs)
        heuristic_success = self.heuristic_generator.initialize(**kwargs)
        
        self.is_initialized = neural_success or heuristic_success
        
        if neural_success and heuristic_success:
            print(f"✅ Hybrid generator initialized (Neural + Heuristic)")
        elif neural_success:
            print(f"⚠️  Hybrid generator initialized (Neural only)")
        elif heuristic_success:
            print(f"⚠️  Hybrid generator initialized (Heuristic only)")
        else:
            print(f"❌ Hybrid generator initialization failed")
        
        return self.is_initialized
    
    def generate_columns(self, request: ColumnGenerationRequest) -> ColumnGenerationResponse:
        """生成混合列"""
        start_time = time.time()
        all_columns = []
        
        # 分配列数量
        neural_columns = int(request.num_columns * self.neural_weight)
        heuristic_columns = request.num_columns - neural_columns
        
        # 神经网络生成
        if self.neural_generator.is_initialized and neural_columns > 0:
            neural_request = ColumnGenerationRequest(
                instance=request.instance,
                dual_prices=request.dual_prices,
                current_solution=request.current_solution,
                time_limit=request.time_limit * 0.8,  # 给神经网络更多时间
                num_columns=neural_columns,
                quality_threshold=request.quality_threshold
            )
            
            neural_response = self.neural_generator.generate_columns(neural_request)
            if neural_response.success:
                all_columns.extend(neural_response.columns)
        
        # 启发式生成
        if self.heuristic_generator.is_initialized and heuristic_columns > 0:
            heuristic_request = ColumnGenerationRequest(
                instance=request.instance,
                dual_prices=request.dual_prices,
                current_solution=request.current_solution,
                time_limit=request.time_limit * 0.2,
                num_columns=heuristic_columns,
                quality_threshold=request.quality_threshold
            )
            
            heuristic_response = self.heuristic_generator.generate_columns(heuristic_request)
            if heuristic_response.success:
                all_columns.extend(heuristic_response.columns)
        
        # 去重和排序
        unique_columns = self._remove_duplicates(all_columns)
        unique_columns.sort(key=lambda x: x.reduced_cost)
        
        return ColumnGenerationResponse(
            columns=unique_columns[:request.num_columns],
            generation_time=time.time() - start_time,
            success=len(unique_columns) > 0,
            statistics={
                'neural_columns': len([c for c in unique_columns if c.source == 'neural']),
                'heuristic_columns': len([c for c in unique_columns if c.source.startswith('heuristic')])
            }
        )
    
    def _remove_duplicates(self, columns):
        """移除重复的列"""
        unique_columns = []
        seen_schedules = set()
        
        for column in columns:
            schedule_key = tuple(sorted(column.operations))
            if schedule_key not in seen_schedules:
                seen_schedules.add(schedule_key)
                unique_columns.append(column)
        
        return unique_columns
    
    def cleanup(self):
        """清理资源"""
        self.neural_generator.cleanup()
        self.heuristic_generator.cleanup()
    
    def get_statistics(self):
        """获取统计信息"""
        neural_stats = self.neural_generator.get_statistics()
        return {
            'neural_stats': neural_stats,
            'neural_weight': self.neural_weight
        }


def create_column_generator(generator_type='hybrid', **kwargs):
    """创建列生成器工厂函数"""
    if generator_type == 'neural':
        return NeuralColumnGenerator(**kwargs)
    elif generator_type == 'heuristic':
        return HeuristicColumnGenerator()
    elif generator_type == 'hybrid':
        return HybridColumnGenerator(**kwargs)
    else:
        raise ValueError(f"Unknown generator type: {generator_type}")


def test_integration():
    """测试集成接口"""
    print("🧪 Testing Branch-and-Price Integration")
    print("=" * 50)
    
    # 创建测试实例
    instance = FJSPInstance(
        n_jobs=3,
        n_machines=4,
        n_operations=10,
        job_lengths=[3, 3, 4],
        processing_times=np.random.randint(5, 20, (10, 4)).astype(float),
        instance_id="test_001"
    )
    
    # 创建混合生成器
    generator = create_column_generator(
        'hybrid',
        neural_model_path="neural_column_generation/models/optimized_model.pth"
    )
    
    # 初始化
    if generator.initialize():
        print(f"✅ Generator initialized successfully")
        
        # 创建请求
        request = ColumnGenerationRequest(
            instance=instance,
            dual_prices=np.random.random(10),
            num_columns=5,
            time_limit=2.0
        )
        
        # 生成列
        response = generator.generate_columns(request)
        
        if response.success:
            print(f"✅ Generated {len(response.columns)} columns in {response.generation_time:.3f}s")
            
            for i, column in enumerate(response.columns):
                print(f"  Column {i+1}: cost={column.cost:.1f}, "
                      f"reduced_cost={column.reduced_cost:.1f}, "
                      f"source={column.source}")
            
            if response.statistics:
                print(f"📊 Statistics: {response.statistics}")
        else:
            print(f"❌ Column generation failed: {response.error_message}")
        
        # 清理
        generator.cleanup()
    else:
        print(f"❌ Generator initialization failed")


if __name__ == "__main__":
    test_integration()
