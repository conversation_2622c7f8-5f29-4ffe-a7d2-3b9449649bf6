#!/usr/bin/env python3
"""
高质量训练管道
使用改进的机器选择模型进行训练，确保高质量的调度生成
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader
from tqdm import tqdm
import time
from datetime import datetime

# 添加路径
sys.path.append('..')
sys.path.append('.')

from fixed_machine_selection import ImprovedNeuralModel
from final_training_pipeline import DANIELDataset


class QualityFocusedLoss(nn.Module):
    """质量导向的损失函数"""
    
    def __init__(self, alpha=2.0, beta=1.0, gamma=1.0):
        super().__init__()
        self.alpha = alpha  # makespan损失权重
        self.beta = beta    # 可行性奖励权重
        self.gamma = gamma  # 决策质量权重
    
    def forward(self, predicted_columns, target_makespan):
        """计算质量导向的损失"""
        if not predicted_columns:
            return torch.tensor(100.0, requires_grad=True)
        
        # 确保target_makespan有效
        if not isinstance(target_makespan, (int, float)) or target_makespan <= 0:
            target_makespan = 100.0
        
        total_loss = 0.0
        valid_count = 0
        
        for column in predicted_columns:
            if hasattr(column, 'feasible') and column.feasible:
                makespan = getattr(column, 'makespan', float('inf'))
                decision_quality = getattr(column, 'decision_quality', 0.0)
                
                if makespan != float('inf') and makespan > 0:
                    # Makespan损失
                    makespan_error = abs(makespan - target_makespan) / target_makespan
                    makespan_loss = self.alpha * (makespan_error ** 2)
                    
                    # 可行性奖励
                    feasibility_bonus = self.beta
                    
                    # 决策质量奖励
                    decision_bonus = self.gamma * decision_quality
                    
                    loss = makespan_loss - feasibility_bonus - decision_bonus
                    
                    # 限制损失范围
                    loss = max(-10.0, min(loss, 50.0))
                    
                    total_loss += loss
                    valid_count += 1
                else:
                    total_loss += 50.0
            else:
                total_loss += 100.0
        
        if valid_count == 0:
            return torch.tensor(100.0, requires_grad=True)
        
        final_loss = total_loss / len(predicted_columns)
        
        # 确保损失值有效
        if np.isnan(final_loss) or np.isinf(final_loss):
            final_loss = 50.0
        
        return torch.tensor(float(final_loss), requires_grad=True)


class HighQualityTrainer:
    """高质量训练器"""
    
    def __init__(self, model, device, config=None):
        self.model = model.to(device)
        self.device = device
        
        # 训练配置
        self.config = config or {
            'learning_rate': 0.0005,
            'weight_decay': 0.01,
            'num_epochs': 30,
            'patience': 10,
            'gradient_clip': 1.0
        }
        
        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.config['learning_rate'],
            weight_decay=self.config['weight_decay']
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=self.config['num_epochs'], eta_min=1e-6
        )
        
        # 质量导向损失函数
        self.criterion = QualityFocusedLoss(alpha=2.0, beta=1.0, gamma=1.0)
        
        # 训练历史
        self.train_history = {
            'loss': [],
            'makespan_error': [],
            'feasibility_rate': [],
            'decision_quality': [],
            'learning_rate': []
        }
        
        # 早停
        self.best_loss = float('inf')
        self.patience_counter = 0
        
        print(f"🎯 High Quality Trainer initialized")
        print(f"📱 Device: {self.device}")
        print(f"📊 Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        epoch_loss = 0.0
        epoch_makespan_error = 0.0
        epoch_feasibility = 0.0
        epoch_decision_quality = 0.0
        valid_batches = 0
        
        for batch in tqdm(train_loader, desc="Training"):
            try:
                self.optimizer.zero_grad()
                
                # 准备数据
                features = batch['features'][0]
                target_makespan = batch['target_makespan'][0]
                
                # 数据验证
                if not isinstance(target_makespan, (int, float)) or target_makespan <= 0:
                    continue
                
                # 前向传播
                predicted_columns = self.model.generate_improved_columns(
                    features['job_features'].unsqueeze(0).to(self.device),
                    features['operation_features'].unsqueeze(0).to(self.device),
                    features['machine_features'].unsqueeze(0).to(self.device),
                    features['processing_matrix'].unsqueeze(0).to(self.device),
                    features['job_lengths'],
                    num_columns=3
                )
                
                if not predicted_columns:
                    continue
                
                # 计算损失
                loss = self.criterion(predicted_columns, target_makespan)
                
                # 检查损失值
                if torch.isnan(loss) or torch.isinf(loss) or loss.item() > 200:
                    continue
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    max_norm=self.config['gradient_clip']
                )
                
                self.optimizer.step()
                
                # 统计指标
                epoch_loss += loss.item()
                valid_batches += 1
                
                # 计算其他指标
                valid_makespans = []
                feasible_count = 0
                decision_qualities = []
                
                for col in predicted_columns:
                    if hasattr(col, 'feasible') and col.feasible:
                        feasible_count += 1
                        makespan = getattr(col, 'makespan', float('inf'))
                        if makespan != float('inf') and makespan > 0:
                            valid_makespans.append(makespan)
                        
                        decision_quality = getattr(col, 'decision_quality', 0.0)
                        decision_qualities.append(decision_quality)
                
                # 可行性率
                feasibility_rate = feasible_count / len(predicted_columns)
                epoch_feasibility += feasibility_rate
                
                # makespan误差
                if valid_makespans:
                    best_makespan = min(valid_makespans)
                    makespan_error = abs(best_makespan - target_makespan) / target_makespan
                    epoch_makespan_error += makespan_error
                
                # 决策质量
                if decision_qualities:
                    avg_decision_quality = np.mean(decision_qualities)
                    epoch_decision_quality += avg_decision_quality
                
            except Exception as e:
                print(f"Training error: {e}")
                self.optimizer.zero_grad()
                continue
        
        # 更新学习率
        self.scheduler.step()
        
        # 计算平均指标
        if valid_batches > 0:
            return {
                'loss': epoch_loss / valid_batches,
                'makespan_error': epoch_makespan_error / valid_batches,
                'feasibility_rate': epoch_feasibility / valid_batches,
                'decision_quality': epoch_decision_quality / valid_batches,
                'valid_batches': valid_batches
            }
        else:
            return {
                'loss': 100.0,
                'makespan_error': 1.0,
                'feasibility_rate': 0.0,
                'decision_quality': 0.0,
                'valid_batches': 0
            }
    
    def train(self, train_loader):
        """完整训练过程"""
        print(f"🚀 Starting high-quality training for {self.config['num_epochs']} epochs...")
        
        for epoch in range(self.config['num_epochs']):
            print(f"\n📊 Epoch {epoch + 1}/{self.config['num_epochs']}")
            
            # 训练
            train_stats = self.train_epoch(train_loader)
            
            # 记录历史
            self.train_history['loss'].append(train_stats['loss'])
            self.train_history['makespan_error'].append(train_stats['makespan_error'])
            self.train_history['feasibility_rate'].append(train_stats['feasibility_rate'])
            self.train_history['decision_quality'].append(train_stats['decision_quality'])
            self.train_history['learning_rate'].append(self.optimizer.param_groups[0]['lr'])
            
            # 打印统计
            print(f"  📈 Loss: {train_stats['loss']:.4f}")
            print(f"  📊 Makespan Error: {train_stats['makespan_error']:.4f}")
            print(f"  ✅ Feasibility Rate: {train_stats['feasibility_rate']:.3f}")
            print(f"  🎯 Decision Quality: {train_stats['decision_quality']:.3f}")
            print(f"  📊 Valid Batches: {train_stats['valid_batches']}")
            print(f"  📊 LR: {self.optimizer.param_groups[0]['lr']:.6f}")
            
            # 早停检查
            current_loss = train_stats['loss']
            if current_loss < self.best_loss and train_stats['valid_batches'] > 0:
                self.best_loss = current_loss
                self.patience_counter = 0
                self.save_model("neural_column_generation/models/high_quality_best.pth")
                print(f"  🎯 New best model saved! Loss: {current_loss:.4f}")
            else:
                self.patience_counter += 1
                if self.patience_counter >= self.config['patience']:
                    print(f"  ⏹️  Early stopping triggered!")
                    break
            
            # 检查训练健康状况
            if train_stats['valid_batches'] == 0:
                print(f"  ⚠️  No valid batches in this epoch!")
                if epoch > 3:
                    print(f"  🛑 Stopping training due to persistent failures")
                    break
        
        print(f"✅ High-quality training completed!")
        print(f"📊 Best loss achieved: {self.best_loss:.4f}")
        return self.train_history
    
    def save_model(self, path):
        """保存模型"""
        model_data = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'train_history': self.train_history,
            'config': self.config,
            'best_loss': self.best_loss,
            'timestamp': datetime.now().isoformat()
        }
        
        os.makedirs(os.path.dirname(path), exist_ok=True)
        torch.save(model_data, path)


def main():
    """主函数"""
    print("🎯 High Quality Training Pipeline")
    print("=" * 60)
    
    # 检查数据集
    dataset_path = "neural_column_generation/daniel_data/daniel_consistent_dataset.pkl"
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset not found: {dataset_path}")
        return
    
    # 创建数据集
    dataset = DANIELDataset(dataset_path)
    
    def custom_collate_fn(batch):
        return {
            'features': [item['features'] for item in batch],
            'target_schedule': [item['target_schedule'] for item in batch],
            'target_makespan': [item['target_makespan'] for item in batch],
            'instance_id': [item['instance_id'] for item in batch]
        }
    
    train_loader = DataLoader(
        dataset, batch_size=1, shuffle=True, collate_fn=custom_collate_fn
    )
    
    # 创建改进的模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = ImprovedNeuralModel(d_model=128, n_heads=4, n_layers=3)
    
    print(f"📱 Device: {device}")
    print(f"📊 Training samples: {len(dataset)}")
    
    # 训练配置
    config = {
        'learning_rate': 0.0005,
        'weight_decay': 0.01,
        'num_epochs': 25,
        'patience': 8,
        'gradient_clip': 1.0
    }
    
    # 创建高质量训练器
    trainer = HighQualityTrainer(model, device, config)
    
    # 开始训练
    history = trainer.train(train_loader)
    
    print(f"\n✅ High-quality training completed!")
    print(f"📊 Best loss: {trainer.best_loss:.4f}")
    print(f"📊 Final feasibility rate: {history['feasibility_rate'][-1]:.3f}")
    print(f"📊 Final decision quality: {history['decision_quality'][-1]:.3f}")
    print(f"🎯 Model saved to: neural_column_generation/models/high_quality_best.pth")


if __name__ == "__main__":
    main()
