{"instances": [{"instance_id": "SD1_0000", "job_length": [10, 6, 6, 1, 8, 6, 9, 7, 10, 10], "processing_times": [[0, 4, 4, 0, 0], [0, 0, 10, 0, 7], [0, 0, 0, 0, 11], [12, 0, 11, 11, 11], [0, 0, 2, 2, 2], [8, 6, 0, 8, 0], [4, 6, 6, 4, 5], [0, 2, 0, 0, 2], [11, 0, 0, 0, 9], [2, 0, 0, 0, 0], [11, 10, 8, 11, 9], [16, 16, 17, 13, 11], [13, 13, 16, 14, 12], [0, 0, 4, 0, 0], [0, 0, 16, 0, 0], [0, 18, 13, 0, 12], [1, 0, 1, 0, 1], [20, 18, 20, 17, 17], [0, 0, 1, 0, 0], [13, 15, 18, 17, 18], [0, 5, 6, 0, 6], [1, 1, 1, 1, 1], [0, 0, 16, 20, 17], [0, 0, 16, 0, 0], [0, 5, 0, 0, 0], [0, 9, 0, 0, 0], [1, 1, 1, 1, 1], [18, 16, 0, 0, 0], [16, 0, 14, 13, 14], [8, 0, 12, 0, 8], [0, 14, 0, 0, 0], [20, 17, 17, 14, 17], [14, 0, 12, 0, 0], [18, 0, 0, 0, 0], [0, 10, 6, 7, 9], [10, 8, 8, 0, 9], [13, 0, 0, 0, 0], [16, 15, 15, 12, 12], [2, 2, 0, 0, 3], [2, 0, 0, 2, 0], [1, 1, 0, 1, 1], [0, 19, 0, 16, 0], [13, 9, 11, 0, 13], [20, 18, 0, 0, 0], [2, 2, 0, 2, 0], [7, 0, 0, 0, 7], [3, 2, 2, 2, 2], [0, 3, 3, 2, 0], [16, 0, 19, 19, 17], [0, 10, 13, 12, 11], [12, 0, 0, 12, 0], [17, 0, 16, 0, 15], [11, 13, 11, 12, 12], [2, 2, 2, 0, 2], [0, 0, 0, 1, 1], [0, 0, 0, 8, 0], [3, 0, 0, 0, 0], [0, 13, 0, 14, 17], [14, 0, 14, 19, 19], [0, 19, 0, 17, 0], [3, 4, 4, 5, 3], [0, 18, 0, 0, 0], [0, 6, 6, 6, 0], [15, 15, 15, 14, 14], [2, 0, 2, 2, 0], [0, 0, 0, 5, 0], [11, 11, 9, 10, 11], [16, 20, 17, 17, 16], [0, 14, 0, 0, 0], [0, 0, 4, 4, 2], [7, 0, 0, 0, 0], [0, 0, 0, 4, 2], [0, 0, 0, 17, 17]], "n_jobs": 10, "n_machines": 5, "n_operations": 73, "data_source": "SD1"}, {"instance_id": "SD1_0001", "job_length": [3, 8, 6, 6, 4, 9, 8, 6, 1, 10], "processing_times": [[17, 0, 0, 17, 0], [15, 15, 0, 0, 0], [0, 19, 20, 0, 16], [17, 0, 16, 16, 0], [14, 0, 14, 13, 13], [0, 4, 0, 4, 0], [7, 8, 11, 11, 9], [13, 13, 10, 12, 9], [18, 0, 14, 0, 16], [0, 0, 19, 0, 20], [0, 0, 15, 15, 16], [4, 5, 3, 3, 4], [0, 15, 0, 0, 0], [1, 0, 0, 0, 0], [3, 3, 4, 3, 4], [0, 0, 2, 2, 0], [0, 0, 0, 19, 0], [13, 12, 12, 14, 13], [0, 0, 0, 0, 4], [11, 15, 12, 11, 14], [8, 10, 10, 11, 11], [0, 15, 0, 18, 0], [1, 1, 1, 1, 1], [0, 6, 0, 0, 0], [0, 0, 0, 0, 16], [0, 0, 0, 4, 0], [0, 0, 1, 0, 0], [0, 0, 12, 0, 14], [0, 0, 17, 0, 0], [17, 19, 19, 16, 17], [16, 16, 10, 11, 14], [19, 0, 0, 18, 0], [0, 9, 0, 0, 9], [14, 15, 19, 14, 20], [12, 17, 13, 14, 12], [19, 14, 16, 15, 0], [20, 20, 19, 19, 20], [7, 8, 7, 8, 6], [6, 4, 5, 6, 6], [0, 0, 13, 0, 0], [5, 0, 0, 3, 5], [0, 0, 16, 16, 16], [17, 18, 0, 16, 16], [0, 0, 0, 11, 0], [0, 6, 7, 6, 6], [0, 11, 0, 13, 0], [9, 9, 9, 9, 11], [1, 1, 1, 1, 1], [0, 0, 10, 0, 11], [4, 4, 2, 2, 2], [11, 11, 13, 14, 16], [15, 11, 12, 10, 10], [13, 11, 13, 9, 0], [19, 18, 17, 20, 19], [7, 5, 6, 7, 7], [0, 12, 0, 0, 14], [16, 10, 11, 13, 16], [6, 5, 4, 5, 0], [2, 2, 2, 2, 2], [0, 0, 9, 0, 0], [10, 11, 9, 0, 10]], "n_jobs": 10, "n_machines": 5, "n_operations": 61, "data_source": "SD1"}, {"instance_id": "SD1_0002", "job_length": [1, 5, 3, 9, 1, 2, 2, 3, 1, 1], "processing_times": [[1, 1, 1, 1, 1], [11, 14, 12, 12, 11], [20, 0, 0, 0, 19], [17, 11, 14, 13, 12], [0, 16, 0, 0, 0], [7, 7, 8, 6, 9], [10, 12, 11, 10, 12], [0, 18, 0, 12, 14], [3, 0, 4, 4, 0], [13, 10, 10, 12, 10], [4, 0, 0, 6, 6], [20, 15, 17, 20, 0], [5, 4, 4, 5, 4], [0, 12, 13, 0, 10], [6, 5, 4, 0, 6], [0, 4, 0, 0, 0], [0, 17, 0, 0, 0], [0, 5, 3, 0, 0], [18, 17, 15, 20, 19], [12, 10, 0, 12, 10], [16, 0, 16, 19, 19], [7, 7, 7, 6, 7], [0, 9, 0, 9, 0], [0, 0, 0, 5, 6], [0, 0, 0, 18, 0], [16, 17, 15, 0, 18], [4, 3, 2, 0, 0], [0, 0, 0, 14, 13]], "n_jobs": 10, "n_machines": 5, "n_operations": 28, "data_source": "SD1"}, {"instance_id": "SD1_0003", "job_length": [2, 10, 3, 7, 5, 3, 8, 8, 5, 2], "processing_times": [[0, 10, 0, 0, 0], [0, 0, 0, 14, 0], [8, 6, 8, 7, 8], [16, 18, 19, 19, 18], [13, 0, 12, 11, 0], [0, 0, 0, 0, 15], [0, 0, 1, 1, 0], [13, 0, 19, 16, 0], [0, 14, 0, 0, 0], [0, 0, 0, 0, 8], [0, 11, 8, 0, 11], [4, 3, 0, 2, 3], [4, 5, 4, 3, 5], [16, 15, 0, 0, 0], [2, 0, 0, 2, 2], [17, 14, 14, 11, 17], [16, 13, 0, 16, 13], [0, 0, 6, 0, 7], [0, 0, 16, 18, 0], [0, 6, 6, 0, 0], [0, 0, 0, 11, 0], [0, 0, 0, 20, 0], [18, 20, 16, 18, 17], [0, 0, 5, 0, 5], [0, 0, 4, 0, 0], [19, 0, 15, 16, 16], [0, 0, 5, 0, 6], [0, 0, 18, 0, 0], [11, 0, 9, 11, 13], [12, 8, 0, 11, 10], [0, 12, 13, 14, 0], [5, 6, 6, 7, 7], [5, 4, 4, 6, 6], [10, 8, 8, 0, 10], [0, 0, 0, 1, 0], [0, 0, 0, 13, 0], [2, 0, 0, 2, 0], [0, 11, 12, 0, 0], [3, 0, 5, 0, 0], [0, 7, 8, 7, 0], [0, 16, 14, 16, 0], [0, 0, 3, 3, 0], [0, 0, 0, 0, 1], [0, 0, 11, 13, 10], [0, 6, 0, 0, 0], [0, 2, 3, 0, 2], [1, 0, 1, 0, 0], [18, 17, 18, 14, 17], [0, 0, 0, 0, 2], [0, 15, 13, 16, 15], [0, 0, 0, 18, 14], [14, 12, 12, 17, 16], [0, 0, 0, 20, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 53, "data_source": "SD1"}, {"instance_id": "SD1_0004", "job_length": [5, 1, 7, 9, 5, 8, 4, 7, 1, 5], "processing_times": [[14, 0, 0, 16, 13], [5, 0, 0, 3, 3], [14, 0, 0, 15, 0], [0, 7, 0, 7, 0], [0, 0, 0, 19, 16], [0, 14, 0, 0, 0], [16, 17, 14, 0, 0], [20, 0, 18, 0, 16], [16, 17, 0, 18, 14], [0, 0, 14, 11, 0], [15, 12, 0, 0, 0], [2, 0, 0, 4, 0], [0, 11, 9, 9, 0], [0, 0, 0, 0, 11], [14, 12, 13, 14, 0], [14, 18, 0, 0, 0], [16, 0, 0, 0, 0], [14, 15, 0, 0, 16], [12, 0, 0, 0, 0], [16, 0, 0, 0, 0], [10, 0, 10, 0, 0], [10, 0, 0, 0, 0], [7, 5, 5, 6, 5], [0, 2, 2, 2, 2], [0, 10, 9, 0, 0], [0, 0, 13, 0, 0], [0, 16, 19, 0, 0], [9, 0, 11, 0, 0], [14, 14, 0, 14, 17], [0, 0, 12, 8, 8], [17, 0, 0, 0, 0], [0, 20, 0, 0, 0], [0, 0, 0, 15, 0], [12, 11, 11, 13, 10], [0, 0, 0, 14, 0], [0, 0, 11, 0, 0], [8, 0, 0, 7, 0], [15, 0, 0, 0, 0], [0, 14, 13, 0, 15], [0, 11, 13, 0, 14], [3, 0, 3, 3, 4], [0, 15, 0, 19, 17], [9, 9, 12, 0, 0], [9, 0, 9, 13, 9], [0, 0, 19, 0, 0], [0, 0, 0, 2, 0], [8, 0, 6, 0, 0], [0, 16, 18, 19, 0], [0, 0, 0, 10, 0], [11, 11, 10, 0, 12], [3, 0, 0, 4, 3], [1, 1, 1, 1, 1]], "n_jobs": 10, "n_machines": 5, "n_operations": 52, "data_source": "SD1"}, {"instance_id": "SD1_0005", "job_length": [10, 6, 10, 6, 5, 8, 2, 10, 3, 2], "processing_times": [[0, 0, 6, 0, 0], [0, 0, 0, 0, 12], [6, 0, 0, 7, 6], [2, 2, 0, 0, 3], [17, 0, 16, 17, 15], [15, 10, 14, 15, 11], [1, 1, 1, 1, 1], [0, 0, 11, 7, 7], [0, 0, 5, 7, 0], [17, 16, 0, 20, 20], [18, 19, 19, 0, 0], [0, 17, 12, 17, 18], [9, 10, 11, 10, 11], [15, 16, 16, 18, 17], [8, 6, 0, 0, 0], [0, 0, 0, 11, 0], [0, 0, 17, 0, 0], [0, 7, 8, 0, 8], [0, 10, 0, 0, 0], [3, 4, 0, 3, 4], [10, 0, 0, 0, 7], [0, 0, 15, 0, 0], [6, 0, 0, 0, 6], [14, 15, 14, 20, 15], [0, 0, 0, 14, 13], [17, 0, 0, 16, 0], [0, 1, 0, 0, 1], [0, 20, 16, 19, 0], [0, 14, 19, 17, 0], [16, 0, 0, 16, 18], [6, 10, 0, 0, 7], [0, 15, 0, 12, 0], [13, 17, 0, 0, 0], [18, 15, 15, 18, 0], [20, 17, 16, 16, 17], [0, 11, 8, 12, 0], [7, 7, 8, 7, 6], [0, 0, 11, 13, 13], [2, 0, 0, 0, 0], [15, 13, 14, 18, 13], [0, 8, 0, 0, 0], [0, 0, 0, 20, 17], [0, 7, 0, 5, 7], [6, 0, 8, 8, 0], [6, 0, 0, 0, 0], [2, 0, 4, 0, 2], [14, 15, 15, 12, 0], [12, 0, 0, 11, 8], [0, 10, 10, 0, 0], [0, 20, 17, 0, 0], [9, 7, 8, 6, 6], [2, 0, 0, 2, 0], [18, 18, 19, 16, 14], [16, 18, 15, 12, 12], [0, 16, 16, 20, 0], [9, 11, 13, 12, 9], [0, 1, 1, 1, 0], [0, 0, 0, 3, 2], [3, 3, 3, 5, 5], [0, 5, 0, 0, 0], [0, 0, 16, 0, 0], [14, 0, 11, 0, 16]], "n_jobs": 10, "n_machines": 5, "n_operations": 62, "data_source": "SD1"}, {"instance_id": "SD1_0006", "job_length": [7, 2, 8, 4, 8, 4, 5, 10, 5, 9], "processing_times": [[19, 18, 18, 14, 14], [10, 11, 11, 0, 9], [0, 0, 0, 0, 15], [0, 0, 18, 20, 16], [17, 0, 0, 0, 17], [0, 0, 0, 0, 5], [16, 18, 18, 19, 16], [1, 0, 0, 0, 1], [2, 2, 2, 2, 2], [0, 18, 17, 18, 17], [0, 8, 0, 8, 7], [0, 0, 6, 5, 6], [12, 12, 0, 15, 11], [0, 0, 0, 10, 0], [0, 3, 4, 3, 0], [5, 4, 0, 5, 6], [17, 16, 0, 12, 14], [14, 0, 13, 11, 14], [7, 5, 6, 0, 5], [0, 7, 0, 9, 0], [4, 3, 2, 4, 3], [0, 15, 0, 0, 0], [0, 0, 0, 5, 3], [6, 0, 0, 0, 0], [1, 0, 1, 0, 1], [0, 17, 0, 18, 0], [11, 12, 9, 13, 11], [5, 5, 4, 0, 0], [0, 0, 3, 0, 4], [10, 9, 9, 0, 9], [10, 0, 0, 0, 0], [8, 0, 0, 0, 0], [0, 18, 17, 20, 0], [0, 7, 6, 5, 5], [2, 2, 2, 2, 2], [7, 0, 5, 0, 0], [5, 6, 4, 5, 4], [2, 2, 4, 4, 2], [0, 5, 0, 4, 6], [10, 10, 8, 0, 9], [14, 12, 0, 0, 0], [0, 14, 0, 0, 0], [0, 7, 0, 0, 0], [0, 2, 4, 2, 3], [0, 18, 17, 20, 18], [0, 13, 12, 0, 16], [0, 0, 0, 9, 0], [0, 20, 0, 0, 0], [7, 10, 8, 10, 11], [19, 15, 0, 0, 0], [11, 0, 10, 10, 9], [0, 4, 0, 0, 0], [7, 7, 6, 7, 6], [0, 15, 0, 0, 18], [4, 0, 6, 5, 6], [15, 19, 18, 0, 0], [0, 11, 0, 10, 8], [0, 12, 0, 10, 0], [19, 14, 20, 0, 20], [20, 17, 0, 16, 17], [0, 0, 20, 17, 0], [0, 16, 18, 16, 17]], "n_jobs": 10, "n_machines": 5, "n_operations": 62, "data_source": "SD1"}, {"instance_id": "SD1_0007", "job_length": [5, 8, 8, 10, 4, 3, 3, 1, 2, 6], "processing_times": [[0, 0, 0, 2, 2], [10, 6, 9, 10, 0], [10, 8, 6, 9, 8], [0, 5, 0, 0, 0], [4, 6, 0, 4, 0], [0, 0, 16, 0, 0], [11, 10, 11, 0, 11], [7, 8, 8, 7, 8], [0, 0, 10, 12, 0], [15, 0, 17, 14, 14], [0, 0, 9, 0, 0], [0, 0, 12, 0, 0], [1, 1, 1, 1, 1], [0, 12, 0, 0, 0], [0, 0, 9, 0, 11], [0, 0, 0, 0, 8], [0, 2, 0, 4, 4], [4, 0, 0, 0, 0], [17, 13, 17, 12, 12], [12, 9, 10, 10, 0], [14, 0, 0, 0, 0], [20, 0, 0, 0, 0], [0, 9, 0, 0, 0], [0, 2, 0, 2, 0], [0, 0, 0, 0, 18], [0, 18, 16, 0, 15], [0, 6, 0, 0, 7], [14, 14, 12, 11, 16], [0, 0, 18, 0, 0], [14, 0, 17, 0, 0], [0, 0, 0, 7, 8], [3, 4, 4, 3, 4], [0, 18, 0, 19, 0], [1, 0, 0, 0, 0], [0, 10, 0, 0, 0], [0, 14, 14, 10, 13], [0, 6, 0, 0, 0], [0, 0, 0, 0, 6], [0, 3, 4, 0, 0], [19, 0, 19, 16, 0], [0, 0, 0, 0, 13], [12, 16, 0, 17, 13], [0, 0, 17, 0, 0], [0, 2, 0, 0, 0], [8, 8, 11, 10, 0], [0, 0, 0, 6, 5], [7, 8, 11, 8, 11], [19, 14, 15, 0, 0], [0, 0, 0, 8, 10], [3, 5, 4, 3, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 50, "data_source": "SD1"}, {"instance_id": "SD1_0008", "job_length": [1, 7, 9, 2, 9, 3, 2, 6, 8, 9], "processing_times": [[0, 0, 0, 8, 0], [0, 0, 0, 14, 14], [1, 1, 1, 0, 0], [2, 3, 3, 3, 4], [0, 0, 0, 0, 12], [9, 7, 6, 9, 0], [0, 17, 15, 19, 15], [0, 15, 0, 0, 0], [0, 0, 8, 0, 0], [2, 2, 0, 0, 2], [9, 12, 0, 9, 0], [0, 0, 0, 7, 0], [20, 0, 0, 0, 0], [0, 0, 17, 15, 19], [6, 10, 0, 0, 9], [9, 7, 9, 7, 8], [0, 0, 4, 2, 3], [14, 19, 20, 0, 15], [12, 0, 16, 13, 0], [0, 15, 17, 14, 14], [16, 12, 16, 17, 0], [14, 19, 0, 0, 0], [6, 0, 7, 7, 6], [17, 17, 16, 13, 16], [5, 0, 5, 5, 5], [0, 14, 17, 13, 0], [13, 11, 14, 11, 11], [0, 0, 0, 0, 1], [0, 2, 2, 0, 0], [14, 0, 0, 0, 0], [0, 16, 17, 18, 18], [10, 0, 13, 10, 0], [0, 18, 20, 0, 0], [13, 0, 0, 14, 13], [1, 0, 0, 1, 1], [14, 0, 0, 14, 10], [16, 0, 14, 16, 16], [20, 19, 17, 0, 14], [5, 5, 6, 5, 6], [4, 6, 6, 6, 5], [17, 11, 17, 0, 16], [6, 4, 0, 0, 6], [0, 0, 0, 6, 0], [0, 9, 11, 10, 12], [0, 0, 8, 0, 10], [0, 0, 11, 0, 9], [20, 16, 19, 20, 20], [0, 8, 0, 7, 0], [1, 0, 1, 0, 1], [3, 2, 0, 2, 2], [15, 16, 0, 0, 20], [0, 0, 18, 0, 18], [18, 18, 17, 0, 16], [0, 0, 0, 0, 10], [2, 0, 2, 2, 2], [17, 0, 20, 0, 18]], "n_jobs": 10, "n_machines": 5, "n_operations": 56, "data_source": "SD1"}, {"instance_id": "SD1_0009", "job_length": [1, 3, 1, 1, 4, 10, 6, 9, 5, 5], "processing_times": [[17, 0, 18, 20, 0], [0, 16, 19, 0, 16], [0, 16, 0, 0, 0], [0, 0, 12, 12, 0], [10, 10, 0, 7, 0], [10, 13, 10, 15, 10], [8, 0, 0, 0, 0], [11, 7, 7, 9, 9], [13, 15, 0, 0, 16], [6, 0, 0, 9, 0], [0, 0, 15, 0, 0], [19, 0, 0, 0, 0], [0, 0, 0, 0, 2], [0, 0, 1, 1, 0], [0, 0, 0, 16, 0], [5, 4, 0, 0, 0], [20, 17, 16, 17, 14], [0, 1, 0, 0, 1], [0, 0, 0, 0, 13], [0, 0, 0, 8, 0], [20, 17, 0, 0, 0], [0, 2, 2, 2, 2], [0, 0, 11, 9, 8], [11, 9, 8, 9, 8], [16, 14, 19, 19, 16], [1, 0, 0, 1, 1], [6, 0, 8, 0, 6], [0, 7, 0, 0, 0], [14, 14, 0, 0, 13], [16, 0, 0, 0, 18], [0, 16, 0, 0, 0], [4, 0, 4, 3, 0], [0, 2, 2, 2, 0], [7, 7, 6, 7, 6], [0, 0, 0, 13, 16], [0, 17, 16, 0, 19], [10, 8, 0, 0, 0], [0, 3, 3, 3, 4], [0, 0, 15, 0, 0], [7, 8, 0, 0, 0], [0, 0, 0, 13, 0], [0, 19, 0, 0, 0], [6, 5, 0, 5, 7], [10, 10, 12, 10, 9], [14, 14, 16, 15, 18]], "n_jobs": 10, "n_machines": 5, "n_operations": 45, "data_source": "SD1"}, {"instance_id": "SD1_0010", "job_length": [4, 9, 8, 3, 1, 6, 5, 9, 10, 9], "processing_times": [[13, 0, 16, 0, 14], [7, 0, 11, 9, 7], [16, 17, 19, 16, 0], [11, 12, 15, 0, 14], [14, 16, 17, 19, 13], [13, 10, 10, 0, 0], [2, 0, 0, 2, 0], [4, 3, 3, 0, 0], [19, 0, 0, 14, 0], [0, 13, 11, 14, 0], [2, 4, 2, 0, 4], [2, 2, 2, 2, 2], [0, 0, 0, 18, 16], [0, 12, 12, 12, 11], [0, 9, 7, 7, 10], [0, 12, 0, 0, 0], [0, 0, 7, 8, 7], [0, 2, 0, 2, 0], [0, 0, 1, 0, 0], [14, 13, 12, 11, 0], [0, 0, 0, 6, 0], [0, 13, 9, 0, 13], [0, 0, 0, 0, 7], [9, 0, 0, 10, 13], [0, 10, 0, 0, 0], [0, 14, 15, 17, 17], [18, 0, 18, 0, 16], [12, 0, 0, 13, 0], [16, 0, 0, 15, 15], [0, 0, 17, 17, 0], [7, 6, 0, 7, 7], [2, 2, 0, 0, 2], [12, 15, 18, 18, 17], [0, 0, 6, 0, 0], [2, 4, 2, 4, 3], [0, 7, 0, 0, 0], [19, 17, 19, 19, 0], [10, 6, 0, 6, 6], [0, 0, 0, 17, 0], [0, 0, 7, 0, 0], [0, 15, 15, 16, 19], [13, 0, 0, 0, 0], [0, 0, 5, 5, 5], [6, 7, 8, 8, 7], [13, 13, 0, 17, 11], [0, 14, 18, 0, 16], [5, 0, 5, 0, 0], [17, 18, 16, 20, 18], [19, 19, 0, 16, 0], [0, 11, 0, 11, 12], [0, 5, 0, 0, 0], [7, 11, 10, 7, 0], [0, 8, 0, 6, 0], [16, 0, 16, 20, 20], [0, 8, 0, 0, 0], [11, 11, 11, 15, 12], [0, 0, 7, 5, 5], [15, 0, 18, 0, 15], [12, 12, 12, 9, 9], [14, 15, 10, 0, 0], [4, 0, 3, 3, 0], [14, 11, 16, 14, 11], [4, 4, 5, 5, 6], [0, 6, 0, 0, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 64, "data_source": "SD1"}, {"instance_id": "SD1_0011", "job_length": [9, 9, 6, 7, 10, 6, 2, 7, 9, 5], "processing_times": [[0, 1, 1, 1, 1], [0, 16, 0, 19, 16], [4, 4, 4, 4, 4], [19, 20, 16, 20, 17], [17, 20, 16, 19, 19], [0, 13, 0, 0, 0], [11, 0, 0, 8, 11], [0, 19, 0, 0, 20], [0, 0, 0, 18, 0], [8, 8, 6, 8, 8], [11, 0, 0, 15, 0], [0, 0, 0, 15, 17], [0, 0, 0, 8, 8], [17, 15, 14, 14, 19], [4, 0, 0, 4, 0], [14, 17, 14, 18, 0], [17, 0, 0, 13, 0], [0, 0, 10, 12, 15], [0, 0, 0, 6, 6], [5, 0, 0, 0, 4], [7, 0, 0, 6, 0], [0, 13, 13, 0, 11], [5, 6, 0, 6, 4], [0, 0, 0, 0, 16], [2, 2, 2, 2, 2], [0, 1, 1, 0, 1], [0, 11, 0, 0, 0], [0, 9, 0, 0, 0], [1, 0, 0, 1, 1], [0, 0, 0, 0, 13], [1, 1, 1, 1, 1], [0, 4, 4, 0, 0], [0, 0, 0, 0, 20], [13, 0, 15, 17, 0], [0, 16, 0, 0, 0], [6, 7, 0, 0, 5], [14, 19, 0, 15, 0], [0, 6, 0, 0, 6], [0, 0, 10, 0, 8], [15, 17, 13, 0, 0], [0, 20, 18, 0, 0], [16, 18, 0, 17, 20], [2, 2, 2, 0, 2], [11, 0, 13, 17, 0], [19, 15, 17, 18, 15], [6, 0, 0, 0, 0], [0, 0, 0, 0, 17], [2, 0, 2, 0, 2], [8, 7, 8, 6, 7], [6, 6, 6, 0, 4], [11, 12, 13, 0, 0], [0, 0, 17, 20, 19], [0, 12, 0, 0, 0], [0, 11, 0, 14, 0], [12, 11, 12, 9, 12], [17, 0, 0, 0, 15], [17, 12, 16, 13, 17], [15, 16, 16, 0, 20], [7, 0, 7, 6, 8], [0, 15, 11, 0, 0], [9, 8, 0, 9, 8], [14, 18, 17, 0, 17], [6, 10, 0, 0, 9], [9, 8, 8, 9, 9], [0, 6, 6, 0, 0], [1, 1, 1, 1, 1], [10, 0, 0, 12, 0], [10, 8, 0, 10, 10], [17, 15, 16, 16, 17], [11, 0, 0, 13, 10]], "n_jobs": 10, "n_machines": 5, "n_operations": 70, "data_source": "SD1"}, {"instance_id": "SD1_0012", "job_length": [10, 6, 3, 3, 9, 10, 6, 2, 1, 7], "processing_times": [[0, 6, 6, 0, 0], [2, 2, 2, 2, 0], [0, 9, 0, 0, 0], [11, 12, 12, 10, 15], [0, 0, 0, 10, 0], [0, 0, 8, 0, 10], [8, 0, 11, 9, 10], [20, 18, 19, 19, 15], [1, 1, 0, 1, 0], [16, 15, 17, 14, 15], [6, 0, 9, 6, 7], [19, 0, 0, 0, 0], [14, 13, 12, 14, 10], [4, 0, 4, 4, 0], [2, 0, 2, 0, 0], [10, 9, 0, 0, 7], [2, 2, 2, 4, 3], [0, 0, 0, 17, 0], [0, 0, 0, 0, 6], [14, 13, 0, 0, 13], [0, 0, 0, 11, 8], [0, 6, 0, 6, 5], [0, 0, 19, 0, 0], [0, 1, 0, 0, 1], [15, 16, 13, 16, 13], [14, 0, 12, 0, 0], [0, 0, 0, 20, 15], [14, 13, 0, 12, 16], [19, 19, 16, 19, 19], [11, 7, 9, 10, 9], [20, 18, 0, 0, 0], [2, 0, 2, 2, 2], [12, 11, 10, 12, 9], [15, 19, 19, 18, 19], [7, 6, 7, 0, 7], [10, 0, 8, 7, 11], [10, 10, 0, 0, 8], [0, 11, 0, 0, 0], [3, 5, 4, 3, 5], [0, 18, 0, 0, 0], [0, 0, 0, 0, 18], [0, 14, 10, 11, 12], [11, 13, 0, 0, 11], [8, 11, 7, 11, 9], [1, 1, 1, 1, 0], [18, 18, 13, 16, 18], [11, 14, 17, 17, 17], [0, 0, 16, 0, 15], [6, 5, 5, 6, 5], [17, 14, 14, 17, 19], [0, 18, 0, 0, 19], [0, 12, 9, 9, 12], [18, 19, 19, 18, 0], [12, 10, 11, 11, 12], [10, 8, 6, 0, 9], [18, 0, 0, 0, 0], [0, 0, 0, 0, 3]], "n_jobs": 10, "n_machines": 5, "n_operations": 57, "data_source": "SD1"}, {"instance_id": "SD1_0013", "job_length": [1, 2, 5, 1, 10, 10, 8, 5, 6, 8], "processing_times": [[10, 7, 10, 9, 0], [0, 1, 1, 1, 0], [1, 1, 0, 1, 0], [11, 13, 12, 12, 14], [2, 0, 0, 2, 2], [6, 7, 10, 8, 9], [5, 5, 3, 0, 4], [0, 18, 15, 0, 17], [0, 10, 0, 0, 0], [6, 8, 0, 6, 0], [0, 10, 0, 0, 11], [0, 0, 0, 0, 5], [17, 0, 0, 11, 16], [2, 2, 2, 2, 2], [8, 0, 0, 9, 0], [13, 0, 14, 17, 15], [0, 0, 0, 11, 0], [12, 0, 12, 0, 9], [13, 13, 0, 13, 13], [8, 7, 9, 9, 6], [1, 1, 0, 1, 0], [3, 2, 3, 2, 4], [0, 0, 0, 5, 0], [3, 5, 0, 4, 0], [0, 0, 13, 0, 14], [0, 13, 17, 13, 16], [13, 0, 0, 17, 12], [0, 0, 0, 19, 0], [0, 0, 0, 0, 12], [11, 12, 17, 17, 16], [17, 16, 15, 0, 17], [16, 13, 19, 16, 18], [9, 8, 11, 10, 0], [18, 17, 18, 0, 0], [3, 0, 3, 3, 2], [2, 2, 2, 0, 0], [17, 0, 0, 19, 0], [6, 0, 7, 8, 6], [18, 0, 0, 0, 0], [10, 8, 8, 7, 0], [9, 11, 10, 11, 8], [0, 10, 10, 0, 0], [0, 18, 0, 0, 0], [14, 11, 0, 11, 0], [13, 16, 13, 11, 0], [16, 0, 0, 19, 0], [10, 10, 9, 0, 0], [9, 7, 8, 10, 9], [0, 19, 0, 19, 17], [17, 18, 16, 16, 18], [0, 0, 0, 16, 0], [2, 2, 2, 2, 2], [5, 3, 5, 5, 0], [0, 0, 5, 0, 0], [16, 12, 16, 12, 18], [0, 13, 0, 0, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 56, "data_source": "SD1"}, {"instance_id": "SD1_0014", "job_length": [10, 5, 4, 8, 8, 4, 10, 5, 9, 1], "processing_times": [[12, 0, 15, 11, 13], [10, 10, 0, 0, 11], [5, 6, 5, 6, 7], [3, 3, 5, 4, 3], [0, 3, 5, 0, 3], [4, 4, 3, 0, 0], [0, 4, 3, 5, 5], [20, 19, 15, 18, 18], [0, 0, 0, 0, 16], [0, 9, 9, 0, 0], [0, 9, 0, 0, 9], [0, 0, 6, 9, 10], [0, 0, 0, 0, 16], [12, 13, 0, 11, 13], [0, 16, 15, 13, 0], [0, 10, 13, 0, 12], [6, 7, 5, 5, 0], [0, 0, 2, 2, 2], [6, 5, 0, 6, 6], [18, 0, 0, 0, 0], [0, 5, 7, 5, 5], [18, 15, 17, 0, 16], [1, 1, 0, 1, 1], [17, 15, 0, 19, 0], [15, 19, 15, 19, 18], [8, 9, 10, 10, 0], [11, 7, 8, 0, 9], [14, 18, 19, 19, 14], [4, 0, 0, 5, 0], [10, 0, 0, 0, 0], [0, 0, 0, 0, 20], [9, 0, 9, 0, 0], [7, 0, 0, 6, 6], [20, 19, 16, 17, 16], [2, 0, 0, 0, 0], [9, 11, 0, 9, 0], [17, 16, 12, 16, 12], [5, 6, 6, 0, 6], [12, 0, 0, 18, 0], [6, 0, 0, 5, 0], [4, 0, 0, 0, 0], [0, 0, 12, 0, 13], [0, 0, 9, 10, 0], [0, 0, 13, 0, 0], [12, 11, 9, 8, 8], [1, 0, 0, 1, 1], [5, 0, 0, 0, 5], [16, 15, 0, 12, 0], [15, 14, 13, 16, 11], [10, 8, 10, 11, 11], [0, 0, 0, 10, 0], [11, 8, 8, 0, 8], [0, 0, 0, 2, 3], [9, 10, 10, 10, 0], [4, 5, 0, 3, 0], [6, 5, 5, 7, 5], [0, 5, 0, 0, 0], [0, 0, 0, 0, 10], [12, 14, 0, 11, 0], [0, 18, 0, 16, 16], [0, 0, 11, 11, 17], [0, 0, 9, 9, 12], [0, 5, 6, 5, 0], [0, 0, 1, 0, 1]], "n_jobs": 10, "n_machines": 5, "n_operations": 64, "data_source": "SD1"}, {"instance_id": "SD1_0015", "job_length": [7, 3, 5, 1, 6, 5, 2, 7, 5, 2], "processing_times": [[13, 12, 0, 13, 15], [9, 8, 6, 10, 7], [0, 0, 16, 17, 17], [0, 3, 0, 3, 0], [0, 0, 14, 0, 0], [15, 0, 16, 13, 16], [14, 15, 16, 15, 0], [0, 0, 14, 19, 13], [8, 9, 0, 11, 10], [18, 0, 18, 19, 0], [11, 12, 0, 15, 0], [0, 7, 8, 0, 0], [20, 18, 14, 19, 17], [0, 0, 16, 15, 19], [0, 7, 0, 8, 6], [4, 2, 3, 2, 0], [0, 0, 15, 16, 0], [0, 11, 11, 10, 9], [16, 0, 0, 0, 19], [5, 7, 7, 6, 0], [17, 18, 17, 19, 0], [0, 10, 14, 0, 0], [0, 8, 8, 8, 0], [2, 0, 0, 2, 0], [5, 5, 5, 0, 6], [0, 0, 0, 20, 0], [0, 5, 0, 5, 0], [13, 12, 0, 0, 0], [2, 0, 0, 2, 0], [0, 14, 0, 0, 0], [7, 6, 5, 7, 6], [0, 17, 0, 0, 16], [0, 0, 7, 6, 0], [0, 7, 7, 6, 7], [0, 0, 9, 0, 0], [0, 0, 0, 0, 3], [11, 0, 0, 0, 0], [0, 0, 16, 17, 13], [15, 0, 0, 0, 0], [14, 15, 17, 0, 13], [0, 0, 0, 0, 8], [2, 4, 2, 3, 2], [16, 0, 0, 19, 20]], "n_jobs": 10, "n_machines": 5, "n_operations": 43, "data_source": "SD1"}, {"instance_id": "SD1_0016", "job_length": [7, 9, 10, 1, 2, 2, 7, 2, 5, 10], "processing_times": [[18, 19, 0, 15, 0], [2, 2, 2, 2, 0], [0, 0, 5, 0, 4], [17, 16, 16, 17, 0], [0, 1, 1, 1, 0], [8, 8, 9, 11, 9], [0, 0, 0, 12, 12], [5, 0, 0, 0, 0], [6, 5, 0, 7, 5], [0, 0, 0, 15, 0], [0, 6, 7, 8, 7], [13, 0, 0, 0, 0], [9, 9, 8, 11, 10], [17, 14, 14, 14, 17], [7, 5, 7, 6, 5], [0, 7, 0, 6, 0], [20, 16, 19, 20, 15], [0, 0, 2, 2, 2], [18, 0, 0, 0, 16], [8, 11, 8, 12, 11], [15, 0, 0, 16, 0], [0, 9, 0, 0, 0], [6, 6, 6, 6, 0], [14, 0, 0, 0, 0], [0, 12, 0, 12, 12], [5, 0, 0, 0, 4], [0, 16, 14, 18, 0], [2, 2, 2, 3, 3], [10, 0, 8, 8, 0], [0, 0, 0, 0, 10], [14, 17, 17, 17, 0], [11, 13, 13, 13, 13], [0, 0, 6, 0, 0], [11, 10, 8, 11, 12], [6, 6, 7, 6, 6], [0, 0, 0, 5, 0], [0, 6, 7, 6, 0], [0, 0, 0, 11, 0], [0, 17, 13, 15, 17], [0, 0, 0, 0, 7], [1, 1, 1, 1, 0], [0, 0, 16, 16, 15], [0, 0, 0, 6, 0], [5, 6, 6, 5, 5], [1, 1, 1, 1, 0], [17, 0, 0, 15, 0], [0, 13, 0, 0, 0], [4, 5, 0, 5, 3], [13, 13, 0, 19, 0], [4, 0, 2, 2, 2], [0, 13, 0, 12, 0], [0, 0, 7, 6, 0], [0, 0, 3, 5, 3], [18, 19, 19, 0, 19], [17, 15, 20, 16, 15]], "n_jobs": 10, "n_machines": 5, "n_operations": 55, "data_source": "SD1"}, {"instance_id": "SD1_0017", "job_length": [5, 10, 8, 1, 6, 7, 2, 7, 10, 8], "processing_times": [[0, 0, 0, 2, 0], [0, 12, 0, 0, 11], [0, 16, 18, 0, 0], [0, 6, 0, 0, 0], [7, 5, 5, 6, 5], [0, 0, 6, 0, 0], [0, 6, 5, 6, 0], [0, 20, 0, 18, 20], [4, 6, 0, 0, 0], [7, 7, 7, 8, 10], [11, 13, 11, 11, 13], [0, 0, 0, 13, 0], [2, 0, 2, 2, 2], [6, 0, 0, 0, 0], [0, 0, 0, 0, 17], [7, 0, 6, 0, 7], [9, 0, 8, 11, 0], [19, 14, 0, 17, 17], [7, 6, 6, 0, 7], [0, 0, 0, 0, 18], [0, 0, 12, 0, 0], [14, 17, 0, 0, 19], [15, 19, 15, 19, 0], [9, 0, 0, 0, 8], [4, 6, 5, 6, 5], [11, 10, 12, 11, 11], [16, 0, 17, 0, 19], [0, 0, 3, 5, 0], [0, 19, 0, 18, 0], [4, 0, 0, 0, 0], [2, 0, 3, 3, 2], [5, 3, 5, 3, 4], [5, 7, 0, 5, 6], [11, 0, 11, 13, 14], [9, 0, 7, 0, 0], [16, 15, 0, 16, 13], [18, 19, 17, 0, 18], [14, 0, 18, 0, 0], [8, 0, 0, 0, 0], [0, 6, 6, 10, 9], [14, 14, 14, 0, 17], [16, 17, 15, 18, 18], [0, 12, 0, 0, 0], [2, 0, 2, 0, 0], [0, 0, 0, 0, 5], [0, 2, 2, 0, 2], [17, 14, 0, 0, 18], [8, 10, 8, 10, 9], [0, 16, 0, 0, 12], [6, 4, 4, 5, 0], [6, 0, 0, 8, 0], [20, 20, 18, 0, 18], [9, 0, 0, 0, 0], [15, 0, 17, 16, 18], [7, 0, 0, 0, 0], [0, 0, 0, 0, 1], [0, 13, 0, 0, 0], [17, 11, 14, 13, 0], [11, 11, 16, 12, 16], [13, 15, 0, 15, 13], [10, 14, 11, 13, 13], [16, 13, 0, 14, 12], [11, 10, 11, 14, 10], [0, 0, 0, 2, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 64, "data_source": "SD1"}, {"instance_id": "SD1_0018", "job_length": [7, 9, 9, 6, 9, 9, 9, 3, 2, 2], "processing_times": [[17, 16, 18, 20, 17], [17, 15, 0, 0, 14], [0, 0, 18, 0, 0], [0, 0, 0, 2, 0], [12, 0, 16, 0, 0], [0, 14, 0, 16, 0], [0, 0, 0, 0, 3], [15, 20, 17, 0, 20], [0, 1, 1, 0, 1], [0, 0, 0, 18, 14], [0, 5, 4, 0, 0], [0, 16, 0, 0, 19], [7, 7, 10, 0, 8], [0, 15, 0, 0, 0], [0, 0, 16, 0, 15], [0, 7, 0, 0, 9], [2, 0, 0, 4, 0], [0, 0, 12, 0, 11], [2, 2, 0, 2, 2], [16, 15, 20, 0, 20], [9, 10, 8, 8, 11], [0, 7, 8, 8, 0], [0, 0, 0, 0, 16], [0, 0, 16, 0, 0], [19, 17, 20, 20, 19], [14, 0, 0, 14, 13], [0, 0, 0, 0, 4], [7, 7, 8, 11, 0], [14, 12, 13, 15, 10], [0, 11, 0, 13, 0], [7, 9, 11, 0, 0], [16, 0, 13, 14, 0], [19, 13, 14, 13, 15], [0, 19, 18, 16, 20], [19, 0, 0, 16, 0], [18, 0, 0, 0, 0], [19, 0, 0, 18, 0], [16, 16, 17, 0, 14], [11, 13, 14, 16, 12], [0, 0, 20, 0, 0], [8, 7, 8, 0, 6], [6, 0, 8, 0, 7], [11, 12, 11, 10, 14], [0, 0, 0, 6, 8], [0, 7, 7, 0, 6], [0, 0, 0, 17, 0], [6, 0, 0, 6, 0], [0, 0, 10, 0, 0], [0, 3, 0, 0, 0], [0, 0, 10, 7, 10], [14, 0, 0, 10, 14], [0, 0, 12, 0, 0], [0, 0, 1, 1, 1], [18, 17, 15, 20, 14], [11, 10, 0, 0, 0], [2, 2, 2, 2, 2], [9, 13, 10, 13, 13], [0, 2, 0, 3, 0], [0, 16, 0, 0, 0], [6, 9, 6, 9, 8], [8, 7, 11, 0, 11], [0, 0, 3, 0, 0], [10, 11, 14, 0, 15], [0, 0, 0, 14, 0], [0, 10, 15, 0, 11]], "n_jobs": 10, "n_machines": 5, "n_operations": 65, "data_source": "SD1"}, {"instance_id": "SD1_0019", "job_length": [9, 1, 9, 9, 1, 4, 10, 7, 4, 8], "processing_times": [[0, 15, 12, 0, 0], [16, 17, 0, 17, 17], [12, 0, 16, 13, 0], [10, 0, 0, 0, 0], [0, 20, 17, 0, 0], [0, 20, 0, 0, 18], [12, 14, 15, 0, 15], [0, 3, 5, 4, 5], [0, 0, 2, 2, 0], [15, 0, 16, 20, 0], [8, 9, 8, 10, 9], [15, 0, 0, 0, 0], [0, 0, 0, 9, 0], [16, 17, 0, 18, 0], [12, 11, 17, 0, 12], [0, 0, 0, 16, 11], [0, 16, 0, 0, 0], [0, 0, 0, 0, 8], [0, 13, 10, 12, 12], [16, 0, 18, 0, 0], [16, 20, 17, 17, 17], [0, 0, 15, 16, 17], [16, 13, 16, 0, 15], [6, 0, 0, 0, 0], [20, 19, 17, 19, 20], [0, 0, 0, 4, 4], [3, 3, 2, 2, 3], [1, 0, 0, 0, 0], [0, 12, 10, 14, 11], [10, 0, 0, 0, 11], [0, 12, 11, 0, 0], [0, 0, 0, 8, 0], [4, 0, 0, 0, 3], [13, 14, 13, 0, 11], [0, 20, 17, 18, 0], [6, 9, 10, 8, 8], [20, 19, 16, 14, 17], [15, 0, 0, 0, 0], [0, 0, 11, 13, 12], [16, 0, 0, 0, 0], [0, 0, 0, 18, 0], [0, 7, 5, 0, 5], [5, 4, 0, 0, 5], [11, 13, 12, 0, 0], [2, 2, 2, 2, 2], [16, 0, 0, 19, 16], [1, 1, 0, 0, 0], [9, 0, 0, 10, 0], [10, 14, 11, 12, 12], [10, 8, 10, 11, 0], [0, 0, 0, 15, 0], [4, 2, 0, 2, 2], [2, 0, 0, 2, 0], [0, 0, 0, 12, 12], [0, 0, 7, 0, 0], [6, 7, 6, 6, 5], [9, 8, 6, 0, 0], [2, 2, 2, 0, 2], [1, 1, 1, 1, 1], [0, 10, 6, 7, 7], [15, 13, 14, 10, 13], [9, 10, 0, 12, 12]], "n_jobs": 10, "n_machines": 5, "n_operations": 62, "data_source": "SD1"}, {"instance_id": "SD1_0020", "job_length": [1, 2, 5, 3, 2, 5, 6, 2, 7, 2], "processing_times": [[0, 0, 0, 0, 19], [15, 20, 15, 19, 15], [0, 0, 0, 20, 0], [4, 2, 4, 0, 2], [1, 1, 1, 1, 1], [11, 8, 8, 0, 9], [18, 0, 0, 0, 0], [2, 0, 2, 2, 0], [18, 15, 18, 15, 19], [14, 17, 13, 17, 12], [0, 0, 0, 10, 11], [0, 0, 17, 0, 16], [16, 15, 17, 18, 19], [4, 4, 3, 3, 4], [13, 15, 0, 17, 17], [2, 2, 0, 0, 0], [1, 1, 1, 1, 1], [17, 18, 17, 16, 13], [16, 11, 0, 16, 12], [0, 0, 11, 0, 0], [0, 7, 0, 0, 0], [0, 12, 12, 15, 0], [1, 1, 1, 0, 1], [16, 0, 0, 0, 0], [2, 2, 0, 2, 0], [0, 0, 0, 0, 4], [0, 6, 5, 0, 6], [17, 19, 15, 17, 16], [0, 0, 0, 0, 19], [15, 11, 11, 0, 12], [0, 20, 0, 18, 16], [0, 0, 0, 0, 15], [0, 0, 13, 17, 0], [16, 19, 19, 20, 17], [20, 20, 19, 20, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 35, "data_source": "SD1"}, {"instance_id": "SD1_0021", "job_length": [6, 6, 8, 10, 3, 1, 9, 10, 10, 6], "processing_times": [[0, 0, 16, 0, 0], [0, 17, 15, 20, 18], [12, 15, 0, 14, 15], [9, 0, 7, 0, 0], [14, 0, 0, 14, 10], [0, 20, 0, 0, 0], [0, 6, 6, 6, 6], [9, 10, 9, 8, 8], [0, 12, 0, 14, 0], [0, 18, 0, 16, 13], [14, 12, 11, 16, 11], [19, 18, 20, 0, 0], [16, 19, 0, 17, 15], [5, 0, 0, 4, 3], [0, 0, 0, 16, 0], [7, 0, 0, 0, 6], [0, 7, 0, 0, 6], [0, 11, 11, 0, 13], [4, 2, 2, 2, 4], [0, 0, 2, 0, 0], [0, 2, 2, 0, 2], [16, 0, 0, 0, 0], [0, 0, 17, 0, 0], [13, 13, 11, 11, 11], [0, 0, 2, 0, 0], [0, 11, 0, 0, 10], [0, 0, 9, 6, 9], [13, 12, 13, 13, 13], [0, 18, 0, 16, 16], [5, 4, 5, 6, 6], [2, 2, 0, 2, 0], [0, 10, 8, 9, 7], [0, 1, 0, 0, 0], [0, 19, 0, 0, 0], [2, 0, 0, 2, 2], [0, 0, 0, 0, 2], [4, 6, 4, 6, 5], [20, 0, 0, 0, 15], [5, 6, 5, 5, 6], [9, 7, 0, 0, 0], [0, 0, 0, 10, 10], [18, 0, 0, 17, 19], [0, 0, 0, 20, 0], [0, 17, 0, 0, 0], [0, 0, 6, 0, 6], [0, 0, 0, 1, 0], [4, 5, 4, 6, 4], [4, 0, 0, 0, 0], [0, 20, 0, 0, 0], [13, 9, 12, 10, 10], [15, 16, 16, 18, 16], [0, 0, 0, 0, 20], [0, 14, 0, 0, 0], [0, 0, 12, 12, 0], [10, 0, 9, 10, 7], [0, 0, 15, 0, 0], [0, 7, 9, 11, 7], [5, 0, 0, 0, 0], [12, 18, 12, 18, 14], [4, 0, 0, 0, 4], [6, 0, 6, 6, 0], [15, 17, 16, 16, 13], [5, 4, 4, 5, 4], [10, 7, 8, 0, 11], [0, 0, 13, 16, 17], [10, 0, 0, 0, 9], [19, 0, 20, 16, 16], [0, 0, 0, 18, 0], [7, 8, 6, 6, 7]], "n_jobs": 10, "n_machines": 5, "n_operations": 69, "data_source": "SD1"}, {"instance_id": "SD1_0022", "job_length": [9, 2, 6, 7, 5, 5, 2, 7, 6, 10], "processing_times": [[14, 13, 0, 12, 15], [10, 11, 12, 12, 11], [0, 4, 0, 3, 0], [0, 0, 3, 0, 0], [8, 0, 0, 8, 0], [0, 16, 20, 16, 17], [0, 16, 0, 16, 0], [0, 0, 8, 11, 0], [14, 18, 20, 19, 17], [0, 0, 13, 11, 0], [16, 17, 17, 16, 14], [0, 17, 20, 16, 17], [7, 6, 6, 7, 6], [18, 15, 12, 15, 18], [3, 4, 5, 0, 3], [0, 0, 12, 0, 10], [13, 16, 17, 0, 17], [16, 20, 19, 19, 18], [0, 6, 7, 0, 6], [9, 11, 8, 11, 11], [17, 18, 18, 17, 18], [0, 18, 18, 0, 0], [2, 0, 0, 0, 2], [0, 0, 8, 11, 0], [0, 16, 0, 0, 0], [0, 0, 0, 0, 3], [11, 11, 10, 0, 9], [0, 7, 11, 10, 0], [17, 14, 20, 15, 14], [0, 0, 0, 13, 0], [6, 6, 6, 8, 7], [9, 9, 10, 0, 10], [0, 2, 4, 2, 3], [10, 9, 13, 12, 10], [0, 15, 0, 0, 15], [10, 0, 12, 0, 12], [19, 0, 18, 15, 0], [15, 16, 16, 19, 15], [6, 7, 0, 6, 0], [0, 19, 0, 18, 20], [13, 0, 0, 0, 0], [0, 12, 12, 0, 13], [0, 13, 17, 14, 15], [0, 0, 0, 16, 0], [17, 17, 17, 16, 17], [1, 1, 1, 1, 1], [9, 10, 0, 0, 11], [13, 13, 9, 10, 0], [0, 0, 0, 5, 0], [0, 0, 0, 7, 0], [5, 0, 4, 5, 5], [7, 7, 8, 0, 0], [10, 13, 9, 0, 12], [16, 15, 0, 16, 16], [8, 0, 0, 10, 0], [20, 0, 14, 15, 17], [0, 0, 0, 0, 12], [10, 8, 9, 11, 12], [0, 0, 1, 0, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 59, "data_source": "SD1"}, {"instance_id": "SD1_0023", "job_length": [6, 9, 3, 1, 7, 4, 4, 2, 7, 2], "processing_times": [[0, 0, 0, 15, 0], [0, 7, 8, 0, 0], [13, 10, 0, 0, 0], [0, 0, 16, 0, 0], [7, 5, 6, 6, 7], [0, 0, 17, 0, 0], [0, 16, 16, 15, 0], [8, 6, 0, 8, 0], [19, 0, 0, 14, 15], [0, 20, 17, 0, 18], [5, 5, 0, 4, 6], [0, 0, 0, 0, 11], [20, 17, 17, 16, 20], [16, 20, 18, 0, 18], [8, 7, 6, 7, 7], [6, 0, 8, 0, 7], [10, 13, 0, 0, 13], [0, 20, 18, 17, 0], [2, 2, 2, 2, 2], [0, 0, 0, 5, 0], [16, 0, 19, 20, 0], [8, 0, 6, 6, 0], [0, 5, 0, 0, 0], [0, 0, 0, 14, 0], [7, 0, 9, 0, 9], [16, 0, 0, 20, 0], [17, 13, 15, 17, 19], [8, 0, 0, 0, 0], [0, 0, 17, 19, 0], [18, 15, 16, 0, 0], [11, 10, 11, 14, 13], [0, 0, 0, 0, 1], [8, 7, 0, 0, 7], [10, 0, 0, 13, 0], [15, 0, 11, 0, 0], [18, 14, 16, 18, 15], [0, 8, 8, 8, 8], [1, 1, 1, 0, 0], [0, 0, 0, 5, 0], [7, 6, 8, 6, 10], [1, 0, 0, 0, 1], [6, 7, 0, 0, 0], [0, 0, 0, 0, 15], [0, 0, 16, 0, 10], [9, 0, 10, 0, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 45, "data_source": "SD1"}, {"instance_id": "SD1_0024", "job_length": [5, 8, 6, 2, 8, 3, 6, 5, 10, 5], "processing_times": [[0, 2, 0, 2, 0], [19, 0, 20, 0, 0], [0, 9, 7, 11, 0], [0, 16, 18, 20, 18], [12, 16, 0, 11, 0], [7, 6, 7, 7, 7], [0, 0, 16, 18, 0], [10, 10, 11, 10, 10], [2, 0, 2, 0, 0], [0, 15, 0, 15, 0], [13, 19, 17, 16, 15], [6, 0, 0, 6, 6], [3, 2, 4, 2, 3], [11, 12, 13, 14, 11], [9, 8, 0, 10, 7], [5, 0, 0, 4, 5], [5, 0, 5, 4, 0], [6, 0, 6, 10, 0], [0, 2, 0, 2, 0], [4, 5, 4, 3, 4], [15, 12, 17, 13, 11], [0, 7, 6, 5, 0], [18, 0, 18, 18, 15], [4, 4, 2, 4, 2], [17, 17, 0, 16, 14], [0, 0, 8, 8, 0], [9, 9, 0, 10, 7], [16, 0, 0, 14, 18], [0, 18, 19, 19, 15], [14, 0, 17, 0, 0], [0, 18, 16, 15, 0], [0, 4, 6, 0, 5], [0, 0, 0, 12, 13], [14, 15, 16, 17, 12], [15, 0, 14, 20, 17], [6, 4, 6, 6, 5], [2, 2, 2, 2, 2], [0, 0, 0, 8, 10], [14, 13, 11, 16, 14], [0, 0, 0, 0, 10], [14, 19, 16, 15, 0], [0, 17, 0, 0, 0], [5, 7, 0, 0, 6], [7, 0, 0, 0, 0], [19, 14, 19, 20, 0], [10, 0, 0, 0, 0], [14, 0, 14, 0, 0], [0, 0, 5, 0, 0], [9, 13, 10, 11, 9], [0, 15, 11, 13, 11], [17, 17, 18, 16, 19], [14, 0, 12, 10, 14], [15, 0, 19, 18, 0], [15, 0, 0, 18, 0], [1, 1, 1, 1, 1], [0, 0, 5, 0, 5], [0, 0, 0, 5, 4], [0, 0, 0, 10, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 58, "data_source": "SD1"}, {"instance_id": "SD1_0025", "job_length": [10, 1, 3, 9, 5, 1, 4, 4, 6, 3], "processing_times": [[0, 16, 0, 0, 0], [0, 0, 0, 0, 4], [1, 1, 0, 1, 1], [14, 17, 18, 16, 0], [0, 0, 0, 12, 0], [9, 8, 0, 8, 11], [0, 0, 6, 0, 0], [0, 0, 5, 6, 7], [7, 6, 6, 8, 0], [19, 0, 0, 19, 0], [0, 2, 0, 2, 0], [0, 0, 12, 11, 9], [1, 1, 0, 0, 0], [6, 6, 0, 6, 6], [17, 0, 19, 19, 15], [5, 3, 0, 0, 3], [17, 19, 19, 20, 15], [0, 0, 9, 0, 10], [0, 17, 18, 16, 13], [6, 8, 0, 6, 7], [7, 0, 7, 0, 0], [15, 0, 14, 0, 13], [13, 0, 0, 14, 16], [2, 2, 2, 0, 2], [15, 20, 20, 0, 0], [0, 0, 0, 0, 5], [0, 6, 5, 0, 4], [11, 11, 0, 10, 10], [2, 2, 2, 0, 2], [0, 0, 4, 0, 0], [0, 16, 0, 16, 0], [12, 10, 12, 8, 0], [7, 7, 6, 5, 6], [0, 17, 0, 17, 12], [0, 13, 11, 0, 16], [0, 0, 0, 18, 0], [13, 15, 16, 0, 0], [0, 17, 0, 19, 18], [5, 4, 0, 4, 0], [11, 0, 9, 0, 0], [20, 20, 20, 0, 20], [4, 0, 0, 0, 0], [0, 14, 14, 15, 14], [10, 0, 10, 0, 6], [0, 0, 20, 17, 19], [19, 0, 0, 0, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 46, "data_source": "SD1"}, {"instance_id": "SD1_0026", "job_length": [9, 2, 2, 5, 7, 7, 6, 10, 10, 8], "processing_times": [[0, 20, 0, 0, 0], [13, 0, 17, 18, 13], [10, 11, 15, 0, 12], [0, 0, 0, 16, 0], [4, 4, 3, 3, 4], [0, 3, 4, 3, 4], [16, 20, 20, 20, 17], [19, 20, 19, 18, 16], [5, 5, 4, 6, 6], [0, 19, 0, 14, 16], [0, 0, 0, 0, 19], [0, 0, 0, 16, 0], [0, 0, 0, 12, 0], [6, 0, 0, 6, 8], [12, 0, 11, 10, 9], [0, 17, 0, 0, 0], [15, 0, 11, 10, 10], [0, 10, 10, 10, 14], [5, 6, 7, 5, 5], [0, 0, 5, 0, 0], [0, 0, 0, 0, 8], [0, 0, 18, 0, 16], [6, 4, 5, 6, 4], [0, 14, 0, 19, 0], [4, 0, 5, 0, 5], [10, 0, 8, 0, 0], [0, 5, 0, 7, 0], [6, 0, 0, 0, 0], [0, 6, 0, 0, 8], [5, 5, 6, 5, 0], [14, 19, 0, 14, 13], [6, 6, 5, 5, 7], [8, 0, 7, 8, 8], [1, 1, 1, 0, 0], [8, 10, 0, 7, 6], [0, 0, 9, 11, 12], [5, 0, 0, 4, 6], [0, 0, 8, 6, 6], [12, 0, 15, 17, 0], [9, 9, 6, 10, 0], [0, 0, 0, 17, 14], [6, 8, 8, 6, 8], [0, 0, 16, 0, 0], [0, 0, 9, 9, 0], [0, 18, 0, 19, 18], [11, 15, 14, 13, 13], [12, 13, 14, 12, 0], [0, 0, 4, 0, 6], [18, 17, 0, 0, 0], [1, 0, 1, 1, 1], [16, 19, 0, 17, 0], [6, 5, 5, 4, 4], [0, 18, 0, 16, 20], [2, 2, 0, 0, 0], [5, 6, 4, 4, 0], [0, 0, 0, 0, 9], [3, 3, 3, 2, 3], [13, 15, 18, 0, 0], [0, 0, 0, 12, 0], [8, 0, 0, 0, 6], [0, 0, 0, 10, 0], [1, 0, 0, 0, 0], [0, 11, 0, 10, 0], [8, 0, 6, 10, 9], [14, 0, 0, 0, 0], [0, 10, 0, 15, 13]], "n_jobs": 10, "n_machines": 5, "n_operations": 66, "data_source": "SD1"}, {"instance_id": "SD1_0027", "job_length": [3, 10, 9, 3, 10, 10, 9, 4, 7, 2], "processing_times": [[14, 16, 14, 0, 16], [0, 7, 6, 8, 6], [0, 0, 13, 14, 0], [0, 15, 13, 16, 13], [18, 13, 16, 14, 16], [0, 15, 0, 13, 14], [0, 0, 11, 0, 7], [14, 13, 11, 14, 15], [0, 11, 13, 9, 0], [8, 6, 6, 0, 0], [0, 0, 0, 3, 0], [9, 8, 0, 0, 0], [11, 0, 0, 0, 0], [0, 12, 0, 0, 17], [17, 18, 17, 16, 19], [16, 16, 18, 16, 20], [9, 7, 11, 10, 10], [0, 0, 0, 0, 15], [14, 12, 16, 10, 13], [1, 1, 1, 0, 1], [0, 0, 19, 17, 20], [0, 10, 11, 0, 12], [18, 0, 0, 0, 13], [0, 0, 2, 3, 3], [5, 4, 6, 4, 0], [12, 11, 12, 16, 11], [8, 12, 0, 9, 9], [17, 12, 0, 0, 16], [15, 15, 15, 12, 12], [17, 0, 0, 0, 0], [15, 18, 0, 17, 15], [0, 6, 5, 5, 7], [6, 6, 7, 7, 6], [20, 17, 17, 19, 20], [19, 14, 17, 14, 20], [1, 1, 1, 1, 1], [4, 3, 2, 3, 3], [18, 15, 14, 0, 16], [0, 0, 0, 0, 10], [0, 2, 0, 0, 0], [4, 4, 0, 0, 0], [6, 0, 0, 6, 0], [20, 0, 0, 18, 15], [0, 0, 5, 5, 6], [20, 17, 0, 0, 15], [0, 17, 18, 0, 17], [0, 19, 0, 0, 0], [9, 9, 10, 6, 6], [7, 0, 0, 0, 0], [18, 0, 20, 0, 14], [12, 0, 0, 13, 12], [14, 0, 15, 0, 18], [12, 11, 10, 10, 10], [16, 14, 14, 14, 15], [0, 0, 6, 0, 5], [0, 0, 9, 9, 11], [16, 18, 0, 0, 0], [11, 13, 14, 0, 14], [0, 0, 7, 5, 5], [0, 8, 0, 0, 0], [0, 14, 0, 0, 0], [15, 19, 14, 15, 15], [14, 14, 11, 0, 13], [0, 0, 3, 4, 5], [1, 0, 0, 1, 1], [5, 4, 6, 6, 4], [10, 13, 16, 14, 13]], "n_jobs": 10, "n_machines": 5, "n_operations": 67, "data_source": "SD1"}, {"instance_id": "SD1_0028", "job_length": [2, 10, 6, 7, 3, 7, 8, 4, 7, 5], "processing_times": [[0, 9, 0, 6, 0], [0, 0, 0, 0, 20], [13, 9, 9, 0, 12], [20, 17, 19, 20, 16], [6, 4, 5, 4, 5], [11, 0, 0, 0, 11], [0, 18, 19, 0, 0], [0, 0, 1, 0, 0], [7, 0, 9, 7, 0], [0, 6, 6, 7, 7], [0, 5, 4, 0, 4], [11, 8, 0, 0, 0], [16, 0, 19, 0, 20], [10, 0, 0, 13, 0], [13, 17, 13, 18, 17], [2, 2, 2, 2, 2], [17, 0, 16, 0, 0], [0, 5, 0, 0, 0], [6, 7, 0, 0, 0], [12, 13, 10, 11, 14], [4, 4, 0, 5, 3], [2, 2, 2, 0, 2], [4, 0, 5, 0, 0], [19, 19, 17, 19, 17], [6, 6, 8, 10, 6], [18, 20, 0, 0, 14], [4, 5, 0, 5, 5], [0, 12, 0, 0, 0], [0, 0, 19, 15, 19], [6, 7, 8, 7, 8], [0, 1, 0, 0, 1], [2, 0, 0, 0, 0], [17, 0, 0, 0, 19], [14, 18, 15, 20, 16], [10, 10, 7, 11, 7], [6, 6, 4, 4, 5], [0, 12, 13, 12, 0], [8, 0, 0, 9, 9], [1, 1, 1, 0, 1], [0, 6, 6, 6, 0], [10, 11, 0, 0, 11], [7, 5, 7, 0, 0], [9, 13, 12, 13, 11], [15, 0, 16, 19, 0], [18, 0, 0, 0, 0], [13, 0, 13, 13, 10], [3, 0, 0, 0, 0], [3, 5, 0, 0, 4], [0, 3, 4, 3, 4], [12, 0, 0, 0, 11], [10, 8, 10, 11, 0], [10, 12, 9, 11, 9], [10, 15, 12, 11, 13], [15, 0, 18, 18, 17], [10, 8, 8, 9, 11], [11, 0, 0, 0, 0], [9, 12, 13, 10, 0], [8, 9, 8, 10, 11], [0, 0, 0, 13, 11]], "n_jobs": 10, "n_machines": 5, "n_operations": 59, "data_source": "SD1"}, {"instance_id": "SD1_0029", "job_length": [4, 4, 3, 5, 3, 6, 10, 8, 8, 10], "processing_times": [[0, 11, 10, 11, 11], [0, 0, 1, 0, 0], [0, 0, 8, 0, 0], [0, 14, 0, 0, 13], [0, 0, 0, 6, 8], [0, 0, 0, 0, 2], [19, 18, 20, 19, 20], [17, 0, 19, 0, 0], [0, 13, 13, 0, 14], [0, 0, 0, 0, 5], [0, 9, 9, 7, 0], [15, 20, 20, 16, 0], [19, 16, 20, 18, 19], [17, 20, 0, 16, 0], [5, 0, 3, 5, 3], [0, 4, 0, 0, 0], [5, 5, 6, 6, 0], [17, 18, 0, 0, 18], [17, 20, 0, 15, 19], [0, 0, 0, 0, 7], [18, 19, 16, 18, 19], [16, 18, 17, 16, 0], [0, 0, 13, 13, 12], [0, 15, 13, 0, 0], [0, 0, 0, 0, 16], [12, 0, 17, 16, 0], [0, 0, 0, 0, 18], [3, 0, 2, 0, 4], [7, 8, 7, 8, 6], [2, 2, 2, 2, 2], [1, 1, 0, 0, 1], [14, 15, 14, 19, 15], [0, 0, 6, 0, 0], [0, 15, 18, 18, 19], [16, 15, 18, 16, 16], [8, 6, 8, 10, 10], [17, 16, 14, 17, 0], [0, 3, 0, 0, 0], [17, 0, 13, 0, 0], [14, 0, 0, 13, 11], [11, 0, 8, 0, 11], [0, 0, 9, 8, 0], [0, 16, 0, 19, 0], [3, 0, 4, 0, 0], [13, 14, 13, 12, 13], [0, 12, 0, 0, 13], [3, 3, 4, 3, 4], [2, 2, 2, 2, 2], [8, 8, 8, 9, 8], [0, 0, 0, 1, 1], [0, 0, 0, 1, 0], [6, 0, 8, 6, 7], [14, 13, 13, 12, 13], [0, 12, 0, 0, 0], [2, 2, 2, 2, 2], [14, 14, 11, 13, 0], [1, 1, 1, 0, 1], [6, 0, 0, 7, 5], [20, 18, 15, 18, 16], [0, 7, 0, 0, 0], [0, 0, 0, 6, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 61, "data_source": "SD1"}, {"instance_id": "SD1_0030", "job_length": [1, 10, 8, 7, 10, 8, 10, 7, 5, 6], "processing_times": [[0, 3, 4, 0, 0], [0, 0, 0, 2, 0], [16, 13, 12, 0, 11], [0, 0, 20, 0, 0], [0, 0, 0, 2, 2], [16, 18, 0, 16, 14], [5, 5, 7, 7, 6], [8, 6, 8, 8, 6], [0, 0, 6, 0, 7], [0, 0, 8, 9, 0], [12, 14, 12, 14, 17], [18, 20, 16, 16, 0], [0, 0, 0, 17, 0], [2, 2, 0, 0, 0], [0, 0, 0, 1, 1], [10, 8, 10, 0, 0], [3, 4, 5, 0, 0], [0, 0, 19, 0, 0], [13, 17, 16, 16, 0], [6, 6, 6, 6, 5], [0, 5, 5, 4, 5], [13, 0, 0, 0, 0], [10, 13, 16, 10, 15], [13, 14, 11, 14, 13], [5, 4, 6, 6, 0], [10, 16, 13, 12, 16], [19, 18, 0, 19, 15], [7, 6, 6, 5, 5], [18, 16, 19, 17, 16], [15, 18, 0, 14, 17], [1, 0, 0, 1, 0], [19, 0, 0, 0, 0], [0, 8, 7, 0, 8], [6, 7, 6, 8, 9], [0, 17, 0, 0, 0], [0, 0, 0, 0, 15], [0, 0, 0, 0, 13], [13, 11, 14, 12, 12], [0, 16, 13, 0, 15], [1, 0, 0, 0, 0], [0, 0, 0, 0, 5], [17, 0, 16, 0, 0], [19, 18, 20, 20, 16], [16, 17, 12, 14, 16], [12, 11, 12, 12, 13], [7, 6, 6, 7, 6], [0, 16, 16, 15, 0], [2, 0, 2, 3, 3], [14, 13, 0, 11, 13], [0, 0, 0, 16, 11], [20, 0, 0, 0, 19], [0, 0, 2, 2, 0], [0, 16, 15, 18, 0], [0, 0, 6, 0, 0], [13, 12, 0, 13, 13], [0, 7, 7, 7, 7], [18, 20, 16, 15, 19], [5, 0, 4, 5, 3], [1, 0, 0, 0, 1], [0, 9, 0, 9, 0], [2, 2, 2, 2, 2], [10, 9, 0, 0, 9], [0, 15, 19, 0, 19], [0, 0, 0, 20, 0], [9, 0, 7, 6, 7], [0, 20, 18, 0, 0], [14, 15, 18, 0, 0], [16, 13, 17, 17, 14], [1, 1, 1, 1, 1], [15, 13, 13, 13, 12], [13, 17, 18, 13, 0], [20, 19, 15, 20, 15]], "n_jobs": 10, "n_machines": 5, "n_operations": 72, "data_source": "SD1"}, {"instance_id": "SD1_0031", "job_length": [8, 6, 1, 9, 2, 9, 10, 1, 1, 2], "processing_times": [[8, 0, 0, 0, 0], [13, 0, 14, 14, 13], [0, 16, 16, 16, 0], [0, 4, 0, 5, 0], [15, 13, 15, 15, 19], [0, 14, 0, 0, 16], [2, 0, 0, 0, 3], [2, 0, 0, 0, 0], [0, 0, 0, 0, 15], [8, 7, 11, 0, 0], [16, 19, 0, 20, 15], [0, 10, 12, 0, 0], [2, 0, 0, 0, 2], [6, 4, 4, 5, 4], [5, 5, 3, 0, 0], [13, 13, 0, 10, 9], [0, 0, 17, 0, 18], [14, 0, 0, 11, 0], [15, 0, 18, 20, 0], [7, 0, 0, 0, 10], [0, 18, 0, 0, 17], [3, 0, 4, 3, 2], [0, 13, 0, 0, 9], [15, 16, 0, 15, 14], [5, 0, 0, 0, 4], [0, 0, 0, 17, 0], [20, 18, 0, 18, 0], [5, 4, 4, 4, 4], [2, 0, 0, 2, 0], [0, 2, 2, 0, 2], [0, 7, 6, 0, 6], [12, 11, 0, 0, 0], [0, 0, 10, 0, 9], [0, 2, 0, 2, 0], [15, 0, 0, 0, 14], [0, 1, 1, 1, 1], [11, 11, 9, 0, 10], [16, 14, 0, 20, 0], [12, 11, 11, 10, 10], [0, 6, 0, 6, 4], [13, 0, 18, 0, 0], [12, 10, 0, 12, 0], [10, 14, 12, 13, 12], [0, 0, 0, 0, 9], [10, 0, 0, 0, 0], [8, 0, 6, 7, 8], [1, 0, 1, 1, 1], [0, 8, 0, 9, 0], [0, 11, 0, 0, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 49, "data_source": "SD1"}, {"instance_id": "SD1_0032", "job_length": [9, 6, 7, 3, 4, 10, 5, 1, 10, 4], "processing_times": [[12, 0, 9, 0, 0], [11, 12, 12, 13, 0], [6, 6, 7, 6, 7], [0, 0, 0, 11, 0], [0, 8, 9, 0, 11], [14, 12, 0, 0, 15], [11, 0, 0, 0, 0], [0, 12, 13, 0, 0], [9, 0, 0, 0, 0], [14, 0, 0, 0, 0], [0, 0, 0, 9, 7], [3, 3, 3, 4, 0], [0, 11, 0, 0, 0], [18, 0, 20, 18, 0], [2, 2, 2, 0, 2], [0, 2, 0, 2, 0], [4, 4, 6, 6, 4], [0, 0, 0, 17, 16], [0, 17, 20, 18, 0], [8, 6, 6, 0, 6], [0, 0, 19, 17, 0], [0, 4, 0, 4, 4], [16, 0, 0, 0, 0], [0, 0, 0, 0, 3], [0, 0, 14, 0, 0], [16, 10, 11, 15, 10], [5, 0, 0, 0, 6], [10, 13, 0, 0, 11], [13, 17, 13, 0, 18], [0, 14, 0, 0, 11], [8, 9, 8, 8, 10], [1, 1, 1, 0, 1], [0, 19, 0, 16, 17], [0, 0, 0, 5, 0], [17, 0, 14, 15, 16], [10, 12, 14, 10, 10], [17, 17, 14, 19, 0], [12, 15, 15, 14, 13], [0, 2, 2, 2, 2], [4, 6, 6, 6, 6], [12, 11, 15, 0, 11], [12, 10, 0, 0, 0], [2, 0, 2, 2, 2], [5, 6, 0, 0, 0], [14, 16, 13, 13, 13], [0, 16, 16, 15, 15], [7, 0, 7, 0, 6], [10, 13, 13, 0, 12], [0, 0, 0, 8, 0], [0, 0, 12, 11, 0], [0, 20, 0, 18, 15], [0, 3, 0, 0, 4], [0, 2, 0, 0, 0], [0, 0, 0, 8, 6], [0, 0, 12, 0, 0], [0, 0, 5, 0, 5], [7, 8, 0, 0, 0], [0, 0, 10, 13, 10], [0, 0, 0, 6, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 59, "data_source": "SD1"}, {"instance_id": "SD1_0033", "job_length": [6, 4, 3, 4, 10, 5, 4, 10, 10, 1], "processing_times": [[3, 0, 0, 0, 2], [7, 0, 7, 7, 0], [15, 15, 11, 10, 12], [16, 18, 0, 0, 15], [0, 0, 17, 16, 19], [19, 16, 18, 19, 16], [15, 10, 10, 14, 0], [12, 0, 0, 0, 0], [7, 5, 5, 7, 7], [0, 2, 2, 2, 2], [0, 0, 20, 18, 17], [0, 0, 0, 15, 0], [4, 3, 3, 4, 3], [3, 5, 3, 5, 4], [18, 16, 16, 17, 0], [4, 0, 4, 4, 2], [14, 15, 0, 17, 16], [0, 5, 5, 0, 7], [8, 8, 7, 6, 8], [11, 0, 0, 10, 0], [0, 4, 4, 0, 0], [18, 19, 14, 19, 15], [15, 0, 15, 16, 13], [0, 0, 0, 6, 6], [20, 0, 17, 19, 0], [0, 2, 2, 2, 2], [20, 19, 16, 15, 17], [0, 13, 11, 13, 12], [16, 0, 0, 0, 13], [0, 0, 2, 0, 0], [0, 0, 0, 16, 0], [10, 7, 0, 7, 10], [13, 11, 9, 10, 13], [0, 8, 6, 7, 6], [1, 0, 0, 0, 0], [15, 0, 20, 0, 18], [11, 0, 10, 0, 11], [14, 0, 0, 20, 0], [0, 4, 0, 2, 4], [13, 0, 13, 16, 0], [0, 0, 6, 0, 0], [6, 10, 0, 0, 10], [12, 12, 10, 10, 0], [8, 8, 9, 9, 0], [0, 8, 6, 0, 0], [1, 1, 1, 0, 1], [5, 4, 6, 5, 4], [15, 0, 13, 16, 18], [5, 5, 5, 7, 6], [19, 18, 0, 18, 15], [0, 0, 9, 0, 0], [0, 4, 2, 0, 0], [2, 2, 2, 0, 0], [8, 0, 0, 0, 0], [0, 13, 0, 0, 0], [6, 4, 6, 6, 5], [0, 0, 10, 0, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 57, "data_source": "SD1"}, {"instance_id": "SD1_0034", "job_length": [10, 6, 8, 9, 7, 4, 4, 8, 3, 4], "processing_times": [[0, 0, 18, 0, 0], [0, 0, 0, 3, 2], [0, 0, 15, 16, 15], [0, 0, 0, 0, 5], [13, 11, 10, 12, 12], [13, 12, 0, 14, 11], [0, 0, 12, 0, 9], [18, 13, 19, 19, 18], [0, 0, 1, 1, 0], [3, 3, 4, 4, 3], [0, 0, 1, 1, 1], [0, 7, 7, 0, 9], [0, 0, 16, 0, 0], [0, 15, 0, 0, 0], [0, 19, 16, 18, 16], [0, 4, 0, 0, 0], [0, 0, 0, 0, 8], [18, 15, 12, 14, 16], [0, 2, 2, 0, 0], [0, 14, 0, 12, 0], [0, 0, 0, 0, 10], [12, 0, 0, 0, 0], [6, 0, 7, 0, 0], [0, 11, 10, 12, 8], [0, 0, 2, 0, 2], [0, 2, 4, 3, 0], [0, 0, 0, 6, 0], [0, 5, 0, 0, 0], [0, 0, 14, 0, 0], [8, 8, 0, 10, 9], [6, 7, 6, 7, 7], [10, 0, 8, 0, 0], [0, 0, 2, 0, 0], [10, 0, 0, 0, 15], [0, 0, 0, 15, 0], [0, 13, 0, 0, 13], [0, 0, 1, 1, 1], [0, 0, 0, 9, 0], [0, 7, 0, 0, 0], [2, 0, 2, 2, 2], [0, 11, 0, 0, 0], [7, 6, 0, 0, 0], [15, 15, 14, 13, 13], [9, 0, 0, 0, 0], [0, 0, 0, 11, 0], [0, 2, 0, 0, 0], [6, 5, 5, 0, 5], [4, 2, 3, 4, 3], [0, 5, 0, 5, 3], [0, 2, 0, 2, 0], [0, 0, 18, 0, 0], [1, 0, 0, 0, 0], [14, 0, 12, 12, 13], [7, 0, 6, 0, 7], [2, 2, 0, 2, 2], [17, 15, 0, 15, 15], [0, 14, 18, 0, 14], [7, 6, 7, 0, 8], [5, 7, 7, 5, 5], [13, 12, 0, 0, 13], [0, 2, 0, 0, 2], [15, 16, 14, 0, 12], [0, 10, 10, 8, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 63, "data_source": "SD1"}, {"instance_id": "SD1_0035", "job_length": [9, 1, 9, 1, 4, 1, 4, 8, 3, 7], "processing_times": [[8, 0, 10, 11, 0], [0, 0, 0, 18, 0], [0, 10, 9, 10, 10], [15, 18, 14, 13, 12], [0, 0, 5, 3, 4], [7, 8, 8, 0, 0], [5, 6, 5, 4, 6], [0, 0, 0, 5, 0], [0, 0, 0, 8, 7], [14, 13, 12, 16, 0], [0, 10, 13, 9, 13], [0, 2, 2, 0, 2], [18, 18, 17, 0, 0], [0, 0, 0, 11, 0], [0, 0, 0, 15, 0], [0, 0, 16, 0, 0], [0, 9, 10, 8, 0], [2, 2, 2, 2, 2], [3, 0, 0, 0, 4], [4, 5, 0, 0, 4], [0, 1, 0, 1, 1], [14, 0, 0, 0, 0], [7, 5, 0, 5, 0], [19, 20, 16, 16, 15], [0, 12, 10, 10, 9], [6, 7, 0, 5, 5], [6, 4, 4, 5, 5], [0, 0, 7, 0, 0], [3, 4, 5, 3, 4], [1, 1, 0, 1, 0], [0, 0, 7, 9, 10], [0, 0, 0, 0, 8], [6, 5, 5, 4, 4], [15, 0, 0, 14, 17], [8, 0, 8, 6, 6], [4, 4, 0, 6, 5], [12, 0, 0, 0, 0], [17, 16, 16, 18, 20], [0, 0, 0, 0, 4], [0, 0, 0, 0, 17], [5, 0, 4, 5, 3], [7, 6, 7, 0, 5], [15, 15, 15, 0, 0], [2, 2, 0, 0, 2], [0, 0, 0, 16, 10], [16, 16, 18, 18, 0], [13, 18, 12, 14, 12]], "n_jobs": 10, "n_machines": 5, "n_operations": 47, "data_source": "SD1"}, {"instance_id": "SD1_0036", "job_length": [6, 8, 2, 6, 7, 1, 9, 2, 6, 6], "processing_times": [[9, 0, 0, 0, 0], [0, 19, 0, 0, 0], [0, 0, 0, 12, 16], [0, 0, 0, 4, 0], [7, 0, 0, 0, 0], [11, 11, 12, 0, 10], [18, 17, 19, 19, 15], [0, 0, 0, 16, 14], [19, 0, 17, 20, 19], [0, 14, 0, 0, 0], [0, 0, 0, 0, 18], [0, 0, 13, 0, 14], [10, 10, 15, 12, 10], [17, 19, 20, 17, 0], [0, 10, 0, 0, 0], [0, 2, 0, 0, 0], [7, 6, 0, 0, 0], [0, 0, 7, 0, 0], [0, 0, 16, 20, 0], [0, 0, 0, 0, 13], [0, 15, 19, 0, 15], [3, 2, 0, 3, 2], [0, 17, 0, 18, 17], [7, 0, 0, 0, 0], [7, 7, 10, 8, 8], [3, 3, 0, 0, 3], [0, 11, 9, 8, 0], [0, 15, 0, 0, 0], [0, 16, 18, 0, 17], [13, 10, 0, 0, 14], [17, 16, 0, 0, 0], [0, 0, 1, 0, 0], [12, 12, 0, 13, 0], [4, 6, 4, 0, 4], [11, 0, 0, 11, 0], [0, 13, 11, 14, 17], [3, 5, 5, 4, 5], [13, 11, 0, 0, 15], [19, 16, 19, 13, 14], [2, 0, 2, 0, 0], [11, 11, 12, 0, 9], [9, 10, 0, 10, 0], [20, 19, 0, 17, 17], [5, 7, 6, 7, 5], [0, 0, 19, 0, 0], [16, 0, 0, 15, 0], [0, 13, 14, 18, 14], [17, 12, 16, 14, 16], [0, 0, 6, 0, 0], [0, 0, 11, 12, 0], [8, 7, 7, 7, 7], [6, 0, 0, 5, 5], [18, 18, 14, 18, 13]], "n_jobs": 10, "n_machines": 5, "n_operations": 53, "data_source": "SD1"}, {"instance_id": "SD1_0037", "job_length": [8, 1, 7, 10, 10, 1, 6, 6, 3, 6], "processing_times": [[0, 0, 11, 0, 10], [14, 0, 11, 0, 0], [5, 3, 3, 5, 3], [0, 15, 0, 17, 0], [2, 2, 0, 0, 0], [16, 0, 0, 0, 0], [3, 0, 5, 4, 3], [8, 6, 8, 10, 10], [0, 0, 0, 0, 4], [8, 11, 7, 10, 8], [13, 12, 10, 0, 13], [0, 0, 0, 0, 6], [7, 6, 5, 7, 0], [0, 0, 11, 0, 0], [0, 0, 0, 11, 0], [2, 2, 2, 2, 2], [15, 14, 15, 15, 16], [8, 9, 6, 6, 8], [20, 0, 19, 20, 0], [0, 10, 12, 9, 0], [0, 2, 0, 0, 0], [0, 3, 0, 3, 0], [14, 0, 0, 18, 19], [12, 14, 11, 13, 0], [20, 0, 18, 18, 20], [10, 10, 13, 0, 13], [0, 2, 2, 2, 2], [6, 6, 5, 5, 4], [0, 3, 4, 5, 3], [5, 6, 5, 6, 6], [18, 0, 17, 20, 18], [17, 17, 18, 0, 0], [2, 4, 2, 4, 4], [18, 19, 16, 18, 0], [14, 15, 15, 16, 15], [2, 2, 2, 2, 2], [16, 14, 16, 19, 0], [10, 8, 0, 6, 0], [0, 19, 0, 0, 0], [6, 9, 7, 0, 6], [0, 0, 11, 0, 0], [0, 16, 16, 13, 17], [0, 0, 8, 8, 0], [4, 5, 0, 0, 0], [6, 6, 5, 5, 5], [20, 16, 19, 16, 20], [0, 0, 0, 0, 12], [16, 16, 18, 19, 18], [0, 8, 7, 6, 0], [13, 0, 13, 0, 0], [6, 0, 0, 8, 7], [9, 8, 8, 7, 10], [13, 10, 0, 0, 0], [10, 6, 9, 7, 6], [0, 0, 15, 0, 0], [8, 0, 8, 7, 6], [13, 14, 13, 15, 13], [17, 17, 18, 16, 16]], "n_jobs": 10, "n_machines": 5, "n_operations": 58, "data_source": "SD1"}, {"instance_id": "SD1_0038", "job_length": [1, 7, 5, 9, 10, 7, 8, 9, 4, 8], "processing_times": [[7, 5, 0, 5, 7], [0, 0, 0, 11, 0], [19, 15, 20, 15, 14], [7, 0, 0, 0, 0], [11, 8, 10, 8, 8], [6, 8, 10, 7, 8], [0, 0, 14, 0, 0], [20, 15, 15, 17, 18], [4, 4, 0, 0, 6], [11, 11, 11, 0, 0], [0, 0, 1, 0, 0], [14, 0, 0, 0, 16], [13, 14, 12, 13, 14], [0, 19, 19, 18, 19], [2, 2, 2, 2, 2], [8, 8, 8, 6, 8], [0, 0, 16, 15, 13], [0, 5, 0, 0, 0], [6, 4, 6, 4, 0], [10, 7, 9, 7, 9], [7, 0, 0, 0, 7], [0, 8, 0, 0, 6], [12, 9, 12, 12, 12], [0, 19, 0, 0, 0], [0, 0, 0, 5, 0], [3, 4, 0, 0, 3], [4, 0, 0, 4, 0], [0, 0, 16, 0, 0], [0, 17, 17, 20, 0], [0, 0, 4, 0, 5], [0, 0, 0, 0, 20], [0, 0, 7, 0, 0], [12, 11, 14, 14, 16], [11, 0, 11, 0, 16], [2, 2, 2, 2, 0], [9, 10, 12, 12, 8], [14, 15, 0, 0, 0], [14, 16, 16, 15, 13], [0, 0, 16, 0, 0], [0, 18, 0, 16, 0], [17, 18, 0, 0, 0], [16, 16, 14, 17, 17], [0, 18, 18, 18, 0], [2, 2, 2, 0, 2], [0, 14, 0, 10, 14], [14, 10, 11, 14, 0], [7, 0, 0, 6, 7], [2, 2, 2, 2, 2], [7, 0, 7, 5, 7], [0, 0, 14, 10, 12], [13, 11, 11, 10, 11], [15, 0, 0, 0, 12], [13, 17, 17, 17, 17], [16, 0, 17, 16, 17], [10, 10, 0, 0, 9], [5, 0, 4, 5, 5], [0, 0, 4, 0, 0], [9, 8, 9, 10, 10], [0, 6, 5, 0, 5], [0, 0, 2, 2, 0], [0, 0, 16, 12, 17], [6, 5, 4, 4, 4], [20, 0, 0, 0, 0], [0, 0, 11, 8, 0], [14, 0, 19, 20, 19], [15, 18, 15, 12, 15], [0, 5, 0, 0, 0], [9, 11, 11, 13, 11]], "n_jobs": 10, "n_machines": 5, "n_operations": 68, "data_source": "SD1"}, {"instance_id": "SD1_0039", "job_length": [1, 3, 3, 2, 4, 9, 5, 2, 5, 2], "processing_times": [[3, 4, 4, 3, 2], [7, 6, 6, 0, 7], [2, 0, 0, 2, 0], [12, 10, 14, 11, 0], [7, 7, 6, 10, 8], [1, 0, 1, 1, 1], [1, 1, 1, 1, 1], [2, 0, 0, 2, 2], [0, 0, 11, 12, 10], [0, 0, 0, 0, 4], [10, 0, 9, 8, 11], [0, 0, 6, 0, 0], [0, 4, 4, 6, 0], [2, 2, 0, 0, 2], [17, 0, 18, 18, 20], [18, 0, 0, 0, 0], [17, 0, 0, 0, 20], [0, 0, 0, 0, 2], [13, 0, 15, 17, 14], [6, 8, 0, 0, 0], [13, 0, 13, 9, 9], [14, 0, 16, 0, 15], [12, 0, 0, 0, 14], [0, 17, 16, 15, 15], [0, 0, 13, 0, 16], [18, 0, 14, 16, 13], [9, 0, 0, 10, 0], [16, 0, 17, 16, 0], [2, 2, 2, 2, 0], [13, 11, 15, 0, 17], [8, 0, 0, 9, 0], [0, 0, 0, 1, 0], [14, 16, 0, 0, 15], [6, 4, 5, 6, 0], [17, 0, 0, 0, 20], [9, 9, 10, 11, 9]], "n_jobs": 10, "n_machines": 5, "n_operations": 36, "data_source": "SD1"}, {"instance_id": "SD1_0040", "job_length": [7, 4, 9, 7, 10, 4, 7, 6, 10, 5], "processing_times": [[2, 4, 4, 4, 0], [0, 20, 0, 18, 0], [0, 19, 18, 20, 19], [11, 12, 16, 11, 10], [16, 0, 0, 0, 0], [11, 7, 8, 11, 11], [9, 0, 0, 0, 0], [7, 0, 8, 0, 8], [0, 2, 2, 2, 2], [0, 12, 13, 11, 17], [15, 16, 0, 18, 0], [0, 11, 0, 0, 0], [0, 0, 6, 5, 4], [0, 13, 16, 0, 0], [0, 0, 19, 17, 16], [0, 8, 0, 11, 0], [18, 20, 17, 18, 18], [11, 11, 0, 12, 9], [0, 0, 15, 19, 15], [18, 14, 18, 15, 14], [15, 0, 0, 0, 0], [9, 0, 0, 0, 0], [8, 7, 8, 6, 8], [8, 0, 0, 0, 0], [15, 17, 18, 16, 15], [6, 0, 0, 0, 0], [8, 10, 10, 10, 8], [0, 1, 0, 1, 0], [9, 11, 0, 10, 9], [10, 10, 0, 0, 10], [0, 0, 1, 0, 0], [0, 13, 0, 0, 0], [13, 11, 0, 12, 14], [0, 0, 1, 1, 1], [0, 15, 16, 0, 0], [0, 7, 0, 0, 0], [0, 0, 0, 0, 14], [1, 1, 1, 1, 1], [15, 14, 15, 17, 14], [0, 0, 18, 0, 19], [17, 14, 16, 18, 0], [1, 1, 1, 0, 0], [0, 0, 7, 6, 0], [6, 0, 7, 6, 6], [18, 17, 18, 18, 16], [0, 2, 0, 0, 0], [10, 10, 7, 11, 7], [0, 17, 0, 0, 18], [4, 0, 3, 5, 0], [0, 0, 0, 0, 6], [0, 9, 0, 0, 10], [0, 0, 13, 0, 0], [12, 0, 12, 9, 11], [11, 10, 7, 7, 11], [0, 1, 0, 1, 0], [0, 5, 0, 4, 5], [15, 0, 0, 15, 0], [0, 12, 12, 0, 9], [13, 10, 12, 13, 12], [7, 0, 10, 6, 10], [9, 12, 11, 13, 11], [0, 0, 17, 0, 13], [0, 0, 2, 2, 0], [0, 10, 14, 12, 0], [0, 0, 0, 20, 17], [0, 0, 0, 7, 0], [0, 1, 0, 1, 0], [0, 9, 10, 0, 0], [0, 0, 0, 0, 9]], "n_jobs": 10, "n_machines": 5, "n_operations": 69, "data_source": "SD1"}, {"instance_id": "SD1_0041", "job_length": [9, 10, 7, 1, 10, 1, 6, 8, 1, 6], "processing_times": [[0, 0, 5, 5, 6], [0, 9, 0, 8, 9], [0, 0, 0, 11, 0], [0, 16, 0, 0, 0], [0, 0, 8, 0, 0], [17, 0, 20, 18, 0], [0, 2, 0, 0, 0], [17, 17, 14, 19, 18], [19, 17, 17, 16, 16], [7, 6, 9, 0, 9], [3, 0, 0, 4, 2], [20, 0, 19, 0, 0], [0, 0, 0, 12, 0], [0, 2, 0, 0, 0], [11, 0, 0, 12, 0], [9, 0, 9, 7, 0], [0, 0, 12, 11, 12], [17, 17, 16, 17, 16], [0, 20, 19, 0, 0], [0, 16, 0, 0, 16], [4, 4, 0, 5, 6], [0, 15, 0, 15, 0], [9, 0, 8, 0, 10], [7, 8, 6, 7, 6], [0, 0, 0, 1, 0], [13, 0, 0, 0, 0], [0, 15, 0, 17, 0], [6, 0, 0, 0, 0], [15, 11, 14, 0, 0], [0, 7, 6, 7, 6], [15, 0, 10, 0, 14], [1, 1, 0, 0, 1], [18, 16, 0, 15, 15], [0, 8, 0, 0, 0], [0, 0, 1, 0, 1], [0, 0, 0, 1, 0], [16, 17, 14, 19, 0], [2, 2, 0, 0, 0], [4, 5, 4, 3, 5], [0, 13, 15, 11, 17], [0, 0, 11, 0, 12], [2, 2, 2, 0, 3], [0, 19, 0, 0, 16], [0, 9, 0, 0, 8], [0, 8, 0, 0, 7], [8, 0, 0, 0, 0], [1, 1, 1, 0, 0], [0, 0, 0, 11, 0], [0, 14, 0, 0, 13], [15, 18, 0, 18, 19], [0, 0, 11, 0, 0], [0, 0, 0, 5, 0], [0, 19, 18, 15, 13], [11, 0, 15, 0, 15], [0, 6, 0, 6, 0], [0, 3, 2, 3, 0], [0, 0, 16, 0, 0], [0, 0, 15, 0, 0], [7, 0, 7, 6, 6]], "n_jobs": 10, "n_machines": 5, "n_operations": 59, "data_source": "SD1"}, {"instance_id": "SD1_0042", "job_length": [3, 3, 8, 10, 5, 7, 3, 9, 1, 2], "processing_times": [[10, 6, 6, 6, 9], [0, 12, 12, 10, 13], [14, 0, 16, 0, 14], [10, 0, 11, 9, 9], [7, 9, 9, 6, 10], [13, 10, 10, 12, 0], [2, 4, 2, 0, 0], [0, 0, 0, 19, 0], [14, 11, 0, 13, 0], [2, 2, 2, 2, 0], [7, 5, 7, 0, 6], [0, 0, 20, 0, 0], [0, 18, 0, 0, 0], [0, 0, 6, 0, 0], [13, 0, 0, 0, 16], [0, 4, 0, 0, 0], [0, 0, 4, 3, 4], [11, 10, 13, 14, 13], [0, 0, 0, 7, 9], [12, 10, 9, 10, 10], [11, 11, 0, 0, 0], [0, 10, 6, 10, 6], [18, 14, 19, 0, 0], [19, 0, 17, 16, 19], [0, 7, 0, 0, 7], [10, 0, 12, 0, 0], [10, 10, 12, 0, 12], [12, 0, 0, 10, 13], [0, 7, 5, 6, 5], [1, 1, 1, 1, 1], [10, 10, 0, 9, 13], [12, 10, 14, 13, 0], [0, 11, 13, 12, 16], [0, 11, 0, 13, 0], [0, 0, 10, 0, 0], [0, 6, 0, 0, 0], [17, 19, 0, 17, 0], [0, 0, 16, 18, 0], [7, 0, 0, 0, 0], [13, 10, 14, 10, 13], [0, 8, 0, 0, 8], [0, 0, 12, 0, 9], [14, 18, 16, 14, 12], [2, 2, 0, 2, 2], [0, 0, 10, 0, 0], [5, 4, 4, 3, 0], [15, 17, 20, 15, 15], [18, 16, 19, 16, 13], [14, 11, 13, 11, 14], [13, 0, 15, 12, 15], [0, 16, 0, 15, 17]], "n_jobs": 10, "n_machines": 5, "n_operations": 51, "data_source": "SD1"}, {"instance_id": "SD1_0043", "job_length": [5, 9, 9, 5, 4, 8, 8, 9, 6, 1], "processing_times": [[10, 6, 0, 6, 0], [6, 8, 0, 0, 8], [11, 0, 16, 13, 16], [0, 16, 17, 0, 14], [13, 14, 12, 11, 12], [17, 0, 17, 0, 0], [0, 0, 0, 13, 0], [13, 19, 19, 0, 15], [2, 3, 0, 0, 2], [7, 8, 0, 6, 7], [7, 0, 0, 7, 7], [2, 2, 2, 2, 2], [0, 0, 15, 0, 14], [0, 12, 0, 0, 0], [3, 0, 0, 0, 0], [0, 0, 4, 0, 5], [6, 6, 6, 6, 6], [13, 13, 17, 19, 14], [5, 0, 4, 5, 4], [0, 19, 0, 0, 0], [5, 3, 3, 0, 5], [18, 14, 18, 13, 19], [10, 0, 0, 0, 0], [5, 6, 5, 0, 0], [0, 0, 0, 16, 0], [1, 0, 1, 1, 1], [0, 0, 0, 17, 12], [5, 4, 4, 4, 0], [2, 2, 2, 2, 2], [10, 12, 10, 12, 11], [11, 9, 0, 11, 9], [8, 0, 0, 8, 0], [17, 16, 16, 0, 17], [14, 15, 0, 0, 0], [7, 0, 6, 5, 0], [14, 12, 14, 12, 16], [0, 0, 5, 0, 0], [0, 0, 8, 7, 10], [19, 19, 20, 0, 17], [0, 16, 0, 0, 0], [0, 0, 0, 0, 1], [16, 0, 16, 19, 0], [2, 0, 0, 2, 4], [0, 2, 0, 2, 2], [0, 0, 0, 0, 6], [0, 0, 0, 5, 0], [0, 20, 14, 0, 18], [12, 0, 0, 0, 11], [0, 4, 0, 0, 0], [0, 8, 0, 0, 0], [0, 1, 1, 1, 1], [0, 4, 0, 0, 0], [4, 2, 4, 4, 4], [0, 0, 5, 4, 0], [0, 0, 12, 12, 0], [0, 0, 1, 0, 0], [8, 8, 8, 6, 7], [0, 0, 9, 7, 0], [14, 0, 0, 0, 0], [13, 12, 15, 17, 13], [0, 15, 14, 17, 0], [9, 11, 0, 0, 10], [8, 12, 8, 11, 8], [16, 17, 0, 20, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 64, "data_source": "SD1"}, {"instance_id": "SD1_0044", "job_length": [5, 4, 6, 2, 8, 3, 6, 2, 5, 3], "processing_times": [[20, 17, 17, 0, 19], [0, 10, 11, 10, 8], [2, 2, 2, 2, 0], [9, 10, 0, 0, 6], [5, 5, 5, 6, 6], [6, 0, 0, 6, 6], [0, 8, 0, 0, 0], [0, 16, 0, 15, 0], [5, 6, 6, 4, 6], [18, 15, 16, 14, 0], [14, 10, 15, 16, 14], [0, 3, 5, 3, 4], [0, 13, 12, 14, 0], [0, 0, 0, 0, 4], [0, 2, 2, 2, 0], [17, 16, 18, 19, 14], [0, 0, 0, 0, 6], [6, 5, 5, 5, 5], [0, 1, 0, 0, 0], [16, 17, 20, 19, 18], [0, 0, 15, 0, 0], [12, 13, 10, 9, 13], [0, 13, 0, 18, 0], [3, 2, 3, 3, 4], [0, 7, 0, 7, 0], [6, 0, 5, 7, 0], [6, 7, 7, 10, 9], [16, 20, 18, 20, 19], [0, 0, 9, 0, 0], [0, 0, 0, 0, 18], [8, 0, 0, 0, 0], [18, 0, 0, 0, 0], [0, 11, 8, 0, 0], [17, 0, 0, 15, 0], [0, 8, 0, 0, 0], [5, 5, 3, 0, 4], [4, 0, 0, 0, 0], [16, 16, 0, 14, 20], [11, 8, 8, 12, 9], [12, 10, 11, 11, 9], [0, 1, 1, 1, 1], [8, 7, 7, 8, 6], [5, 7, 6, 5, 5], [0, 7, 0, 0, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 44, "data_source": "SD1"}, {"instance_id": "SD1_0045", "job_length": [6, 5, 7, 7, 1, 1, 9, 4, 6, 4], "processing_times": [[0, 0, 0, 0, 13], [10, 7, 8, 11, 9], [20, 19, 20, 0, 18], [0, 0, 0, 0, 13], [0, 0, 0, 0, 13], [8, 0, 0, 0, 7], [19, 16, 0, 16, 20], [0, 12, 9, 11, 11], [0, 0, 9, 0, 0], [6, 6, 8, 6, 7], [15, 12, 16, 0, 0], [0, 18, 0, 0, 16], [17, 15, 13, 14, 13], [12, 12, 11, 10, 10], [0, 0, 10, 0, 8], [0, 0, 0, 0, 4], [0, 0, 16, 19, 0], [6, 0, 7, 0, 0], [20, 19, 16, 0, 20], [0, 1, 0, 1, 0], [0, 10, 0, 9, 0], [0, 0, 0, 13, 0], [0, 18, 0, 0, 0], [15, 0, 0, 20, 17], [10, 0, 0, 12, 0], [3, 0, 0, 0, 0], [8, 11, 0, 8, 11], [13, 15, 18, 13, 13], [0, 0, 1, 1, 0], [19, 0, 0, 17, 0], [3, 3, 5, 3, 3], [3, 4, 2, 0, 2], [12, 11, 10, 11, 8], [0, 16, 0, 16, 18], [0, 0, 0, 0, 9], [15, 12, 0, 12, 12], [0, 0, 0, 15, 12], [10, 0, 0, 10, 11], [0, 2, 0, 0, 2], [10, 11, 10, 10, 14], [0, 15, 0, 0, 0], [3, 5, 4, 5, 4], [0, 15, 16, 10, 12], [0, 0, 7, 0, 0], [13, 15, 14, 14, 10], [3, 0, 0, 5, 0], [0, 19, 17, 16, 19], [4, 4, 5, 5, 4], [0, 0, 7, 7, 6], [13, 0, 11, 0, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 50, "data_source": "SD1"}, {"instance_id": "SD1_0046", "job_length": [2, 8, 1, 5, 2, 8, 6, 1, 7, 10], "processing_times": [[0, 11, 10, 13, 12], [12, 0, 10, 10, 0], [0, 17, 11, 15, 17], [11, 9, 7, 7, 8], [6, 0, 0, 0, 7], [13, 0, 0, 17, 0], [19, 17, 17, 19, 17], [19, 0, 0, 0, 0], [0, 0, 0, 0, 18], [14, 0, 0, 0, 0], [0, 0, 17, 19, 15], [0, 0, 2, 0, 0], [17, 14, 17, 15, 17], [6, 8, 7, 6, 6], [2, 2, 4, 2, 3], [9, 10, 11, 7, 8], [12, 14, 12, 13, 12], [1, 1, 1, 1, 1], [12, 17, 17, 18, 16], [5, 0, 4, 0, 5], [14, 13, 14, 17, 14], [19, 17, 0, 0, 0], [16, 17, 20, 0, 16], [0, 12, 0, 9, 0], [20, 18, 0, 0, 14], [0, 4, 5, 4, 0], [0, 0, 16, 0, 0], [17, 16, 18, 20, 0], [19, 13, 17, 17, 0], [12, 10, 10, 10, 9], [1, 1, 1, 0, 1], [10, 0, 0, 0, 0], [0, 0, 0, 0, 5], [14, 16, 15, 17, 13], [7, 0, 7, 0, 8], [12, 11, 13, 11, 14], [7, 8, 0, 6, 8], [7, 6, 0, 0, 8], [18, 0, 0, 19, 15], [12, 12, 10, 14, 11], [0, 6, 0, 6, 0], [0, 2, 3, 4, 4], [5, 6, 4, 4, 6], [0, 12, 9, 0, 0], [2, 0, 2, 0, 0], [18, 19, 17, 16, 19], [11, 11, 9, 12, 12], [0, 0, 16, 0, 0], [0, 7, 0, 0, 6], [13, 11, 12, 13, 9]], "n_jobs": 10, "n_machines": 5, "n_operations": 50, "data_source": "SD1"}, {"instance_id": "SD1_0047", "job_length": [5, 1, 9, 7, 6, 6, 10, 5, 7, 6], "processing_times": [[0, 9, 0, 0, 9], [0, 0, 0, 12, 0], [0, 18, 0, 0, 19], [0, 0, 0, 0, 16], [0, 0, 12, 0, 0], [17, 14, 18, 18, 17], [0, 0, 0, 8, 10], [17, 0, 0, 0, 15], [0, 3, 0, 0, 3], [8, 0, 0, 0, 0], [0, 0, 6, 0, 4], [0, 7, 8, 7, 0], [0, 0, 7, 7, 0], [14, 14, 14, 17, 16], [8, 0, 0, 0, 0], [7, 9, 0, 10, 8], [4, 0, 0, 0, 0], [13, 13, 0, 14, 13], [0, 13, 15, 15, 18], [1, 1, 1, 1, 0], [14, 14, 0, 0, 0], [2, 0, 2, 2, 0], [17, 0, 14, 15, 15], [0, 13, 18, 14, 0], [3, 4, 0, 3, 5], [17, 14, 0, 0, 14], [0, 0, 7, 7, 0], [17, 19, 19, 0, 19], [16, 10, 11, 10, 10], [4, 4, 0, 4, 3], [0, 0, 0, 16, 0], [10, 0, 9, 0, 10], [0, 0, 0, 0, 6], [0, 0, 14, 17, 14], [15, 20, 17, 0, 19], [1, 1, 1, 0, 1], [14, 14, 10, 0, 11], [0, 0, 19, 0, 19], [18, 16, 19, 16, 0], [0, 0, 0, 4, 0], [0, 0, 0, 0, 14], [0, 0, 0, 2, 2], [0, 4, 0, 0, 4], [0, 0, 0, 16, 0], [13, 0, 13, 13, 0], [0, 0, 0, 0, 18], [0, 0, 14, 16, 14], [0, 0, 15, 18, 20], [0, 10, 0, 0, 0], [0, 13, 0, 0, 14], [0, 12, 0, 0, 0], [0, 9, 10, 9, 10], [4, 0, 0, 0, 4], [20, 0, 0, 0, 0], [0, 0, 0, 9, 0], [0, 15, 0, 0, 0], [12, 0, 13, 0, 0], [0, 8, 0, 0, 0], [17, 18, 17, 19, 0], [7, 0, 0, 0, 7], [6, 8, 8, 6, 7], [0, 0, 10, 9, 0]], "n_jobs": 10, "n_machines": 5, "n_operations": 62, "data_source": "SD1"}, {"instance_id": "SD1_0048", "job_length": [7, 10, 1, 8, 9, 8, 4, 5, 5, 4], "processing_times": [[0, 2, 0, 2, 2], [6, 4, 5, 4, 5], [14, 10, 10, 14, 11], [0, 1, 1, 1, 1], [11, 14, 0, 0, 0], [15, 0, 16, 18, 19], [0, 1, 1, 0, 1], [0, 6, 8, 0, 8], [1, 0, 1, 0, 1], [0, 11, 14, 0, 0], [4, 4, 3, 5, 4], [4, 5, 6, 4, 0], [0, 0, 16, 14, 16], [0, 0, 0, 4, 0], [10, 10, 0, 8, 7], [6, 0, 0, 7, 10], [0, 0, 10, 11, 12], [11, 8, 0, 10, 8], [0, 0, 20, 0, 17], [16, 0, 17, 19, 0], [0, 3, 4, 5, 4], [0, 15, 0, 0, 0], [0, 15, 0, 12, 12], [4, 4, 0, 4, 5], [0, 0, 10, 9, 0], [18, 16, 15, 15, 15], [11, 0, 0, 0, 0], [5, 4, 5, 4, 0], [2, 0, 0, 0, 0], [17, 17, 17, 19, 0], [0, 8, 0, 0, 0], [0, 0, 19, 17, 16], [0, 0, 0, 2, 0], [13, 15, 17, 17, 16], [12, 11, 14, 10, 14], [0, 0, 0, 16, 0], [0, 0, 2, 0, 0], [7, 7, 6, 7, 0], [0, 19, 13, 15, 0], [13, 17, 14, 0, 13], [10, 0, 10, 12, 0], [0, 7, 0, 0, 6], [2, 0, 0, 0, 0], [0, 19, 16, 19, 0], [0, 0, 4, 0, 0], [0, 0, 0, 10, 0], [0, 12, 0, 12, 16], [9, 0, 0, 10, 11], [16, 0, 12, 0, 0], [14, 11, 0, 0, 12], [4, 5, 0, 4, 0], [11, 0, 0, 0, 12], [0, 6, 7, 7, 10], [19, 0, 0, 0, 0], [19, 20, 15, 15, 19], [16, 15, 10, 14, 10], [0, 18, 19, 0, 19], [5, 4, 0, 0, 5], [0, 19, 16, 0, 0], [5, 7, 7, 7, 5], [10, 12, 11, 0, 12]], "n_jobs": 10, "n_machines": 5, "n_operations": 61, "data_source": "SD1"}, {"instance_id": "SD1_0049", "job_length": [5, 6, 8, 1, 7, 1, 4, 1, 6, 4], "processing_times": [[0, 0, 0, 0, 6], [2, 0, 0, 0, 0], [14, 12, 11, 11, 0], [14, 0, 0, 11, 0], [0, 0, 0, 0, 6], [0, 14, 10, 14, 0], [2, 2, 2, 2, 2], [14, 0, 0, 0, 0], [20, 20, 18, 17, 19], [15, 15, 14, 15, 20], [0, 4, 6, 6, 4], [12, 11, 13, 16, 17], [9, 0, 0, 0, 0], [0, 0, 18, 0, 0], [17, 0, 0, 18, 0], [0, 2, 0, 0, 0], [3, 5, 0, 0, 0], [8, 0, 8, 0, 0], [16, 16, 0, 20, 15], [14, 0, 13, 15, 11], [13, 12, 0, 12, 0], [1, 1, 1, 1, 1], [14, 14, 13, 16, 14], [0, 0, 2, 2, 2], [2, 4, 4, 2, 4], [14, 0, 0, 0, 0], [1, 0, 0, 0, 0], [0, 11, 0, 12, 0], [15, 15, 14, 12, 0], [6, 7, 7, 7, 6], [0, 11, 0, 0, 0], [13, 0, 13, 9, 0], [0, 15, 0, 0, 17], [13, 11, 0, 10, 12], [13, 0, 14, 14, 16], [2, 2, 2, 2, 2], [0, 17, 0, 0, 0], [7, 0, 6, 0, 0], [20, 17, 17, 15, 17], [4, 0, 4, 0, 5], [17, 18, 14, 19, 16], [7, 7, 8, 0, 0], [0, 0, 10, 11, 9]], "n_jobs": 10, "n_machines": 5, "n_operations": 43, "data_source": "SD1"}], "solutions": [{"instance_id": "SD1_0000", "method": "SPT", "schedule": [[16, 0], [46, 1], [47, 3], [53, 0], [0, 1], [1, 4], [10, 2], [2, 4], [3, 2], [11, 4], [12, 4], [37, 3], [31, 3], [63, 3], [22, 2], [23, 2], [48, 0], [17, 3]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0000", "method": "FIFO", "schedule": [[0, 1], [1, 4], [2, 4], [3, 2], [4, 2], [5, 1], [6, 0], [7, 1], [8, 4], [9, 0], [10, 2], [11, 4], [12, 4], [13, 2], [14, 2], [15, 4], [16, 0], [17, 3], [18, 2], [19, 0], [20, 1], [21, 0], [22, 2], [23, 2], [24, 1], [25, 1], [26, 0], [27, 1], [28, 3], [29, 0], [30, 1], [31, 3], [32, 2], [33, 0], [34, 2], [35, 1], [36, 0], [37, 3], [38, 0], [39, 0], [40, 0], [41, 3], [42, 1], [43, 1], [44, 0], [45, 0], [46, 1], [47, 3], [48, 0], [49, 1], [50, 0], [51, 4], [52, 0], [53, 0], [54, 3], [55, 3], [56, 0], [57, 1], [58, 0], [59, 3], [60, 0], [61, 1], [62, 1], [63, 3], [64, 0], [65, 3], [66, 2], [67, 0], [68, 1], [69, 4], [70, 0], [71, 4], [72, 3]], "makespan": 514.0, "feasible": true, "quality_score": 0.16286644951140067}, {"instance_id": "SD1_0001", "method": "SPT", "schedule": [[11, 2], [23, 1], [44, 1], [51, 3], [45, 1], [50, 0], [17, 1], [27, 2], [12, 1], [3, 2], [24, 4], [0, 0], [28, 2], [36, 2]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0001", "method": "FIFO", "schedule": [[0, 0], [1, 0], [2, 4], [3, 2], [4, 3], [5, 1], [6, 0], [7, 4], [8, 2], [9, 2], [10, 2], [11, 2], [12, 1], [13, 0], [14, 0], [15, 2], [16, 3], [17, 1], [18, 4], [19, 0], [20, 0], [21, 1], [22, 0], [23, 1], [24, 4], [25, 3], [26, 2], [27, 2], [28, 2], [29, 3], [30, 2], [31, 3], [32, 1], [33, 0], [34, 0], [35, 1], [36, 2], [37, 4], [38, 1], [39, 2], [40, 3], [41, 2], [42, 3], [43, 3], [44, 1], [45, 1], [46, 0], [47, 0], [48, 2], [49, 2], [50, 0], [51, 3], [52, 3], [53, 2], [54, 1], [55, 1], [56, 1], [57, 2], [58, 0], [59, 2], [60, 2]], "makespan": 472.0, "feasible": true, "quality_score": 0.17482517482517484}, {"instance_id": "SD1_0002", "method": "SPT", "schedule": [[0, 0], [26, 2], [23, 3], [21, 3], [22, 1], [6, 0], [9, 1], [19, 1], [1, 0], [7, 3], [27, 4], [18, 2], [20, 0], [24, 3], [2, 4]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0002", "method": "FIFO", "schedule": [[0, 0], [1, 0], [2, 4], [3, 1], [4, 1], [5, 3], [6, 0], [7, 3], [8, 0], [9, 1], [10, 0], [11, 1], [12, 1], [13, 4], [14, 2], [15, 1], [16, 1], [17, 2], [18, 2], [19, 1], [20, 0], [21, 3], [22, 1], [23, 3], [24, 3], [25, 2], [26, 2], [27, 4]], "makespan": 172.0, "feasible": true, "quality_score": 0.36764705882352944}, {"instance_id": "SD1_0003", "method": "SPT", "schedule": [[46, 0], [12, 3], [38, 0], [2, 1], [39, 1], [0, 1], [15, 3], [30, 1], [51, 1], [16, 1], [1, 3], [40, 2], [47, 3], [13, 1], [3, 0], [22, 2], [27, 2], [52, 3]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0003", "method": "FIFO", "schedule": [[0, 1], [1, 3], [2, 1], [3, 0], [4, 3], [5, 4], [6, 2], [7, 0], [8, 1], [9, 4], [10, 2], [11, 3], [12, 3], [13, 1], [14, 0], [15, 3], [16, 1], [17, 2], [18, 2], [19, 1], [20, 3], [21, 3], [22, 2], [23, 2], [24, 2], [25, 2], [26, 2], [27, 2], [28, 2], [29, 1], [30, 1], [31, 0], [32, 1], [33, 1], [34, 3], [35, 3], [36, 0], [37, 1], [38, 0], [39, 1], [40, 2], [41, 2], [42, 4], [43, 4], [44, 1], [45, 1], [46, 0], [47, 3], [48, 4], [49, 2], [50, 4], [51, 1], [52, 3]], "makespan": 368.0, "feasible": true, "quality_score": 0.2136752136752137}, {"instance_id": "SD1_0004", "method": "SPT", "schedule": [[22, 1], [46, 2], [27, 0], [13, 4], [35, 2], [39, 1], [14, 1], [0, 4], [5, 1], [6, 2], [15, 0], [28, 0], [7, 4], [16, 0], [47, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0004", "method": "FIFO", "schedule": [[0, 4], [1, 3], [2, 0], [3, 1], [4, 4], [5, 1], [6, 2], [7, 4], [8, 4], [9, 3], [10, 1], [11, 0], [12, 2], [13, 4], [14, 1], [15, 0], [16, 0], [17, 0], [18, 0], [19, 0], [20, 0], [21, 0], [22, 1], [23, 1], [24, 2], [25, 2], [26, 1], [27, 0], [28, 0], [29, 3], [30, 0], [31, 1], [32, 3], [33, 4], [34, 3], [35, 2], [36, 3], [37, 0], [38, 2], [39, 1], [40, 0], [41, 1], [42, 0], [43, 0], [44, 2], [45, 3], [46, 2], [47, 1], [48, 3], [49, 2], [50, 0], [51, 0]], "makespan": 420.0, "feasible": true, "quality_score": 0.1923076923076923}, {"instance_id": "SD1_0005", "method": "SPT", "schedule": [[26, 1], [45, 0], [57, 4], [58, 0], [59, 1], [0, 2], [47, 4], [48, 1], [37, 2], [1, 4], [46, 3], [32, 0], [33, 1], [27, 2], [34, 2], [60, 2], [16, 2], [49, 2], [10, 0]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0005", "method": "FIFO", "schedule": [[0, 2], [1, 4], [2, 0], [3, 0], [4, 4], [5, 1], [6, 0], [7, 3], [8, 2], [9, 1], [10, 0], [11, 2], [12, 0], [13, 0], [14, 1], [15, 3], [16, 2], [17, 1], [18, 1], [19, 0], [20, 4], [21, 2], [22, 0], [23, 0], [24, 4], [25, 3], [26, 1], [27, 2], [28, 1], [29, 0], [30, 0], [31, 3], [32, 0], [33, 1], [34, 2], [35, 2], [36, 4], [37, 2], [38, 0], [39, 1], [40, 1], [41, 4], [42, 3], [43, 0], [44, 0], [45, 0], [46, 3], [47, 4], [48, 1], [49, 2], [50, 3], [51, 0], [52, 4], [53, 3], [54, 1], [55, 0], [56, 1], [57, 4], [58, 0], [59, 1], [60, 2], [61, 2]], "makespan": 411.0, "feasible": true, "quality_score": 0.19569471624266144}, {"instance_id": "SD1_0006", "method": "SPT", "schedule": [[7, 0], [8, 0], [38, 3], [33, 3], [48, 0], [39, 2], [29, 1], [30, 0], [17, 3], [40, 1], [0, 3], [41, 1], [21, 1], [49, 1], [53, 1], [9, 2]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0006", "method": "FIFO", "schedule": [[0, 3], [1, 4], [2, 4], [3, 4], [4, 0], [5, 4], [6, 0], [7, 0], [8, 0], [9, 2], [10, 4], [11, 3], [12, 4], [13, 3], [14, 1], [15, 1], [16, 3], [17, 3], [18, 1], [19, 1], [20, 2], [21, 1], [22, 4], [23, 0], [24, 0], [25, 1], [26, 2], [27, 2], [28, 2], [29, 1], [30, 0], [31, 0], [32, 2], [33, 3], [34, 0], [35, 2], [36, 2], [37, 0], [38, 3], [39, 2], [40, 1], [41, 1], [42, 1], [43, 1], [44, 2], [45, 2], [46, 3], [47, 1], [48, 0], [49, 1], [50, 4], [51, 1], [52, 2], [53, 1], [54, 0], [55, 0], [56, 4], [57, 3], [58, 1], [59, 3], [60, 3], [61, 1]], "makespan": 490.0, "feasible": true, "quality_score": 0.1694915254237288}, {"instance_id": "SD1_0007", "method": "SPT", "schedule": [[0, 3], [31, 0], [38, 1], [1, 1], [2, 2], [44, 0], [35, 3], [13, 1], [41, 0], [5, 2], [39, 3], [42, 2], [32, 1], [21, 0]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0007", "method": "FIFO", "schedule": [[0, 3], [1, 1], [2, 2], [3, 1], [4, 0], [5, 2], [6, 1], [7, 0], [8, 2], [9, 3], [10, 2], [11, 2], [12, 0], [13, 1], [14, 2], [15, 4], [16, 1], [17, 0], [18, 3], [19, 1], [20, 0], [21, 0], [22, 1], [23, 1], [24, 4], [25, 4], [26, 1], [27, 3], [28, 2], [29, 0], [30, 3], [31, 0], [32, 1], [33, 0], [34, 1], [35, 3], [36, 1], [37, 4], [38, 1], [39, 3], [40, 4], [41, 0], [42, 2], [43, 1], [44, 0], [45, 4], [46, 0], [47, 1], [48, 3], [49, 0]], "makespan": 370.0, "feasible": true, "quality_score": 0.2127659574468085}, {"instance_id": "SD1_0008", "method": "SPT", "schedule": [[28, 1], [39, 0], [47, 3], [0, 3], [8, 2], [31, 0], [40, 1], [33, 0], [1, 3], [17, 0], [19, 3], [29, 0], [30, 1], [32, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0008", "method": "FIFO", "schedule": [[0, 3], [1, 3], [2, 0], [3, 0], [4, 4], [5, 2], [6, 2], [7, 1], [8, 2], [9, 0], [10, 0], [11, 3], [12, 0], [13, 3], [14, 0], [15, 1], [16, 3], [17, 0], [18, 0], [19, 3], [20, 1], [21, 0], [22, 0], [23, 3], [24, 0], [25, 3], [26, 1], [27, 4], [28, 1], [29, 0], [30, 1], [31, 0], [32, 1], [33, 0], [34, 0], [35, 4], [36, 2], [37, 4], [38, 0], [39, 0], [40, 1], [41, 1], [42, 3], [43, 1], [44, 2], [45, 4], [46, 1], [47, 3], [48, 0], [49, 1], [50, 0], [51, 2], [52, 4], [53, 4], [54, 0], [55, 0]], "makespan": 452.0, "feasible": true, "quality_score": 0.1811594202898551}, {"instance_id": "SD1_0009", "method": "SPT", "schedule": [[26, 0], [4, 3], [27, 1], [6, 0], [5, 0], [28, 4], [40, 3], [10, 2], [1, 1], [2, 1], [29, 0], [30, 1], [35, 2], [0, 0], [20, 1], [11, 0], [41, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0009", "method": "FIFO", "schedule": [[0, 0], [1, 1], [2, 1], [3, 2], [4, 3], [5, 0], [6, 0], [7, 1], [8, 0], [9, 0], [10, 2], [11, 0], [12, 4], [13, 2], [14, 3], [15, 1], [16, 4], [17, 1], [18, 4], [19, 3], [20, 1], [21, 1], [22, 4], [23, 2], [24, 1], [25, 0], [26, 0], [27, 1], [28, 4], [29, 0], [30, 1], [31, 3], [32, 1], [33, 2], [34, 3], [35, 2], [36, 1], [37, 1], [38, 2], [39, 0], [40, 3], [41, 1], [42, 1], [43, 4], [44, 0]], "makespan": 311.0, "feasible": true, "quality_score": 0.24330900243309006}, {"instance_id": "SD1_0010", "method": "SPT", "schedule": [[31, 0], [21, 2], [24, 1], [13, 4], [55, 0], [32, 0], [0, 0], [4, 4], [25, 1], [45, 1], [26, 4], [36, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0010", "method": "FIFO", "schedule": [[0, 0], [1, 0], [2, 0], [3, 0], [4, 4], [5, 1], [6, 0], [7, 1], [8, 3], [9, 2], [10, 0], [11, 0], [12, 4], [13, 4], [14, 2], [15, 1], [16, 2], [17, 1], [18, 2], [19, 3], [20, 3], [21, 2], [22, 4], [23, 0], [24, 1], [25, 1], [26, 4], [27, 0], [28, 3], [29, 2], [30, 1], [31, 0], [32, 0], [33, 2], [34, 0], [35, 1], [36, 1], [37, 1], [38, 3], [39, 2], [40, 1], [41, 0], [42, 2], [43, 0], [44, 4], [45, 1], [46, 0], [47, 2], [48, 3], [49, 1], [50, 1], [51, 0], [52, 3], [53, 0], [54, 1], [55, 0], [56, 3], [57, 0], [58, 3], [59, 2], [60, 2], [61, 1], [62, 0], [63, 1]], "makespan": 477.0, "feasible": true, "quality_score": 0.1733102253032929}, {"instance_id": "SD1_0011", "method": "SPT", "schedule": [[0, 1], [65, 0], [24, 0], [47, 0], [31, 1], [49, 4], [9, 2], [18, 3], [48, 3], [66, 0], [10, 0], [50, 0], [56, 1], [11, 3], [57, 0], [1, 1], [41, 0], [51, 2], [32, 4]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0011", "method": "FIFO", "schedule": [[0, 1], [1, 1], [2, 0], [3, 2], [4, 2], [5, 1], [6, 3], [7, 1], [8, 3], [9, 2], [10, 0], [11, 3], [12, 3], [13, 2], [14, 0], [15, 0], [16, 3], [17, 2], [18, 3], [19, 4], [20, 3], [21, 4], [22, 4], [23, 4], [24, 0], [25, 1], [26, 1], [27, 1], [28, 0], [29, 4], [30, 0], [31, 1], [32, 4], [33, 0], [34, 1], [35, 4], [36, 0], [37, 1], [38, 4], [39, 2], [40, 2], [41, 0], [42, 0], [43, 0], [44, 1], [45, 0], [46, 4], [47, 0], [48, 3], [49, 4], [50, 0], [51, 2], [52, 1], [53, 1], [54, 3], [55, 4], [56, 1], [57, 0], [58, 3], [59, 2], [60, 1], [61, 0], [62, 0], [63, 1], [64, 1], [65, 0], [66, 0], [67, 1], [68, 1], [69, 4]], "makespan": 548.0, "feasible": true, "quality_score": 0.15432098765432098}, {"instance_id": "SD1_0012", "method": "SPT", "schedule": [[16, 0], [31, 0], [0, 1], [10, 0], [32, 4], [41, 2], [42, 0], [19, 1], [49, 1], [33, 0], [47, 4], [17, 3], [50, 1], [11, 0], [22, 2]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0012", "method": "FIFO", "schedule": [[0, 1], [1, 0], [2, 1], [3, 3], [4, 3], [5, 2], [6, 0], [7, 4], [8, 0], [9, 3], [10, 0], [11, 0], [12, 4], [13, 0], [14, 0], [15, 4], [16, 0], [17, 3], [18, 4], [19, 1], [20, 4], [21, 4], [22, 2], [23, 1], [24, 2], [25, 2], [26, 4], [27, 3], [28, 2], [29, 1], [30, 1], [31, 0], [32, 4], [33, 0], [34, 1], [35, 3], [36, 4], [37, 1], [38, 0], [39, 1], [40, 4], [41, 2], [42, 0], [43, 2], [44, 0], [45, 2], [46, 0], [47, 4], [48, 1], [49, 1], [50, 1], [51, 2], [52, 0], [53, 1], [54, 2], [55, 0], [56, 4]], "makespan": 403.0, "feasible": true, "quality_score": 0.19880715705765406}, {"instance_id": "SD1_0013", "method": "SPT", "schedule": [[1, 1], [2, 0], [9, 0], [19, 4], [37, 0], [0, 1], [8, 1], [10, 1], [3, 0], [29, 0], [30, 2], [48, 4], [38, 0], [42, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0013", "method": "FIFO", "schedule": [[0, 1], [1, 1], [2, 0], [3, 0], [4, 0], [5, 0], [6, 2], [7, 2], [8, 1], [9, 0], [10, 1], [11, 4], [12, 3], [13, 0], [14, 0], [15, 0], [16, 3], [17, 4], [18, 0], [19, 4], [20, 0], [21, 1], [22, 3], [23, 0], [24, 2], [25, 1], [26, 4], [27, 3], [28, 4], [29, 0], [30, 2], [31, 1], [32, 1], [33, 1], [34, 4], [35, 0], [36, 0], [37, 0], [38, 0], [39, 3], [40, 4], [41, 1], [42, 1], [43, 1], [44, 3], [45, 0], [46, 2], [47, 1], [48, 4], [49, 2], [50, 3], [51, 0], [52, 1], [53, 2], [54, 1], [55, 1]], "makespan": 398.0, "feasible": true, "quality_score": 0.2008032128514056}, {"instance_id": "SD1_0014", "method": "SPT", "schedule": [[63, 2], [54, 3], [39, 3], [55, 1], [56, 1], [49, 1], [10, 1], [35, 0], [15, 1], [50, 3], [57, 4], [0, 3], [58, 3], [36, 2], [27, 0], [59, 3], [19, 0]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0014", "method": "FIFO", "schedule": [[0, 3], [1, 0], [2, 0], [3, 0], [4, 1], [5, 2], [6, 2], [7, 2], [8, 4], [9, 1], [10, 1], [11, 2], [12, 4], [13, 3], [14, 3], [15, 1], [16, 2], [17, 2], [18, 1], [19, 0], [20, 1], [21, 1], [22, 0], [23, 1], [24, 0], [25, 0], [26, 1], [27, 0], [28, 0], [29, 0], [30, 4], [31, 0], [32, 3], [33, 2], [34, 0], [35, 0], [36, 2], [37, 0], [38, 0], [39, 3], [40, 0], [41, 2], [42, 2], [43, 2], [44, 3], [45, 0], [46, 0], [47, 3], [48, 4], [49, 1], [50, 3], [51, 1], [52, 3], [53, 0], [54, 3], [55, 1], [56, 1], [57, 4], [58, 3], [59, 3], [60, 2], [61, 2], [62, 1], [63, 2]], "makespan": 446.0, "feasible": true, "quality_score": 0.18315018315018314}, {"instance_id": "SD1_0015", "method": "SPT", "schedule": [[15, 1], [41, 0], [22, 1], [10, 0], [36, 0], [0, 1], [27, 1], [7, 4], [37, 4], [29, 1], [16, 2], [38, 0], [42, 0]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0015", "method": "FIFO", "schedule": [[0, 1], [1, 2], [2, 2], [3, 1], [4, 2], [5, 3], [6, 0], [7, 4], [8, 0], [9, 0], [10, 0], [11, 1], [12, 2], [13, 3], [14, 4], [15, 1], [16, 2], [17, 4], [18, 0], [19, 0], [20, 0], [21, 1], [22, 1], [23, 0], [24, 0], [25, 3], [26, 1], [27, 1], [28, 0], [29, 1], [30, 2], [31, 4], [32, 3], [33, 3], [34, 2], [35, 4], [36, 0], [37, 4], [38, 0], [39, 4], [40, 4], [41, 0], [42, 0]], "makespan": 374.0, "feasible": true, "quality_score": 0.21097046413502107}, {"instance_id": "SD1_0016", "method": "SPT", "schedule": [[40, 0], [27, 0], [7, 0], [8, 1], [28, 2], [29, 4], [31, 0], [38, 2], [26, 2], [30, 0], [0, 3], [9, 3], [16, 4], [41, 4], [45, 3]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0016", "method": "FIFO", "schedule": [[0, 3], [1, 0], [2, 4], [3, 1], [4, 1], [5, 0], [6, 3], [7, 0], [8, 1], [9, 3], [10, 1], [11, 0], [12, 2], [13, 1], [14, 1], [15, 3], [16, 4], [17, 2], [18, 4], [19, 0], [20, 0], [21, 1], [22, 0], [23, 0], [24, 1], [25, 4], [26, 2], [27, 0], [28, 2], [29, 4], [30, 0], [31, 0], [32, 2], [33, 2], [34, 0], [35, 3], [36, 1], [37, 3], [38, 2], [39, 4], [40, 0], [41, 4], [42, 3], [43, 0], [44, 0], [45, 3], [46, 1], [47, 4], [48, 0], [49, 2], [50, 3], [51, 3], [52, 2], [53, 0], [54, 1]], "makespan": 376.0, "feasible": true, "quality_score": 0.21008403361344538}, {"instance_id": "SD1_0017", "method": "SPT", "schedule": [[0, 3], [30, 0], [31, 1], [24, 0], [32, 0], [5, 2], [15, 2], [39, 1], [16, 2], [23, 4], [25, 1], [1, 4], [33, 0], [56, 1], [17, 1], [37, 0], [40, 0], [46, 1], [41, 2], [2, 1], [26, 0]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0017", "method": "FIFO", "schedule": [[0, 3], [1, 4], [2, 1], [3, 1], [4, 1], [5, 2], [6, 2], [7, 3], [8, 0], [9, 0], [10, 0], [11, 3], [12, 0], [13, 0], [14, 4], [15, 2], [16, 2], [17, 1], [18, 1], [19, 4], [20, 2], [21, 0], [22, 0], [23, 4], [24, 0], [25, 1], [26, 0], [27, 2], [28, 3], [29, 0], [30, 0], [31, 1], [32, 0], [33, 0], [34, 2], [35, 4], [36, 2], [37, 0], [38, 0], [39, 1], [40, 0], [41, 2], [42, 1], [43, 0], [44, 4], [45, 1], [46, 1], [47, 0], [48, 4], [49, 1], [50, 0], [51, 2], [52, 0], [53, 0], [54, 0], [55, 4], [56, 1], [57, 1], [58, 0], [59, 0], [60, 0], [61, 4], [62, 1], [63, 3]], "makespan": 448.0, "feasible": true, "quality_score": 0.18248175182481752}, {"instance_id": "SD1_0018", "method": "SPT", "schedule": [[16, 0], [61, 2], [40, 4], [41, 0], [49, 3], [42, 3], [50, 3], [62, 0], [17, 4], [51, 2], [25, 4], [31, 2], [32, 1], [63, 3], [7, 0], [0, 1], [33, 3], [34, 3], [58, 1], [35, 0], [36, 3]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0018", "method": "FIFO", "schedule": [[0, 1], [1, 4], [2, 2], [3, 3], [4, 0], [5, 1], [6, 4], [7, 0], [8, 1], [9, 4], [10, 2], [11, 1], [12, 0], [13, 1], [14, 4], [15, 1], [16, 0], [17, 4], [18, 0], [19, 1], [20, 2], [21, 1], [22, 4], [23, 2], [24, 1], [25, 4], [26, 4], [27, 0], [28, 4], [29, 1], [30, 0], [31, 2], [32, 1], [33, 3], [34, 3], [35, 0], [36, 3], [37, 4], [38, 0], [39, 2], [40, 4], [41, 0], [42, 3], [43, 3], [44, 4], [45, 3], [46, 0], [47, 2], [48, 1], [49, 3], [50, 3], [51, 2], [52, 2], [53, 4], [54, 1], [55, 0], [56, 0], [57, 1], [58, 1], [59, 0], [60, 1], [61, 2], [62, 0], [63, 3], [64, 1]], "makespan": 511.0, "feasible": true, "quality_score": 0.1636661211129296}, {"instance_id": "SD1_0019", "method": "SPT", "schedule": [[54, 2], [10, 0], [28, 2], [29, 0], [30, 2], [33, 4], [43, 0], [0, 2], [9, 0], [11, 0], [50, 3], [1, 0], [19, 0], [20, 0], [34, 2]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0019", "method": "FIFO", "schedule": [[0, 2], [1, 0], [2, 0], [3, 0], [4, 2], [5, 4], [6, 0], [7, 1], [8, 2], [9, 0], [10, 0], [11, 0], [12, 3], [13, 0], [14, 1], [15, 4], [16, 1], [17, 4], [18, 2], [19, 0], [20, 0], [21, 2], [22, 1], [23, 0], [24, 2], [25, 3], [26, 2], [27, 0], [28, 2], [29, 0], [30, 2], [31, 3], [32, 4], [33, 4], [34, 2], [35, 0], [36, 3], [37, 0], [38, 2], [39, 0], [40, 3], [41, 2], [42, 1], [43, 0], [44, 0], [45, 0], [46, 0], [47, 0], [48, 0], [49, 1], [50, 3], [51, 1], [52, 0], [53, 3], [54, 2], [55, 4], [56, 2], [57, 0], [58, 0], [59, 2], [60, 3], [61, 0]], "makespan": 488.0, "feasible": true, "quality_score": 0.17006802721088435}, {"instance_id": "SD1_0020", "method": "SPT", "schedule": [[3, 1], [24, 0], [13, 2], [25, 4], [26, 2], [18, 1], [19, 2], [14, 0], [1, 0], [8, 1], [27, 2], [11, 4], [33, 0], [0, 4], [28, 4], [34, 2], [2, 3]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0020", "method": "FIFO", "schedule": [[0, 4], [1, 0], [2, 3], [3, 1], [4, 0], [5, 1], [6, 0], [7, 0], [8, 1], [9, 4], [10, 3], [11, 4], [12, 1], [13, 2], [14, 0], [15, 0], [16, 0], [17, 4], [18, 1], [19, 2], [20, 1], [21, 1], [22, 0], [23, 0], [24, 0], [25, 4], [26, 2], [27, 2], [28, 4], [29, 1], [30, 4], [31, 4], [32, 2], [33, 0], [34, 2]], "makespan": 239.0, "feasible": true, "quality_score": 0.2949852507374631}, {"instance_id": "SD1_0021", "method": "SPT", "schedule": [[20, 1], [30, 0], [34, 0], [35, 4], [36, 0], [6, 1], [31, 4], [63, 1], [7, 3], [8, 1], [53, 2], [9, 4], [64, 2], [12, 4], [37, 4], [0, 2], [21, 0], [22, 2], [43, 1], [33, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0021", "method": "FIFO", "schedule": [[0, 2], [1, 2], [2, 0], [3, 2], [4, 4], [5, 1], [6, 1], [7, 3], [8, 1], [9, 4], [10, 2], [11, 1], [12, 4], [13, 4], [14, 3], [15, 4], [16, 4], [17, 1], [18, 1], [19, 2], [20, 1], [21, 0], [22, 2], [23, 2], [24, 2], [25, 4], [26, 3], [27, 1], [28, 3], [29, 1], [30, 0], [31, 4], [32, 1], [33, 1], [34, 0], [35, 4], [36, 0], [37, 4], [38, 0], [39, 1], [40, 3], [41, 3], [42, 3], [43, 1], [44, 2], [45, 3], [46, 0], [47, 0], [48, 1], [49, 1], [50, 0], [51, 4], [52, 1], [53, 2], [54, 4], [55, 2], [56, 1], [57, 0], [58, 0], [59, 0], [60, 0], [61, 4], [62, 1], [63, 1], [64, 2], [65, 4], [66, 3], [67, 3], [68, 2]], "makespan": 563.0, "feasible": true, "quality_score": 0.15082956259426847}, {"instance_id": "SD1_0022", "method": "SPT", "schedule": [[49, 3], [9, 3], [0, 3], [29, 3], [10, 4], [34, 1], [36, 3], [37, 0], [11, 3], [17, 0], [24, 1], [43, 3], [44, 3]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0022", "method": "FIFO", "schedule": [[0, 3], [1, 0], [2, 3], [3, 2], [4, 0], [5, 1], [6, 1], [7, 2], [8, 0], [9, 3], [10, 4], [11, 3], [12, 1], [13, 2], [14, 0], [15, 4], [16, 0], [17, 0], [18, 1], [19, 2], [20, 0], [21, 1], [22, 0], [23, 2], [24, 1], [25, 4], [26, 4], [27, 1], [28, 1], [29, 3], [30, 0], [31, 0], [32, 1], [33, 1], [34, 1], [35, 0], [36, 3], [37, 0], [38, 0], [39, 3], [40, 0], [41, 1], [42, 1], [43, 3], [44, 3], [45, 0], [46, 0], [47, 2], [48, 3], [49, 3], [50, 2], [51, 0], [52, 2], [53, 1], [54, 0], [55, 2], [56, 4], [57, 1], [58, 2]], "makespan": 446.0, "feasible": true, "quality_score": 0.18315018315018314}, {"instance_id": "SD1_0023", "method": "SPT", "schedule": [[18, 0], [19, 3], [15, 0], [36, 1], [16, 0], [30, 1], [43, 4], [34, 2], [26, 1], [35, 1], [0, 3], [6, 3], [20, 0], [17, 3]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0023", "method": "FIFO", "schedule": [[0, 3], [1, 1], [2, 1], [3, 2], [4, 1], [5, 2], [6, 3], [7, 1], [8, 3], [9, 2], [10, 3], [11, 4], [12, 3], [13, 0], [14, 2], [15, 0], [16, 0], [17, 3], [18, 0], [19, 3], [20, 0], [21, 2], [22, 1], [23, 3], [24, 0], [25, 0], [26, 1], [27, 0], [28, 2], [29, 1], [30, 1], [31, 4], [32, 1], [33, 0], [34, 2], [35, 1], [36, 1], [37, 0], [38, 3], [39, 1], [40, 0], [41, 0], [42, 4], [43, 4], [44, 0]], "makespan": 372.0, "feasible": true, "quality_score": 0.21186440677966098}, {"instance_id": "SD1_0024", "method": "SPT", "schedule": [[0, 1], [19, 3], [21, 3], [5, 1], [43, 0], [13, 0], [20, 4], [38, 2], [32, 3], [33, 4], [29, 0], [34, 2], [44, 1], [22, 4], [30, 3], [53, 0], [6, 2], [1, 0]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0024", "method": "FIFO", "schedule": [[0, 1], [1, 0], [2, 2], [3, 1], [4, 3], [5, 1], [6, 2], [7, 0], [8, 0], [9, 1], [10, 0], [11, 0], [12, 1], [13, 0], [14, 4], [15, 3], [16, 3], [17, 0], [18, 1], [19, 3], [20, 4], [21, 3], [22, 4], [23, 2], [24, 4], [25, 2], [26, 4], [27, 3], [28, 4], [29, 0], [30, 3], [31, 1], [32, 3], [33, 4], [34, 2], [35, 1], [36, 0], [37, 3], [38, 2], [39, 4], [40, 0], [41, 1], [42, 0], [43, 0], [44, 1], [45, 0], [46, 0], [47, 2], [48, 0], [49, 2], [50, 3], [51, 3], [52, 0], [53, 0], [54, 0], [55, 2], [56, 4], [57, 3]], "makespan": 468.0, "feasible": true, "quality_score": 0.17605633802816903}, {"instance_id": "SD1_0025", "method": "SPT", "schedule": [[10, 1], [23, 0], [28, 0], [29, 2], [43, 4], [11, 4], [33, 4], [14, 4], [24, 0], [0, 1], [30, 1], [37, 1], [44, 3], [45, 0]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0025", "method": "FIFO", "schedule": [[0, 1], [1, 4], [2, 0], [3, 0], [4, 3], [5, 1], [6, 2], [7, 2], [8, 1], [9, 0], [10, 1], [11, 4], [12, 0], [13, 0], [14, 4], [15, 1], [16, 4], [17, 2], [18, 4], [19, 0], [20, 0], [21, 4], [22, 0], [23, 0], [24, 0], [25, 4], [26, 4], [27, 3], [28, 0], [29, 2], [30, 1], [31, 3], [32, 3], [33, 4], [34, 2], [35, 3], [36, 0], [37, 1], [38, 1], [39, 2], [40, 0], [41, 0], [42, 1], [43, 4], [44, 3], [45, 0]], "makespan": 276.0, "feasible": true, "quality_score": 0.26595744680851063}, {"instance_id": "SD1_0026", "method": "SPT", "schedule": [[18, 0], [19, 2], [13, 0], [32, 2], [20, 4], [25, 2], [14, 4], [38, 0], [58, 3], [9, 3], [11, 3], [21, 4], [15, 1], [48, 1], [10, 4], [0, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0026", "method": "FIFO", "schedule": [[0, 1], [1, 0], [2, 0], [3, 3], [4, 2], [5, 1], [6, 0], [7, 4], [8, 2], [9, 3], [10, 4], [11, 3], [12, 3], [13, 0], [14, 4], [15, 1], [16, 3], [17, 1], [18, 0], [19, 2], [20, 4], [21, 4], [22, 1], [23, 1], [24, 0], [25, 2], [26, 1], [27, 0], [28, 1], [29, 0], [30, 4], [31, 2], [32, 2], [33, 0], [34, 4], [35, 2], [36, 3], [37, 3], [38, 0], [39, 2], [40, 4], [41, 0], [42, 2], [43, 2], [44, 1], [45, 0], [46, 0], [47, 2], [48, 1], [49, 0], [50, 0], [51, 3], [52, 3], [53, 0], [54, 2], [55, 4], [56, 3], [57, 0], [58, 3], [59, 4], [60, 3], [61, 0], [62, 3], [63, 2], [64, 0], [65, 1]], "makespan": 458.0, "feasible": true, "quality_score": 0.17921146953405018}, {"instance_id": "SD1_0027", "method": "SPT", "schedule": [[35, 0], [36, 2], [65, 1], [54, 4], [58, 3], [59, 1], [55, 2], [66, 0], [25, 1], [13, 1], [3, 2], [4, 1], [5, 3], [22, 4], [0, 0], [37, 2], [60, 1], [61, 2], [14, 3], [15, 0], [56, 0], [45, 1], [46, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0027", "method": "FIFO", "schedule": [[0, 0], [1, 2], [2, 2], [3, 2], [4, 1], [5, 3], [6, 4], [7, 2], [8, 3], [9, 1], [10, 3], [11, 1], [12, 0], [13, 1], [14, 3], [15, 0], [16, 1], [17, 4], [18, 3], [19, 0], [20, 3], [21, 1], [22, 4], [23, 2], [24, 1], [25, 1], [26, 0], [27, 1], [28, 3], [29, 0], [30, 0], [31, 2], [32, 0], [33, 1], [34, 1], [35, 0], [36, 2], [37, 2], [38, 4], [39, 1], [40, 0], [41, 0], [42, 4], [43, 2], [44, 4], [45, 1], [46, 1], [47, 3], [48, 0], [49, 4], [50, 0], [51, 0], [52, 2], [53, 1], [54, 4], [55, 2], [56, 0], [57, 0], [58, 3], [59, 1], [60, 1], [61, 2], [62, 2], [63, 2], [64, 0], [65, 1], [66, 0]], "makespan": 517.0, "feasible": true, "quality_score": 0.1620745542949757}, {"instance_id": "SD1_0028", "method": "SPT", "schedule": [[47, 0], [48, 1], [35, 2], [0, 3], [18, 0], [54, 1], [2, 1], [19, 2], [49, 4], [55, 0], [36, 1], [25, 4], [28, 3], [43, 0], [3, 4], [12, 0], [44, 0], [1, 4]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0028", "method": "FIFO", "schedule": [[0, 3], [1, 4], [2, 1], [3, 4], [4, 1], [5, 0], [6, 1], [7, 2], [8, 0], [9, 1], [10, 2], [11, 1], [12, 0], [13, 0], [14, 0], [15, 0], [16, 2], [17, 1], [18, 0], [19, 2], [20, 4], [21, 0], [22, 0], [23, 2], [24, 0], [25, 4], [26, 0], [27, 1], [28, 3], [29, 0], [30, 1], [31, 0], [32, 0], [33, 0], [34, 2], [35, 2], [36, 1], [37, 0], [38, 0], [39, 1], [40, 0], [41, 1], [42, 0], [43, 0], [44, 0], [45, 4], [46, 0], [47, 0], [48, 1], [49, 4], [50, 1], [51, 2], [52, 0], [53, 0], [54, 1], [55, 0], [56, 0], [57, 0], [58, 4]], "makespan": 438.0, "feasible": true, "quality_score": 0.1858736059479554}, {"instance_id": "SD1_0029", "method": "SPT", "schedule": [[43, 0], [16, 0], [4, 3], [35, 1], [51, 0], [19, 4], [0, 2], [25, 0], [44, 3], [45, 1], [52, 3], [53, 1], [8, 1], [36, 2], [11, 0], [12, 1], [13, 3], [20, 2], [21, 0], [17, 0], [26, 4]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0029", "method": "FIFO", "schedule": [[0, 2], [1, 2], [2, 2], [3, 4], [4, 3], [5, 4], [6, 1], [7, 0], [8, 1], [9, 4], [10, 3], [11, 0], [12, 1], [13, 3], [14, 2], [15, 1], [16, 0], [17, 0], [18, 3], [19, 4], [20, 2], [21, 0], [22, 4], [23, 2], [24, 4], [25, 0], [26, 4], [27, 2], [28, 4], [29, 0], [30, 0], [31, 0], [32, 2], [33, 1], [34, 1], [35, 1], [36, 2], [37, 1], [38, 2], [39, 4], [40, 2], [41, 3], [42, 1], [43, 0], [44, 3], [45, 1], [46, 0], [47, 0], [48, 0], [49, 3], [50, 3], [51, 0], [52, 3], [53, 1], [54, 0], [55, 2], [56, 0], [57, 4], [58, 2], [59, 1], [60, 3]], "makespan": 452.0, "feasible": true, "quality_score": 0.1811594202898551}, {"instance_id": "SD1_0030", "method": "SPT", "schedule": [[1, 3], [0, 1], [19, 4], [61, 1], [2, 4], [44, 1], [54, 1], [36, 4], [66, 0], [26, 4], [62, 1], [11, 2], [12, 3], [3, 2], [63, 3]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0030", "method": "FIFO", "schedule": [[0, 1], [1, 3], [2, 4], [3, 2], [4, 3], [5, 4], [6, 0], [7, 1], [8, 2], [9, 2], [10, 0], [11, 2], [12, 3], [13, 0], [14, 3], [15, 1], [16, 0], [17, 2], [18, 0], [19, 4], [20, 3], [21, 0], [22, 0], [23, 2], [24, 1], [25, 0], [26, 4], [27, 3], [28, 1], [29, 3], [30, 0], [31, 0], [32, 2], [33, 0], [34, 1], [35, 4], [36, 4], [37, 1], [38, 2], [39, 0], [40, 4], [41, 2], [42, 4], [43, 2], [44, 1], [45, 1], [46, 3], [47, 0], [48, 3], [49, 4], [50, 4], [51, 2], [52, 2], [53, 2], [54, 1], [55, 1], [56, 3], [57, 4], [58, 0], [59, 1], [60, 0], [61, 1], [62, 1], [63, 3], [64, 3], [65, 2], [66, 0], [67, 1], [68, 0], [69, 4], [70, 0], [71, 2]], "makespan": 487.0, "feasible": true, "quality_score": 0.17035775127768313}, {"instance_id": "SD1_0031", "method": "SPT", "schedule": [[35, 1], [46, 0], [14, 2], [24, 4], [45, 2], [0, 0], [47, 1], [15, 4], [36, 2], [48, 1], [1, 0], [37, 1], [8, 4], [2, 1], [16, 2], [25, 3], [26, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0031", "method": "FIFO", "schedule": [[0, 0], [1, 0], [2, 1], [3, 1], [4, 1], [5, 1], [6, 0], [7, 0], [8, 4], [9, 1], [10, 4], [11, 1], [12, 0], [13, 1], [14, 2], [15, 4], [16, 2], [17, 3], [18, 0], [19, 0], [20, 4], [21, 4], [22, 4], [23, 4], [24, 4], [25, 3], [26, 1], [27, 1], [28, 0], [29, 1], [30, 2], [31, 1], [32, 4], [33, 1], [34, 4], [35, 1], [36, 2], [37, 1], [38, 3], [39, 4], [40, 0], [41, 1], [42, 0], [43, 4], [44, 0], [45, 2], [46, 0], [47, 1], [48, 1]], "makespan": 297.0, "feasible": true, "quality_score": 0.2518891687657431}, {"instance_id": "SD1_0032", "method": "SPT", "schedule": [[15, 1], [16, 0], [39, 0], [55, 2], [56, 0], [0, 2], [25, 1], [57, 2], [1, 0], [29, 4], [40, 1], [44, 2], [9, 0], [45, 3], [17, 4], [22, 0], [18, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0032", "method": "FIFO", "schedule": [[0, 2], [1, 0], [2, 0], [3, 3], [4, 1], [5, 1], [6, 0], [7, 1], [8, 0], [9, 0], [10, 4], [11, 0], [12, 1], [13, 0], [14, 0], [15, 1], [16, 0], [17, 4], [18, 1], [19, 1], [20, 3], [21, 1], [22, 0], [23, 4], [24, 2], [25, 1], [26, 0], [27, 0], [28, 0], [29, 4], [30, 0], [31, 0], [32, 3], [33, 3], [34, 2], [35, 0], [36, 2], [37, 0], [38, 1], [39, 0], [40, 1], [41, 1], [42, 0], [43, 0], [44, 2], [45, 3], [46, 4], [47, 0], [48, 3], [49, 3], [50, 4], [51, 1], [52, 1], [53, 4], [54, 2], [55, 2], [56, 0], [57, 2], [58, 3]], "makespan": 453.0, "feasible": true, "quality_score": 0.18083182640144665}, {"instance_id": "SD1_0033", "method": "SPT", "schedule": [[0, 4], [13, 0], [46, 1], [17, 1], [18, 3], [1, 0], [32, 2], [2, 3], [6, 1], [19, 3], [36, 2], [56, 2], [27, 2], [7, 0], [28, 4], [47, 2], [37, 0], [3, 4], [4, 3], [5, 1], [14, 1], [10, 4]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0033", "method": "FIFO", "schedule": [[0, 4], [1, 0], [2, 3], [3, 4], [4, 3], [5, 1], [6, 1], [7, 0], [8, 1], [9, 1], [10, 4], [11, 3], [12, 1], [13, 0], [14, 1], [15, 4], [16, 0], [17, 1], [18, 3], [19, 3], [20, 1], [21, 2], [22, 4], [23, 3], [24, 2], [25, 1], [26, 3], [27, 2], [28, 4], [29, 2], [30, 3], [31, 1], [32, 2], [33, 2], [34, 0], [35, 0], [36, 2], [37, 0], [38, 3], [39, 0], [40, 2], [41, 0], [42, 2], [43, 0], [44, 2], [45, 0], [46, 1], [47, 2], [48, 0], [49, 4], [50, 2], [51, 2], [52, 0], [53, 0], [54, 1], [55, 1], [56, 2]], "makespan": 382.0, "feasible": true, "quality_score": 0.20746887966804978}, {"instance_id": "SD1_0034", "method": "SPT", "schedule": [[10, 2], [24, 2], [25, 1], [48, 4], [26, 3], [11, 1], [16, 4], [33, 0], [40, 1], [44, 3], [17, 2], [59, 1], [56, 1], [34, 3], [12, 2], [0, 2]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0034", "method": "FIFO", "schedule": [[0, 2], [1, 4], [2, 2], [3, 4], [4, 2], [5, 4], [6, 4], [7, 1], [8, 2], [9, 0], [10, 2], [11, 1], [12, 2], [13, 1], [14, 2], [15, 1], [16, 4], [17, 2], [18, 1], [19, 3], [20, 4], [21, 0], [22, 0], [23, 4], [24, 2], [25, 1], [26, 3], [27, 1], [28, 2], [29, 0], [30, 0], [31, 2], [32, 2], [33, 0], [34, 3], [35, 1], [36, 2], [37, 3], [38, 1], [39, 0], [40, 1], [41, 1], [42, 3], [43, 0], [44, 3], [45, 1], [46, 1], [47, 1], [48, 4], [49, 1], [50, 2], [51, 0], [52, 2], [53, 2], [54, 0], [55, 1], [56, 1], [57, 1], [58, 0], [59, 1], [60, 1], [61, 4], [62, 3]], "makespan": 422.0, "feasible": true, "quality_score": 0.19157088122605365}, {"instance_id": "SD1_0035", "method": "SPT", "schedule": [[20, 1], [29, 0], [40, 4], [19, 0], [25, 3], [41, 4], [30, 2], [0, 0], [31, 4], [10, 3], [24, 4], [9, 2], [21, 0], [42, 0], [37, 1], [1, 3]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0035", "method": "FIFO", "schedule": [[0, 0], [1, 3], [2, 2], [3, 4], [4, 3], [5, 0], [6, 3], [7, 3], [8, 4], [9, 2], [10, 3], [11, 1], [12, 2], [13, 3], [14, 3], [15, 2], [16, 3], [17, 0], [18, 0], [19, 0], [20, 1], [21, 0], [22, 1], [23, 4], [24, 4], [25, 3], [26, 1], [27, 2], [28, 0], [29, 0], [30, 2], [31, 4], [32, 3], [33, 3], [34, 3], [35, 0], [36, 0], [37, 1], [38, 4], [39, 4], [40, 4], [41, 4], [42, 0], [43, 0], [44, 4], [45, 0], [46, 2]], "makespan": 299.0, "feasible": true, "quality_score": 0.2506265664160401}, {"instance_id": "SD1_0036", "method": "SPT", "schedule": [[39, 0], [16, 1], [17, 2], [0, 0], [40, 4], [41, 0], [14, 1], [29, 1], [47, 1], [6, 4], [18, 2], [30, 1], [22, 1], [42, 3], [1, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0036", "method": "FIFO", "schedule": [[0, 0], [1, 1], [2, 3], [3, 3], [4, 0], [5, 4], [6, 4], [7, 4], [8, 2], [9, 1], [10, 4], [11, 2], [12, 0], [13, 0], [14, 1], [15, 1], [16, 1], [17, 2], [18, 2], [19, 4], [20, 1], [21, 1], [22, 1], [23, 0], [24, 0], [25, 0], [26, 3], [27, 1], [28, 1], [29, 1], [30, 1], [31, 2], [32, 0], [33, 0], [34, 0], [35, 2], [36, 0], [37, 1], [38, 3], [39, 0], [40, 4], [41, 0], [42, 3], [43, 0], [44, 2], [45, 3], [46, 1], [47, 1], [48, 2], [49, 2], [50, 1], [51, 3], [52, 4]], "makespan": 493.0, "feasible": true, "quality_score": 0.16863406408094436}, {"instance_id": "SD1_0037", "method": "SPT", "schedule": [[26, 1], [8, 4], [27, 4], [43, 0], [44, 2], [37, 3], [9, 2], [0, 4], [10, 2], [52, 1], [1, 2], [49, 0], [16, 1], [36, 1], [45, 1], [38, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0037", "method": "FIFO", "schedule": [[0, 4], [1, 2], [2, 1], [3, 1], [4, 0], [5, 0], [6, 0], [7, 1], [8, 4], [9, 2], [10, 2], [11, 4], [12, 2], [13, 2], [14, 3], [15, 0], [16, 1], [17, 2], [18, 2], [19, 3], [20, 1], [21, 1], [22, 0], [23, 2], [24, 2], [25, 0], [26, 1], [27, 4], [28, 1], [29, 0], [30, 2], [31, 0], [32, 0], [33, 2], [34, 0], [35, 0], [36, 1], [37, 3], [38, 1], [39, 0], [40, 2], [41, 3], [42, 2], [43, 0], [44, 2], [45, 1], [46, 4], [47, 0], [48, 3], [49, 0], [50, 0], [51, 3], [52, 1], [53, 1], [54, 2], [55, 4], [56, 0], [57, 3]], "makespan": 380.0, "feasible": true, "quality_score": 0.20833333333333334}, {"instance_id": "SD1_0038", "method": "SPT", "schedule": [[47, 0], [8, 0], [56, 2], [0, 1], [48, 3], [57, 1], [22, 1], [49, 3], [50, 3], [1, 3], [9, 0], [32, 1], [33, 0], [51, 4], [60, 3], [52, 0], [2, 4], [39, 3], [53, 0], [40, 0], [13, 3], [23, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0038", "method": "FIFO", "schedule": [[0, 1], [1, 3], [2, 4], [3, 0], [4, 1], [5, 0], [6, 2], [7, 1], [8, 0], [9, 0], [10, 2], [11, 0], [12, 2], [13, 3], [14, 0], [15, 3], [16, 4], [17, 1], [18, 1], [19, 1], [20, 0], [21, 4], [22, 1], [23, 1], [24, 3], [25, 0], [26, 0], [27, 2], [28, 1], [29, 2], [30, 4], [31, 2], [32, 1], [33, 0], [34, 0], [35, 4], [36, 0], [37, 4], [38, 2], [39, 3], [40, 0], [41, 2], [42, 1], [43, 0], [44, 3], [45, 1], [46, 3], [47, 0], [48, 3], [49, 3], [50, 3], [51, 4], [52, 0], [53, 0], [54, 4], [55, 2], [56, 2], [57, 1], [58, 2], [59, 2], [60, 3], [61, 2], [62, 0], [63, 3], [64, 0], [65, 3], [66, 1], [67, 0]], "makespan": 491.0, "feasible": true, "quality_score": 0.1692047377326565}, {"instance_id": "SD1_0039", "method": "SPT", "schedule": [[0, 4], [7, 0], [13, 0], [9, 4], [1, 1], [4, 2], [10, 3], [8, 4], [29, 1], [22, 0], [23, 3], [27, 0], [14, 0], [34, 0], [15, 0]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0039", "method": "FIFO", "schedule": [[0, 4], [1, 1], [2, 0], [3, 1], [4, 2], [5, 0], [6, 0], [7, 0], [8, 4], [9, 4], [10, 3], [11, 2], [12, 1], [13, 0], [14, 0], [15, 0], [16, 0], [17, 4], [18, 0], [19, 0], [20, 3], [21, 0], [22, 0], [23, 3], [24, 2], [25, 4], [26, 0], [27, 0], [28, 0], [29, 1], [30, 0], [31, 3], [32, 0], [33, 1], [34, 0], [35, 0]], "makespan": 239.0, "feasible": true, "quality_score": 0.2949852507374631}, {"instance_id": "SD1_0040", "method": "SPT", "schedule": [[27, 1], [37, 0], [41, 0], [54, 1], [0, 0], [48, 2], [55, 3], [42, 3], [43, 0], [49, 4], [7, 0], [28, 0], [50, 1], [29, 0], [11, 1], [51, 2], [38, 1], [20, 0], [56, 0], [44, 4], [64, 4], [1, 3], [2, 2], [39, 2]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0040", "method": "FIFO", "schedule": [[0, 0], [1, 3], [2, 2], [3, 4], [4, 0], [5, 1], [6, 0], [7, 0], [8, 1], [9, 3], [10, 0], [11, 1], [12, 4], [13, 1], [14, 4], [15, 1], [16, 2], [17, 4], [18, 2], [19, 1], [20, 0], [21, 0], [22, 3], [23, 0], [24, 0], [25, 0], [26, 0], [27, 1], [28, 0], [29, 0], [30, 2], [31, 1], [32, 1], [33, 2], [34, 1], [35, 1], [36, 4], [37, 0], [38, 1], [39, 2], [40, 1], [41, 0], [42, 3], [43, 0], [44, 4], [45, 1], [46, 2], [47, 1], [48, 2], [49, 4], [50, 1], [51, 2], [52, 3], [53, 2], [54, 1], [55, 3], [56, 0], [57, 4], [58, 1], [59, 3], [60, 0], [61, 4], [62, 2], [63, 1], [64, 4], [65, 3], [66, 1], [67, 1], [68, 4]], "makespan": 476.0, "feasible": true, "quality_score": 0.1736111111111111}, {"instance_id": "SD1_0041", "method": "SPT", "schedule": [[37, 0], [38, 3], [0, 2], [9, 1], [27, 0], [44, 4], [1, 3], [45, 0], [2, 3], [28, 1], [39, 3], [40, 2], [53, 0], [52, 4], [26, 1], [3, 1], [19, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0041", "method": "FIFO", "schedule": [[0, 2], [1, 3], [2, 3], [3, 1], [4, 2], [5, 0], [6, 1], [7, 2], [8, 3], [9, 1], [10, 4], [11, 2], [12, 3], [13, 1], [14, 0], [15, 3], [16, 3], [17, 2], [18, 2], [19, 1], [20, 0], [21, 1], [22, 2], [23, 2], [24, 3], [25, 0], [26, 1], [27, 0], [28, 1], [29, 2], [30, 2], [31, 0], [32, 3], [33, 1], [34, 2], [35, 3], [36, 2], [37, 0], [38, 3], [39, 3], [40, 2], [41, 0], [42, 4], [43, 4], [44, 4], [45, 0], [46, 0], [47, 3], [48, 4], [49, 0], [50, 2], [51, 3], [52, 4], [53, 0], [54, 1], [55, 2], [56, 2], [57, 2], [58, 3]], "makespan": 427.0, "feasible": true, "quality_score": 0.18975332068311196}, {"instance_id": "SD1_0042", "method": "SPT", "schedule": [[29, 0], [6, 0], [0, 1], [24, 1], [3, 3], [30, 3], [1, 3], [25, 0], [26, 0], [27, 3], [31, 1], [39, 1], [32, 1], [33, 1], [48, 1], [49, 3], [14, 0], [2, 0], [50, 3], [36, 0], [7, 3]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0042", "method": "FIFO", "schedule": [[0, 1], [1, 3], [2, 0], [3, 3], [4, 3], [5, 1], [6, 0], [7, 3], [8, 1], [9, 0], [10, 1], [11, 2], [12, 1], [13, 2], [14, 0], [15, 1], [16, 3], [17, 1], [18, 3], [19, 2], [20, 0], [21, 2], [22, 1], [23, 3], [24, 1], [25, 0], [26, 0], [27, 3], [28, 2], [29, 0], [30, 3], [31, 1], [32, 1], [33, 1], [34, 2], [35, 1], [36, 0], [37, 2], [38, 0], [39, 1], [40, 1], [41, 4], [42, 4], [43, 0], [44, 2], [45, 3], [46, 0], [47, 4], [48, 1], [49, 3], [50, 3]], "makespan": 347.0, "feasible": true, "quality_score": 0.22371364653243844}, {"instance_id": "SD1_0043", "method": "SPT", "schedule": [[40, 4], [28, 0], [14, 0], [15, 2], [48, 1], [23, 0], [0, 1], [1, 0], [16, 0], [57, 3], [49, 1], [29, 0], [2, 0], [17, 0], [3, 4], [58, 0], [24, 3], [32, 1], [41, 0], [63, 0], [5, 0]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0043", "method": "FIFO", "schedule": [[0, 1], [1, 0], [2, 0], [3, 4], [4, 3], [5, 0], [6, 3], [7, 0], [8, 0], [9, 3], [10, 0], [11, 0], [12, 4], [13, 1], [14, 0], [15, 2], [16, 0], [17, 0], [18, 2], [19, 1], [20, 1], [21, 3], [22, 0], [23, 0], [24, 3], [25, 0], [26, 4], [27, 1], [28, 0], [29, 0], [30, 1], [31, 0], [32, 1], [33, 0], [34, 3], [35, 1], [36, 2], [37, 3], [38, 4], [39, 1], [40, 4], [41, 0], [42, 0], [43, 1], [44, 4], [45, 3], [46, 2], [47, 4], [48, 1], [49, 1], [50, 1], [51, 1], [52, 1], [53, 3], [54, 2], [55, 2], [56, 3], [57, 3], [58, 0], [59, 1], [60, 2], [61, 0], [62, 0], [63, 0]], "makespan": 434.0, "feasible": true, "quality_score": 0.18726591760299627}, {"instance_id": "SD1_0044", "method": "SPT", "schedule": [[36, 0], [17, 1], [25, 2], [5, 0], [26, 0], [41, 4], [6, 1], [34, 1], [28, 2], [9, 3], [15, 4], [37, 3], [7, 3], [27, 0], [0, 1], [29, 4]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0044", "method": "FIFO", "schedule": [[0, 1], [1, 4], [2, 0], [3, 4], [4, 0], [5, 0], [6, 1], [7, 3], [8, 3], [9, 3], [10, 1], [11, 1], [12, 2], [13, 4], [14, 1], [15, 4], [16, 4], [17, 1], [18, 1], [19, 0], [20, 2], [21, 3], [22, 1], [23, 1], [24, 1], [25, 2], [26, 0], [27, 0], [28, 2], [29, 4], [30, 0], [31, 0], [32, 2], [33, 3], [34, 1], [35, 2], [36, 0], [37, 3], [38, 1], [39, 4], [40, 1], [41, 4], [42, 0], [43, 1]], "makespan": 283.0, "feasible": true, "quality_score": 0.2610966057441253}, {"instance_id": "SD1_0045", "method": "SPT", "schedule": [[25, 0], [26, 0], [36, 4], [0, 4], [27, 0], [40, 1], [6, 1], [11, 4], [18, 2], [46, 3]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0045", "method": "FIFO", "schedule": [[0, 4], [1, 1], [2, 4], [3, 4], [4, 4], [5, 4], [6, 1], [7, 2], [8, 2], [9, 0], [10, 1], [11, 4], [12, 2], [13, 3], [14, 4], [15, 4], [16, 2], [17, 0], [18, 2], [19, 1], [20, 3], [21, 3], [22, 1], [23, 0], [24, 0], [25, 0], [26, 0], [27, 0], [28, 2], [29, 3], [30, 0], [31, 2], [32, 4], [33, 1], [34, 4], [35, 1], [36, 4], [37, 0], [38, 1], [39, 0], [40, 1], [41, 0], [42, 3], [43, 2], [44, 4], [45, 0], [46, 3], [47, 0], [48, 4], [49, 2]], "makespan": 393.0, "feasible": true, "quality_score": 0.20283975659229211}, {"instance_id": "SD1_0046", "method": "SPT", "schedule": [[11, 2], [32, 4], [40, 1], [0, 2], [1, 2], [2, 2], [16, 0], [18, 0], [33, 4], [12, 1], [10, 4], [26, 2], [27, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0046", "method": "FIFO", "schedule": [[0, 2], [1, 2], [2, 2], [3, 2], [4, 0], [5, 0], [6, 1], [7, 0], [8, 4], [9, 0], [10, 4], [11, 2], [12, 1], [13, 0], [14, 0], [15, 3], [16, 0], [17, 0], [18, 0], [19, 2], [20, 1], [21, 1], [22, 0], [23, 3], [24, 4], [25, 1], [26, 2], [27, 1], [28, 1], [29, 4], [30, 0], [31, 0], [32, 4], [33, 4], [34, 0], [35, 1], [36, 3], [37, 1], [38, 4], [39, 2], [40, 1], [41, 1], [42, 2], [43, 2], [44, 0], [45, 3], [46, 2], [47, 2], [48, 4], [49, 4]], "makespan": 417.0, "feasible": true, "quality_score": 0.19342359767891684}, {"instance_id": "SD1_0047", "method": "SPT", "schedule": [[15, 0], [6, 3], [0, 1], [28, 1], [1, 3], [56, 0], [44, 0], [49, 1], [5, 1], [22, 2], [7, 4], [34, 0], [2, 1], [45, 4]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0047", "method": "FIFO", "schedule": [[0, 1], [1, 3], [2, 1], [3, 4], [4, 2], [5, 1], [6, 3], [7, 4], [8, 1], [9, 0], [10, 4], [11, 1], [12, 2], [13, 0], [14, 0], [15, 0], [16, 0], [17, 0], [18, 1], [19, 0], [20, 0], [21, 0], [22, 2], [23, 1], [24, 0], [25, 1], [26, 2], [27, 0], [28, 1], [29, 4], [30, 3], [31, 2], [32, 4], [33, 2], [34, 0], [35, 0], [36, 2], [37, 2], [38, 1], [39, 3], [40, 4], [41, 3], [42, 1], [43, 3], [44, 0], [45, 4], [46, 2], [47, 2], [48, 1], [49, 1], [50, 1], [51, 1], [52, 0], [53, 0], [54, 3], [55, 1], [56, 0], [57, 1], [58, 0], [59, 0], [60, 0], [61, 3]], "makespan": 499.0, "feasible": true, "quality_score": 0.1669449081803005}, {"instance_id": "SD1_0048", "method": "SPT", "schedule": [[0, 1], [1, 1], [57, 1], [7, 1], [52, 1], [17, 1], [47, 0], [2, 1], [26, 0], [48, 2], [35, 3], [43, 2], [58, 2], [18, 4], [53, 0]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0048", "method": "FIFO", "schedule": [[0, 1], [1, 1], [2, 1], [3, 1], [4, 0], [5, 0], [6, 1], [7, 1], [8, 0], [9, 1], [10, 2], [11, 0], [12, 3], [13, 3], [14, 4], [15, 0], [16, 2], [17, 1], [18, 4], [19, 0], [20, 1], [21, 1], [22, 3], [23, 0], [24, 3], [25, 2], [26, 0], [27, 1], [28, 0], [29, 0], [30, 1], [31, 4], [32, 3], [33, 0], [34, 3], [35, 3], [36, 2], [37, 2], [38, 2], [39, 0], [40, 0], [41, 4], [42, 0], [43, 2], [44, 2], [45, 3], [46, 1], [47, 0], [48, 2], [49, 1], [50, 0], [51, 0], [52, 1], [53, 0], [54, 2], [55, 2], [56, 1], [57, 1], [58, 2], [59, 0], [60, 0]], "makespan": 456.0, "feasible": true, "quality_score": 0.1798561151079137}, {"instance_id": "SD1_0049", "method": "SPT", "schedule": [[39, 0], [0, 4], [5, 2], [33, 3], [11, 1], [19, 4], [27, 1], [20, 1], [28, 3], [34, 0], [40, 2], [32, 1]], "makespan": Infinity, "feasible": false, "quality_score": 0.0}, {"instance_id": "SD1_0049", "method": "FIFO", "schedule": [[0, 4], [1, 0], [2, 2], [3, 3], [4, 4], [5, 2], [6, 0], [7, 0], [8, 3], [9, 2], [10, 1], [11, 1], [12, 0], [13, 2], [14, 0], [15, 1], [16, 0], [17, 0], [18, 4], [19, 4], [20, 1], [21, 0], [22, 2], [23, 2], [24, 0], [25, 0], [26, 0], [27, 1], [28, 3], [29, 0], [30, 1], [31, 3], [32, 1], [33, 3], [34, 0], [35, 0], [36, 1], [37, 2], [38, 3], [39, 0], [40, 2], [41, 0], [42, 4]], "makespan": 286.0, "feasible": true, "quality_score": 0.2590673575129534}], "metadata": {"num_instances": 50, "num_solutions": 100, "data_source": "SD1", "generation_method": "DANIEL_consistent", "config": {"n_j": 10, "n_m": 5, "op_per_job_min": 1, "op_per_job_max": 10, "num_envs": 20, "data_source": "SD1", "op_per_job": 0, "op_per_mch_min": 1, "op_per_mch_max": 5}}}