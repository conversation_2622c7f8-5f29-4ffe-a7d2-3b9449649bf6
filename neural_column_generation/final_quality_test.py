#!/usr/bin/env python3
"""
最终质量测试
验证高质量模型的实际性能
"""

import os
import sys
import torch
import numpy as np
import time

# 添加路径
sys.path.append('..')
sys.path.append('.')

from fixed_machine_selection import ImprovedNeuralModel
from daniel_consistent_data_generator import DANIELConsistentDataGenerator, DANIELTrainingConfig


def load_high_quality_model():
    """加载高质量模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = ImprovedNeuralModel(d_model=128, n_heads=4, n_layers=3).to(device)
    
    # 加载权重
    model_path = "neural_column_generation/models/high_quality_best.pth"
    if os.path.exists(model_path):
        try:
            checkpoint = torch.load(model_path, map_location=device, weights_only=False)
            model.load_state_dict(checkpoint['model_state_dict'])
            model.eval()
            print(f"✅ High quality model loaded from {model_path}")
            print(f"📊 Best training loss: {checkpoint.get('best_loss', 'N/A')}")
            return model, device, checkpoint
        except Exception as e:
            print(f"❌ Failed to load model: {e}")
            return None, device, None
    else:
        print(f"❌ Model file not found: {model_path}")
        return None, device, None


def create_diagnostic_instance():
    """创建诊断实例"""
    # 使用之前的简单测试实例
    job_lengths = [2, 2, 2]
    processing_times = np.array([
        [10, 20, 30],  # Op 0: M0最优(10)
        [30, 10, 20],  # Op 1: M1最优(10)
        [20, 30, 10],  # Op 2: M2最优(10)
        [10, 20, 30],  # Op 3: M0最优(10)
        [30, 10, 20],  # Op 4: M1最优(10)
        [20, 30, 10],  # Op 5: M2最优(10)
    ])
    
    return {
        'instance_id': 'diagnostic_test',
        'job_length': job_lengths,
        'processing_times': processing_times.tolist(),
        'n_jobs': 3,
        'n_machines': 3,
        'n_operations': 6
    }


def extract_features_for_model(instance):
    """提取模型特征"""
    job_length = instance['job_length']
    processing_times = np.array(instance['processing_times'])
    n_jobs = instance['n_jobs']
    n_machines = instance['n_machines']
    n_operations = instance['n_operations']
    
    # 作业特征
    job_features = []
    for job_id, n_ops in enumerate(job_length):
        op_start = sum(job_length[:job_id])
        op_end = op_start + n_ops
        
        total_workload = 0
        for op_idx in range(op_start, op_end):
            if op_idx < len(processing_times):
                min_time = min(t for t in processing_times[op_idx] if t > 0)
                total_workload += min_time
        
        job_feat = [
            job_id / n_jobs, n_ops / n_operations, total_workload / 1000.0,
            n_ops / max(job_length), total_workload / (n_ops * 20),
            0, 0, 0, 0, 0
        ]
        job_features.append(job_feat)
    
    # 操作特征
    operation_features = []
    for op_id in range(n_operations):
        if op_id < len(processing_times):
            proc_times = processing_times[op_id]
            
            job_id = 0
            op_in_job = op_id
            for j, n_ops in enumerate(job_length):
                if op_in_job < n_ops:
                    job_id = j
                    break
                op_in_job -= n_ops
            
            valid_times = [t for t in proc_times if t > 0]
            min_time = min(valid_times) if valid_times else 0
            max_time = max(valid_times) if valid_times else 0
            avg_time = np.mean(valid_times) if valid_times else 0
            n_machines_available = len(valid_times)
            
            op_feat = [
                op_id / n_operations, job_id / n_jobs, op_in_job / max(job_length),
                min_time / 50.0, max_time / 50.0, avg_time / 50.0,
                n_machines_available / n_machines, (max_time - min_time) / 50.0,
                0, 0, 0, 0, 0, 0, 0
            ]
            operation_features.append(op_feat)
    
    # 机器特征
    machine_features = []
    for machine_id in range(n_machines):
        total_load = 0
        n_operations_available = 0
        
        for op_id in range(n_operations):
            if op_id < len(processing_times) and machine_id < len(processing_times[op_id]):
                proc_time = processing_times[op_id][machine_id]
                if proc_time > 0:
                    total_load += proc_time
                    n_operations_available += 1
        
        avg_load = total_load / max(1, n_operations_available)
        
        machine_feat = [
            machine_id / n_machines, total_load / 1000.0,
            n_operations_available / n_operations, avg_load / 50.0,
            0, 0, 0, 0
        ]
        machine_features.append(machine_feat)
    
    return {
        'job_features': torch.FloatTensor(job_features),
        'operation_features': torch.FloatTensor(operation_features),
        'machine_features': torch.FloatTensor(machine_features),
        'processing_matrix': torch.FloatTensor(processing_times),
        'job_lengths': job_length
    }


def evaluate_schedule_simple(schedule, processing_times):
    """简单评估调度"""
    if not schedule:
        return float('inf')
    
    machine_times = [0] * processing_times.shape[1]
    
    for op_id, machine_id in schedule:
        if (machine_id < processing_times.shape[1] and 
            op_id < processing_times.shape[0]):
            machine_times[machine_id] += processing_times[op_id, machine_id]
    
    return max(machine_times)


def test_high_quality_model():
    """测试高质量模型"""
    print("🎯 FINAL QUALITY TEST - High Quality Model")
    print("=" * 60)
    
    # 加载模型
    model, device, checkpoint = load_high_quality_model()
    
    if model is None:
        print("❌ Cannot proceed without model")
        return
    
    # 创建测试实例
    instance = create_diagnostic_instance()
    processing_times = np.array(instance['processing_times'])
    
    print(f"📊 Test Instance:")
    print(f"  Jobs: {instance['job_length']}")
    print(f"  Processing times:")
    for i, row in enumerate(processing_times):
        optimal_machine = np.argmin(row)
        print(f"    Op {i}: {row} → optimal M{optimal_machine}({row[optimal_machine]})")
    
    print(f"  Theoretical optimal makespan: 20")
    
    # 提取特征
    features = extract_features_for_model(instance)
    
    # 测试多次生成
    print(f"\n🧠 Testing high quality model (5 runs):")
    
    all_results = []
    
    for run in range(5):
        print(f"\n  Run {run + 1}:")
        
        start_time = time.time()
        
        try:
            with torch.no_grad():
                columns = model.generate_improved_columns(
                    features['job_features'].unsqueeze(0).to(device),
                    features['operation_features'].unsqueeze(0).to(device),
                    features['machine_features'].unsqueeze(0).to(device),
                    features['processing_matrix'].unsqueeze(0).to(device),
                    features['job_lengths'],
                    num_columns=3
                )
            
            generation_time = time.time() - start_time
            
            print(f"    Generated {len(columns)} columns in {generation_time:.3f}s")
            
            if columns:
                best_makespan = min(col.makespan for col in columns if col.feasible)
                avg_decision_quality = np.mean([col.decision_quality for col in columns if col.feasible])
                
                print(f"    Best makespan: {best_makespan}")
                print(f"    Avg decision quality: {avg_decision_quality:.3f}")
                
                # 分析决策质量
                for i, column in enumerate(columns):
                    if column.feasible:
                        correct_decisions = 0
                        total_decisions = len(column.schedule)
                        
                        for op_id, machine_id in column.schedule:
                            optimal_machine = np.argmin(processing_times[op_id])
                            if machine_id == optimal_machine:
                                correct_decisions += 1
                        
                        decision_accuracy = correct_decisions / total_decisions * 100
                        print(f"      Column {i+1}: makespan={column.makespan}, accuracy={decision_accuracy:.1f}%")
                
                all_results.append({
                    'makespan': best_makespan,
                    'decision_quality': avg_decision_quality,
                    'generation_time': generation_time
                })
            else:
                print(f"    ❌ No valid columns generated")
                all_results.append({
                    'makespan': float('inf'),
                    'decision_quality': 0.0,
                    'generation_time': generation_time
                })
                
        except Exception as e:
            print(f"    ❌ Error: {e}")
            all_results.append({
                'makespan': float('inf'),
                'decision_quality': 0.0,
                'generation_time': 0.0
            })
    
    # 综合分析
    print(f"\n📈 COMPREHENSIVE ANALYSIS:")
    print(f"=" * 40)
    
    valid_results = [r for r in all_results if r['makespan'] < float('inf')]
    
    if valid_results:
        avg_makespan = np.mean([r['makespan'] for r in valid_results])
        best_makespan = min([r['makespan'] for r in valid_results])
        avg_decision_quality = np.mean([r['decision_quality'] for r in valid_results])
        avg_time = np.mean([r['generation_time'] for r in valid_results])
        
        print(f"🎯 Performance Metrics:")
        print(f"  Success Rate: {len(valid_results)}/5 ({len(valid_results)/5*100:.1f}%)")
        print(f"  Best Makespan: {best_makespan}")
        print(f"  Average Makespan: {avg_makespan:.1f}")
        print(f"  Average Decision Quality: {avg_decision_quality:.3f}")
        print(f"  Average Generation Time: {avg_time:.3f}s")
        
        # 与理论最优比较
        theoretical_optimal = 20
        gap = (best_makespan - theoretical_optimal) / theoretical_optimal * 100
        print(f"  Gap to Optimal: {gap:.1f}%")
        
        # 质量评估
        if gap <= 5:
            print(f"  🏆 EXCELLENT: Very close to optimal!")
        elif gap <= 20:
            print(f"  😊 GOOD: Competitive performance")
        elif gap <= 50:
            print(f"  😐 FAIR: Reasonable performance")
        else:
            print(f"  ⚠️  POOR: Needs improvement")
        
        # 决策质量评估
        if avg_decision_quality >= 0.9:
            print(f"  🎯 DECISION QUALITY: EXCELLENT ({avg_decision_quality:.3f})")
        elif avg_decision_quality >= 0.7:
            print(f"  🎯 DECISION QUALITY: GOOD ({avg_decision_quality:.3f})")
        else:
            print(f"  🎯 DECISION QUALITY: NEEDS WORK ({avg_decision_quality:.3f})")
        
    else:
        print(f"❌ No valid results - model failed completely")
    
    # 与之前的对比
    print(f"\n📊 COMPARISON WITH PREVIOUS RESULTS:")
    print(f"  Previous neural makespan: 60 (200% gap)")
    if valid_results:
        print(f"  Current neural makespan: {best_makespan} ({gap:.1f}% gap)")
        improvement = (60 - best_makespan) / 60 * 100
        print(f"  Improvement: {improvement:.1f}%")
        
        if improvement > 50:
            print(f"  🎉 MAJOR IMPROVEMENT!")
        elif improvement > 20:
            print(f"  😊 SIGNIFICANT IMPROVEMENT")
        elif improvement > 0:
            print(f"  👍 SOME IMPROVEMENT")
        else:
            print(f"  😞 NO IMPROVEMENT")
    
    return all_results


def main():
    """主函数"""
    print("🎯 FINAL QUALITY TEST - HIGH QUALITY MODEL")
    print("=" * 70)
    
    results = test_high_quality_model()
    
    print(f"\n📋 QUALITY TEST COMPLETED")
    print(f"🎯 High quality model evaluation finished!")


if __name__ == "__main__":
    main()
