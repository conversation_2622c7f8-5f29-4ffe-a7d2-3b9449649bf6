#!/usr/bin/env python3
"""
确保输出有效调度的神经网络模型
基于约束感知的序列生成，确保满足FJSP的所有约束
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass


@dataclass
class ValidScheduleOutput:
    """有效调度输出"""
    schedule: List[Tuple[int, int]]  # [(operation_id, machine_id), ...]
    makespan: float
    feasible: bool
    quality_score: float
    constraint_violations: int = 0


class ConstraintAwareDecoder(nn.Module):
    """约束感知的解码器，确保生成有效调度"""
    
    def __init__(self, d_model=256, n_heads=8, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        
        # 操作选择网络
        self.operation_selector = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 1)
        )
        
        # 机器选择网络
        self.machine_selector = nn.Sequential(
            nn.Linear(d_model + 1, d_model // 2),  # +1 for processing time
            nn.ReLU(),
            nn.Dropout(dropout),
            nn.Linear(d_model // 2, 1)
        )
        
        # 全局状态编码器
        self.global_encoder = nn.Sequential(
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, d_model // 4)
        )
    
    def forward(self, operation_embeddings, machine_embeddings, processing_matrix, 
                job_lengths, max_steps=None):
        """
        约束感知的前向传播
        
        Args:
            operation_embeddings: [batch, n_ops, d_model]
            machine_embeddings: [batch, n_machines, d_model]
            processing_matrix: [batch, n_ops, n_machines]
            job_lengths: List[int] - 每个作业的操作数
            max_steps: 最大解码步数
        """
        batch_size = operation_embeddings.size(0)
        n_operations = operation_embeddings.size(1)
        n_machines = machine_embeddings.size(1)
        
        if max_steps is None:
            max_steps = n_operations
        
        # 生成有效调度序列
        schedules = []
        
        for b in range(batch_size):
            schedule = self._generate_valid_schedule(
                operation_embeddings[b],  # [n_ops, d_model]
                machine_embeddings[b],    # [n_machines, d_model]
                processing_matrix[b],     # [n_ops, n_machines]
                job_lengths,
                max_steps
            )
            schedules.append(schedule)
        
        return schedules
    
    def _generate_valid_schedule(self, op_embeddings, machine_embeddings, 
                               processing_matrix, job_lengths, max_steps):
        """生成单个有效调度"""
        n_operations = op_embeddings.size(0)
        n_machines = machine_embeddings.size(0)
        
        # 初始化调度状态
        schedule = []
        scheduled_ops = set()
        job_progress = [0] * len(job_lengths)
        
        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_lengths):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops
        
        # 逐步生成调度
        for step in range(max_steps):
            # 1. 找到所有可调度的操作（满足作业内顺序约束）
            available_operations = self._get_available_operations(
                job_operations, job_progress, scheduled_ops
            )
            
            if not available_operations:
                break  # 没有可调度的操作
            
            # 2. 使用神经网络选择操作
            selected_op = self._select_operation_with_nn(
                available_operations, op_embeddings, processing_matrix
            )
            
            # 3. 为选定操作选择机器
            selected_machine = self._select_machine_with_nn(
                selected_op, machine_embeddings, processing_matrix
            )
            
            # 4. 验证选择的有效性
            if self._is_valid_assignment(selected_op, selected_machine, processing_matrix):
                # 添加到调度中
                schedule.append((selected_op, selected_machine))
                scheduled_ops.add(selected_op)
                
                # 更新作业进度
                for job_id, ops in enumerate(job_operations):
                    if selected_op in ops:
                        job_progress[job_id] += 1
                        break
            else:
                # 如果神经网络选择无效，使用启发式回退
                fallback_machine = self._fallback_machine_selection(
                    selected_op, processing_matrix
                )
                if fallback_machine is not None:
                    schedule.append((selected_op, fallback_machine))
                    scheduled_ops.add(selected_op)
                    
                    for job_id, ops in enumerate(job_operations):
                        if selected_op in ops:
                            job_progress[job_id] += 1
                            break
            
            # 检查是否完成所有操作
            if len(scheduled_ops) >= n_operations:
                break
        
        return schedule
    
    def _get_available_operations(self, job_operations, job_progress, scheduled_ops):
        """获取所有可调度的操作（满足作业内顺序约束）"""
        available_ops = []
        
        for job_id, operations in enumerate(job_operations):
            if job_progress[job_id] < len(operations):
                next_op = operations[job_progress[job_id]]
                if next_op not in scheduled_ops:
                    available_ops.append(next_op)
        
        return available_ops
    
    def _select_operation_with_nn(self, available_operations, op_embeddings, processing_matrix):
        """使用神经网络选择操作"""
        if len(available_operations) == 1:
            return available_operations[0]
        
        # 计算每个可用操作的评分
        op_scores = []
        for op_id in available_operations:
            op_embedding = op_embeddings[op_id]  # [d_model]
            score = self.operation_selector(op_embedding.unsqueeze(0)).squeeze()  # scalar
            op_scores.append((score.item(), op_id))
        
        # 选择评分最高的操作
        op_scores.sort(reverse=True)
        return op_scores[0][1]
    
    def _select_machine_with_nn(self, operation_id, machine_embeddings, processing_matrix):
        """使用神经网络选择机器"""
        # 找到可用的机器
        valid_machines = [m for m in range(processing_matrix.size(1)) 
                         if processing_matrix[operation_id, m] > 0]
        
        if not valid_machines:
            return None
        
        if len(valid_machines) == 1:
            return valid_machines[0]
        
        # 计算每个可用机器的评分
        machine_scores = []
        for machine_id in valid_machines:
            machine_embedding = machine_embeddings[machine_id]  # [d_model]
            proc_time = processing_matrix[operation_id, machine_id].unsqueeze(0)  # [1]
            
            # 结合机器嵌入和处理时间
            combined_input = torch.cat([machine_embedding, proc_time], dim=0)  # [d_model + 1]
            score = self.machine_selector(combined_input.unsqueeze(0)).squeeze()  # scalar
            machine_scores.append((score.item(), machine_id))
        
        # 选择评分最高的机器
        machine_scores.sort(reverse=True)
        return machine_scores[0][1]
    
    def _is_valid_assignment(self, operation_id, machine_id, processing_matrix):
        """验证操作-机器分配的有效性"""
        if machine_id is None:
            return False
        
        # 检查机器是否可以处理该操作
        return processing_matrix[operation_id, machine_id] > 0
    
    def _fallback_machine_selection(self, operation_id, processing_matrix):
        """启发式机器选择回退"""
        valid_machines = [m for m in range(processing_matrix.size(1)) 
                         if processing_matrix[operation_id, m] > 0]
        
        if not valid_machines:
            return None
        
        # 选择处理时间最短的机器
        best_machine = min(valid_machines, 
                          key=lambda m: processing_matrix[operation_id, m].item())
        return best_machine


class ValidScheduleNeuralModel(nn.Module):
    """确保输出有效调度的神经网络模型"""
    
    def __init__(self, d_model=256, n_heads=8, n_layers=4, dropout=0.1):
        super().__init__()
        self.d_model = d_model
        
        # 特征编码器
        self.job_encoder = nn.Sequential(
            nn.Linear(10, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, d_model),
            nn.LayerNorm(d_model)
        )
        
        self.operation_encoder = nn.Sequential(
            nn.Linear(15, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, d_model),
            nn.LayerNorm(d_model)
        )
        
        self.machine_encoder = nn.Sequential(
            nn.Linear(8, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, d_model),
            nn.LayerNorm(d_model)
        )
        
        # Transformer层
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            batch_first=True
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)
        
        # 约束感知解码器
        self.decoder = ConstraintAwareDecoder(d_model, n_heads, dropout)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.LayerNorm):
                nn.init.ones_(module.weight)
                nn.init.zeros_(module.bias)
    
    def forward(self, job_features, operation_features, machine_features, 
                processing_matrix, job_lengths, num_columns=1):
        """
        前向传播
        
        Args:
            job_features: [batch, n_jobs, 10]
            operation_features: [batch, n_ops, 15]
            machine_features: [batch, n_machines, 8]
            processing_matrix: [batch, n_ops, n_machines]
            job_lengths: List[int]
            num_columns: 生成的列数
        """
        batch_size = job_features.size(0)
        
        # 编码特征
        job_embeddings = self.job_encoder(job_features)
        operation_embeddings = self.operation_encoder(operation_features)
        machine_embeddings = self.machine_encoder(machine_features)
        
        # 通过Transformer增强表示
        operation_embeddings = self.transformer(operation_embeddings)
        machine_embeddings = self.transformer(machine_embeddings)
        
        # 生成多个列
        all_columns = []
        
        for col_idx in range(num_columns):
            # 为每个列添加不同的随机扰动以增加多样性
            if col_idx > 0:
                noise_scale = 0.05 * col_idx
                operation_embeddings_noisy = operation_embeddings + torch.randn_like(operation_embeddings) * noise_scale
                machine_embeddings_noisy = machine_embeddings + torch.randn_like(machine_embeddings) * noise_scale
            else:
                operation_embeddings_noisy = operation_embeddings
                machine_embeddings_noisy = machine_embeddings
            
            # 生成调度
            schedules = self.decoder(
                operation_embeddings_noisy,
                machine_embeddings_noisy,
                processing_matrix,
                job_lengths
            )
            
            # 评估每个调度并创建输出
            for schedule in schedules:
                column_output = self._create_column_output(
                    schedule, processing_matrix[0].cpu().numpy(), job_lengths
                )
                if column_output:
                    all_columns.append(column_output)
        
        return all_columns
    
    def _create_column_output(self, schedule, processing_matrix, job_lengths):
        """创建列输出"""
        if not schedule:
            return None
        
        # 评估调度
        makespan, feasible = self._evaluate_schedule(schedule, processing_matrix, job_lengths)
        
        if not feasible:
            return None
        
        # 计算质量评分
        quality_score = 1.0 / (1.0 + makespan / 100.0)
        
        return ValidScheduleOutput(
            schedule=schedule,
            makespan=makespan,
            feasible=feasible,
            quality_score=quality_score,
            constraint_violations=0
        )
    
    def _evaluate_schedule(self, schedule, processing_matrix, job_lengths):
        """评估调度"""
        try:
            from schedule_evaluator import AccurateScheduleEvaluator
            evaluator = AccurateScheduleEvaluator(job_lengths, processing_matrix)
            result = evaluator.evaluate_schedule(schedule)
            return result.makespan, result.feasible
        except:
            # 简单评估
            if not schedule:
                return float('inf'), False
            
            n_machines = processing_matrix.shape[1]
            machine_times = [0] * n_machines
            
            for op_id, machine_id in schedule:
                if (machine_id < n_machines and 
                    op_id < processing_matrix.shape[0] and 
                    processing_matrix[op_id, machine_id] > 0):
                    machine_times[machine_id] += processing_matrix[op_id, machine_id]
            
            makespan = max(machine_times) if machine_times else float('inf')
            return makespan, makespan < float('inf')
    
    def generate_valid_columns(self, job_features, operation_features, machine_features,
                              processing_matrix, job_lengths, num_columns=5):
        """生成有效的调度列"""
        self.eval()
        with torch.no_grad():
            columns = self.forward(
                job_features, operation_features, machine_features,
                processing_matrix, job_lengths, num_columns
            )
        
        # 过滤出有效的列
        valid_columns = [col for col in columns if col.feasible]
        
        return valid_columns


def main():
    """测试有效调度神经网络模型"""
    print("🧪 Testing Valid Schedule Neural Model")
    print("=" * 50)
    
    # 创建模型
    model = ValidScheduleNeuralModel(d_model=128, n_heads=4, n_layers=3)
    
    print(f"✅ Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
    
    # 创建测试数据
    batch_size = 1
    n_jobs = 3
    n_operations = 10
    n_machines = 4
    job_lengths = [3, 3, 4]
    
    job_features = torch.randn(batch_size, n_jobs, 10)
    operation_features = torch.randn(batch_size, n_operations, 15)
    machine_features = torch.randn(batch_size, n_machines, 8)
    
    # 创建有效的处理时间矩阵
    processing_matrix = torch.zeros(batch_size, n_operations, n_machines)
    for b in range(batch_size):
        for op in range(n_operations):
            # 每个操作在2-3个机器上可用
            available_machines = np.random.choice(n_machines, size=np.random.randint(2, 4), replace=False)
            for m in available_machines:
                processing_matrix[b, op, m] = np.random.randint(5, 20)
    
    print(f"🧪 Testing with {n_jobs} jobs, {n_machines} machines, {n_operations} operations")
    
    # 测试模型
    try:
        columns = model.generate_valid_columns(
            job_features, operation_features, machine_features,
            processing_matrix, job_lengths, num_columns=3
        )
        
        print(f"✅ Generated {len(columns)} valid columns!")
        
        for i, column in enumerate(columns):
            print(f"  Column {i+1}: makespan={column.makespan:.1f}, "
                  f"feasible={column.feasible}, quality={column.quality_score:.3f}")
            print(f"    Schedule length: {len(column.schedule)}")
        
        if columns:
            print(f"🎉 All generated schedules are valid!")
        else:
            print(f"⚠️  No valid schedules generated")
    
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
