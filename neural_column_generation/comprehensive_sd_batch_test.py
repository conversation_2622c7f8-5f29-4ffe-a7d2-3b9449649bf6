#!/usr/bin/env python3
"""
SD数据集批量测试
在SD数据集上进行大规模批量测试，全面评估神经列生成系统性能
"""

import os
import sys
import torch
import numpy as np
import time
import json
import pickle
from typing import List, Dict, Tuple
from collections import defaultdict
from datetime import datetime
import matplotlib.pyplot as plt

# 添加路径
sys.path.append('..')
sys.path.append('.')

from fixed_machine_selection import ImprovedNeuralModel
from daniel_consistent_data_generator import DANIELConsistentDataGenerator, DANIELTrainingConfig


class SDDatasetBatchTester:
    """SD数据集批量测试器"""
    
    def __init__(self):
        self.model = None
        self.device = None
        self.results = []
        self.summary_stats = {}
        
    def load_model(self):
        """加载训练好的模型"""
        print("🔄 Loading neural column generation model...")
        
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = ImprovedNeuralModel(d_model=128, n_heads=4, n_layers=3).to(self.device)
        
        model_path = "neural_column_generation/models/high_quality_best.pth"
        if os.path.exists(model_path):
            try:
                checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                self.model.eval()
                print(f"✅ Model loaded successfully")
                print(f"📊 Training loss: {checkpoint.get('best_loss', 'N/A')}")
                return True
            except Exception as e:
                print(f"❌ Failed to load model: {e}")
                return False
        else:
            print(f"❌ Model file not found: {model_path}")
            return False
    
    def generate_sd_dataset(self, total_instances=50):
        """生成SD数据集实例"""
        print(f"📊 Generating {total_instances} SD dataset instances...")
        
        instances = []
        
        # SD1数据集 - 小规模
        configs_sd1 = [
            (3, 3, 8),   # 3作业3机器，8个实例
            (4, 3, 6),   # 4作业3机器，6个实例
            (5, 4, 6),   # 5作业4机器，6个实例
        ]
        
        for n_j, n_m, count in configs_sd1:
            config = DANIELTrainingConfig(n_j=n_j, n_m=n_m, data_source='SD1')
            generator = DANIELConsistentDataGenerator(config)
            batch_instances = generator.sample_training_instances_like_daniel(count)
            instances.extend(batch_instances)
            print(f"  ✅ Generated {count} instances: {n_j}J×{n_m}M (SD1)")
        
        # SD2数据集 - 中等规模
        configs_sd2 = [
            (6, 4, 5),   # 6作业4机器，5个实例
            (8, 5, 5),   # 8作业5机器，5个实例
            (10, 6, 5),  # 10作业6机器，5个实例
            (12, 6, 4),  # 12作业6机器，4个实例
            (15, 8, 3),  # 15作业8机器，3个实例
        ]
        
        for n_j, n_m, count in configs_sd2:
            config = DANIELTrainingConfig(n_j=n_j, n_m=n_m, data_source='SD2')
            generator = DANIELConsistentDataGenerator(config)
            batch_instances = generator.sample_training_instances_like_daniel(count)
            instances.extend(batch_instances)
            print(f"  ✅ Generated {count} instances: {n_j}J×{n_m}M (SD2)")
        
        print(f"✅ Total generated: {len(instances)} instances")
        return instances
    
    def extract_features(self, instance):
        """提取实例特征"""
        job_length = instance['job_length']
        processing_times = np.array(instance['processing_times'])
        n_jobs = instance['n_jobs']
        n_machines = instance['n_machines']
        n_operations = instance['n_operations']
        
        # 作业特征
        job_features = []
        for job_id, n_ops in enumerate(job_length):
            op_start = sum(job_length[:job_id])
            op_end = op_start + n_ops
            
            total_workload = 0
            for op_idx in range(op_start, op_end):
                if op_idx < len(processing_times):
                    valid_times = [t for t in processing_times[op_idx] if t > 0]
                    if valid_times:
                        min_time = min(valid_times)
                        total_workload += min_time
            
            job_feat = [
                job_id / n_jobs, n_ops / n_operations, total_workload / 1000.0,
                n_ops / max(job_length), total_workload / max(1, n_ops * 20),
                0, 0, 0, 0, 0
            ]
            job_features.append(job_feat)
        
        # 操作特征
        operation_features = []
        for op_id in range(n_operations):
            if op_id < len(processing_times):
                proc_times = processing_times[op_id]
                
                job_id = 0
                op_in_job = op_id
                for j, n_ops in enumerate(job_length):
                    if op_in_job < n_ops:
                        job_id = j
                        break
                    op_in_job -= n_ops
                
                valid_times = [t for t in proc_times if t > 0]
                min_time = min(valid_times) if valid_times else 0
                max_time = max(valid_times) if valid_times else 0
                avg_time = np.mean(valid_times) if valid_times else 0
                n_machines_available = len(valid_times)
                
                op_feat = [
                    op_id / n_operations, job_id / n_jobs, op_in_job / max(job_length),
                    min_time / 50.0, max_time / 50.0, avg_time / 50.0,
                    n_machines_available / n_machines, (max_time - min_time) / max(1, 50.0),
                    0, 0, 0, 0, 0, 0, 0
                ]
                operation_features.append(op_feat)
        
        # 机器特征
        machine_features = []
        for machine_id in range(n_machines):
            total_load = 0
            n_operations_available = 0
            
            for op_id in range(n_operations):
                if op_id < len(processing_times) and machine_id < len(processing_times[op_id]):
                    proc_time = processing_times[op_id][machine_id]
                    if proc_time > 0:
                        total_load += proc_time
                        n_operations_available += 1
            
            avg_load = total_load / max(1, n_operations_available)
            
            machine_feat = [
                machine_id / n_machines, total_load / 1000.0,
                n_operations_available / n_operations, avg_load / 50.0,
                0, 0, 0, 0
            ]
            machine_features.append(machine_feat)
        
        return {
            'job_features': torch.FloatTensor(job_features),
            'operation_features': torch.FloatTensor(operation_features),
            'machine_features': torch.FloatTensor(machine_features),
            'processing_matrix': torch.FloatTensor(processing_times),
            'job_lengths': job_length
        }
    
    def evaluate_schedule(self, schedule, instance):
        """评估调度方案"""
        if not schedule:
            return float('inf'), False, {}
        
        processing_times = np.array(instance['processing_times'])
        job_lengths = instance['job_length']
        n_machines = instance['n_machines']
        n_operations = instance['n_operations']
        
        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_lengths):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops
        
        # 检查可行性并计算makespan
        job_progress = [0] * len(job_lengths)
        machine_times = [0] * n_machines
        scheduled_ops = set()
        
        for op_id, machine_id in schedule:
            # 基本检查
            if (op_id >= n_operations or machine_id >= n_machines or 
                processing_times[op_id, machine_id] <= 0):
                return float('inf'), False, {}
            
            # 检查作业顺序约束
            target_job = None
            for job_id, ops in enumerate(job_operations):
                if op_id in ops:
                    target_job = job_id
                    break
            
            if target_job is None:
                return float('inf'), False, {}
            
            op_index_in_job = job_operations[target_job].index(op_id)
            if job_progress[target_job] != op_index_in_job:
                return float('inf'), False, {}
            
            # 更新状态
            machine_times[machine_id] += processing_times[op_id, machine_id]
            job_progress[target_job] += 1
            scheduled_ops.add(op_id)
        
        # 检查完整性
        if len(scheduled_ops) != n_operations:
            return float('inf'), False, {}
        
        makespan = max(machine_times)
        
        # 计算额外指标
        metrics = {
            'makespan': makespan,
            'machine_utilization': np.mean(machine_times) / makespan if makespan > 0 else 0,
            'load_balance': 1 - (np.std(machine_times) / np.mean(machine_times)) if np.mean(machine_times) > 0 else 0,
            'total_processing_time': sum(machine_times)
        }
        
        return makespan, True, metrics
    
    def generate_baseline_solutions(self, instance):
        """生成基线解决方案"""
        job_lengths = instance['job_length']
        processing_times = np.array(instance['processing_times'])
        n_operations = instance['n_operations']
        n_machines = instance['n_machines']
        
        baselines = {}
        
        # FIFO (First In First Out)
        fifo_ops = list(range(n_operations))
        fifo_schedule = self.create_feasible_schedule(fifo_ops, job_lengths, processing_times)
        fifo_makespan, fifo_feasible, fifo_metrics = self.evaluate_schedule(fifo_schedule, instance)
        baselines['FIFO'] = {
            'makespan': fifo_makespan,
            'feasible': fifo_feasible,
            'metrics': fifo_metrics,
            'schedule': fifo_schedule
        }
        
        # SPT (Shortest Processing Time)
        try:
            spt_ops = list(range(n_operations))
            spt_ops.sort(key=lambda op: min(processing_times[op, m] 
                                          for m in range(n_machines) 
                                          if processing_times[op, m] > 0))
            spt_schedule = self.create_feasible_schedule(spt_ops, job_lengths, processing_times)
            spt_makespan, spt_feasible, spt_metrics = self.evaluate_schedule(spt_schedule, instance)
            baselines['SPT'] = {
                'makespan': spt_makespan,
                'feasible': spt_feasible,
                'metrics': spt_metrics,
                'schedule': spt_schedule
            }
        except:
            baselines['SPT'] = {'makespan': float('inf'), 'feasible': False, 'metrics': {}}
        
        # LPT (Longest Processing Time)
        try:
            lpt_ops = list(range(n_operations))
            lpt_ops.sort(key=lambda op: max(processing_times[op, m] 
                                          for m in range(n_machines) 
                                          if processing_times[op, m] > 0), reverse=True)
            lpt_schedule = self.create_feasible_schedule(lpt_ops, job_lengths, processing_times)
            lpt_makespan, lpt_feasible, lpt_metrics = self.evaluate_schedule(lpt_schedule, instance)
            baselines['LPT'] = {
                'makespan': lpt_makespan,
                'feasible': lpt_feasible,
                'metrics': lpt_metrics,
                'schedule': lpt_schedule
            }
        except:
            baselines['LPT'] = {'makespan': float('inf'), 'feasible': False, 'metrics': {}}
        
        return baselines
    
    def create_feasible_schedule(self, operation_order, job_lengths, processing_times):
        """创建可行调度"""
        schedule = []
        job_progress = [0] * len(job_lengths)
        scheduled_ops = set()
        
        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_lengths):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops
        
        for target_op in operation_order:
            if target_op in scheduled_ops:
                continue
            
            # 找到操作所属作业
            target_job = None
            for job_id, ops in enumerate(job_operations):
                if target_op in ops:
                    target_job = job_id
                    break
            
            if target_job is None:
                continue
            
            # 检查是否可调度
            op_index_in_job = job_operations[target_job].index(target_op)
            if job_progress[target_job] == op_index_in_job:
                valid_machines = [m for m in range(processing_times.shape[1]) 
                                 if processing_times[target_op, m] > 0]
                
                if valid_machines:
                    # 选择处理时间最短的机器
                    best_machine = min(valid_machines, 
                                     key=lambda m: processing_times[target_op, m])
                    
                    schedule.append((target_op, best_machine))
                    scheduled_ops.add(target_op)
                    job_progress[target_job] += 1
        
        return schedule
    
    def test_single_instance(self, instance, instance_idx):
        """测试单个实例"""
        print(f"  📊 Instance {instance_idx}: {instance['n_jobs']}J×{instance['n_machines']}M ({instance['n_operations']} ops)")
        
        result = {
            'instance_id': instance['instance_id'],
            'instance_idx': instance_idx,
            'size': f"{instance['n_jobs']}J×{instance['n_machines']}M",
            'n_jobs': instance['n_jobs'],
            'n_machines': instance['n_machines'],
            'n_operations': instance['n_operations'],
            'data_source': instance.get('data_source', 'SD'),
            'timestamp': datetime.now().isoformat()
        }
        
        # 测试神经网络
        neural_start = time.time()
        try:
            features = self.extract_features(instance)
            
            with torch.no_grad():
                columns = self.model.generate_improved_columns(
                    features['job_features'].unsqueeze(0).to(self.device),
                    features['operation_features'].unsqueeze(0).to(self.device),
                    features['machine_features'].unsqueeze(0).to(self.device),
                    features['processing_matrix'].unsqueeze(0).to(self.device),
                    features['job_lengths'],
                    num_columns=5
                )
            
            neural_time = time.time() - neural_start
            
            # 评估神经网络结果
            valid_columns = []
            for column in columns:
                if hasattr(column, 'feasible') and column.feasible:
                    makespan, feasible, metrics = self.evaluate_schedule(column.schedule, instance)
                    if feasible and makespan < float('inf'):
                        valid_columns.append({
                            'makespan': makespan,
                            'metrics': metrics,
                            'decision_quality': getattr(column, 'decision_quality', 0.0),
                            'schedule': column.schedule
                        })
            
            if valid_columns:
                best_column = min(valid_columns, key=lambda x: x['makespan'])
                result['neural'] = {
                    'success': True,
                    'best_makespan': best_column['makespan'],
                    'avg_makespan': np.mean([c['makespan'] for c in valid_columns]),
                    'best_metrics': best_column['metrics'],
                    'avg_decision_quality': np.mean([c['decision_quality'] for c in valid_columns]),
                    'feasible_columns': len(valid_columns),
                    'total_columns': len(columns),
                    'generation_time': neural_time
                }
                print(f"    🧠 Neural: {best_column['makespan']:.1f} ({len(valid_columns)}/{len(columns)} feasible)")
            else:
                result['neural'] = {
                    'success': False,
                    'generation_time': neural_time,
                    'error': 'No feasible columns generated'
                }
                print(f"    ❌ Neural: Failed")
                
        except Exception as e:
            result['neural'] = {
                'success': False,
                'generation_time': time.time() - neural_start,
                'error': str(e)
            }
            print(f"    ❌ Neural: Error - {e}")
        
        # 测试基线算法
        baseline_start = time.time()
        baselines = self.generate_baseline_solutions(instance)
        baseline_time = time.time() - baseline_start
        
        result['baselines'] = baselines
        result['baseline_time'] = baseline_time
        
        # 找到最佳基线
        best_baseline_makespan = float('inf')
        best_baseline_method = None
        for method, baseline in baselines.items():
            if baseline['feasible'] and baseline['makespan'] < best_baseline_makespan:
                best_baseline_makespan = baseline['makespan']
                best_baseline_method = method
        
        result['best_baseline'] = {
            'method': best_baseline_method,
            'makespan': best_baseline_makespan
        }
        
        # 计算改进
        if (result['neural']['success'] and 
            best_baseline_makespan < float('inf')):
            improvement = ((best_baseline_makespan - result['neural']['best_makespan']) / 
                          best_baseline_makespan * 100)
            result['improvement'] = improvement
            print(f"    📈 Improvement: {improvement:+.1f}% vs {best_baseline_method}")
        else:
            result['improvement'] = None
            print(f"    ⚠️  Cannot compare")
        
        return result
    
    def run_batch_test(self, total_instances=50):
        """运行批量测试"""
        print("🎯 SD DATASET COMPREHENSIVE BATCH TEST")
        print("=" * 70)
        
        # 加载模型
        if not self.load_model():
            return None
        
        # 生成数据集
        instances = self.generate_sd_dataset(total_instances)
        
        # 批量测试
        print(f"\n🧪 Testing {len(instances)} instances...")
        
        for i, instance in enumerate(instances):
            result = self.test_single_instance(instance, i + 1)
            self.results.append(result)
        
        # 生成统计报告
        self.generate_summary_report()
        
        # 保存结果
        self.save_results()
        
        return self.results
    
    def generate_summary_report(self):
        """生成汇总报告"""
        print(f"\n📈 COMPREHENSIVE BATCH TEST RESULTS")
        print("=" * 60)
        
        total_instances = len(self.results)
        neural_successes = [r for r in self.results if r['neural']['success']]
        neural_success_rate = len(neural_successes) / total_instances * 100
        
        print(f"🎯 Overall Performance:")
        print(f"  Total Instances: {total_instances}")
        print(f"  Neural Success Rate: {neural_success_rate:.1f}% ({len(neural_successes)}/{total_instances})")
        
        if neural_successes:
            # 性能统计
            neural_makespans = [r['neural']['best_makespan'] for r in neural_successes]
            neural_times = [r['neural']['generation_time'] for r in neural_successes]
            decision_qualities = [r['neural']['avg_decision_quality'] for r in neural_successes]
            
            print(f"  Average Neural Makespan: {np.mean(neural_makespans):.1f}")
            print(f"  Average Generation Time: {np.mean(neural_times):.3f}s")
            print(f"  Average Decision Quality: {np.mean(decision_qualities):.3f}")
            
            # 改进统计
            valid_improvements = [r['improvement'] for r in self.results if r['improvement'] is not None]
            if valid_improvements:
                avg_improvement = np.mean(valid_improvements)
                positive_improvements = [imp for imp in valid_improvements if imp > 0]
                
                print(f"\n🏆 Quality Comparison:")
                print(f"  Average Improvement: {avg_improvement:+.1f}%")
                print(f"  Instances with Improvement: {len(positive_improvements)}/{len(valid_improvements)}")
                print(f"  Best Improvement: {max(valid_improvements):+.1f}%")
                print(f"  Worst Performance: {min(valid_improvements):+.1f}%")
            
            # 按规模分析
            print(f"\n📊 Performance by Instance Size:")
            size_groups = defaultdict(list)
            for result in neural_successes:
                size_groups[result['size']].append(result)
            
            for size, results in sorted(size_groups.items()):
                if results:
                    avg_makespan = np.mean([r['neural']['best_makespan'] for r in results])
                    improvements = [r['improvement'] for r in results if r['improvement'] is not None]
                    avg_improvement = np.mean(improvements) if improvements else 0
                    print(f"  {size}: {len(results)} instances, avg makespan={avg_makespan:.1f}, avg improvement={avg_improvement:+.1f}%")
        
        # 保存统计信息
        self.summary_stats = {
            'total_instances': total_instances,
            'neural_success_rate': neural_success_rate,
            'avg_makespan': np.mean([r['neural']['best_makespan'] for r in neural_successes]) if neural_successes else 0,
            'avg_generation_time': np.mean([r['neural']['generation_time'] for r in neural_successes]) if neural_successes else 0,
            'avg_improvement': np.mean([r['improvement'] for r in self.results if r['improvement'] is not None]) if any(r['improvement'] is not None for r in self.results) else 0
        }
    
    def save_results(self):
        """保存测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 保存详细结果
        results_file = f"neural_column_generation/results/sd_batch_test_{timestamp}.json"
        os.makedirs(os.path.dirname(results_file), exist_ok=True)

        # 转换numpy类型为Python原生类型
        def convert_numpy(obj):
            if isinstance(obj, np.integer):
                return int(obj)
            elif isinstance(obj, np.floating):
                return float(obj)
            elif isinstance(obj, np.ndarray):
                return obj.tolist()
            elif isinstance(obj, dict):
                return {key: convert_numpy(value) for key, value in obj.items()}
            elif isinstance(obj, list):
                return [convert_numpy(item) for item in obj]
            return obj

        converted_results = convert_numpy({
            'timestamp': timestamp,
            'summary_stats': self.summary_stats,
            'detailed_results': self.results
        })

        with open(results_file, 'w') as f:
            json.dump(converted_results, f, indent=2)
        
        print(f"\n💾 Results saved to: {results_file}")
        
        # 保存简化版本
        summary_file = f"neural_column_generation/results/sd_batch_summary_{timestamp}.txt"
        with open(summary_file, 'w') as f:
            f.write("SD Dataset Batch Test Summary\n")
            f.write("=" * 40 + "\n\n")
            f.write(f"Total Instances: {self.summary_stats['total_instances']}\n")
            f.write(f"Success Rate: {self.summary_stats['neural_success_rate']:.1f}%\n")
            f.write(f"Average Makespan: {self.summary_stats['avg_makespan']:.1f}\n")
            f.write(f"Average Time: {self.summary_stats['avg_generation_time']:.3f}s\n")
            f.write(f"Average Improvement: {self.summary_stats['avg_improvement']:+.1f}%\n")
        
        print(f"📋 Summary saved to: {summary_file}")


def main():
    """主函数"""
    print("🎯 SD DATASET COMPREHENSIVE BATCH TEST")
    print("=" * 80)
    
    tester = SDDatasetBatchTester()
    results = tester.run_batch_test(total_instances=50)
    
    if results:
        print(f"\n✅ Batch test completed successfully!")
        print(f"📊 {len(results)} instances tested")
        print(f"🎯 System ready for production deployment!")
    else:
        print(f"\n❌ Batch test failed!")


if __name__ == "__main__":
    main()
