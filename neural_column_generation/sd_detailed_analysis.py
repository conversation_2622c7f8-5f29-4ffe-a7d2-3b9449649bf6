#!/usr/bin/env python3
"""
SD数据集详细分析
深入分析神经网络在SD数据集上的表现，找出质量差异的原因
"""

import os
import sys
import torch
import numpy as np
import time
from typing import List, Dict, Tuple

# 添加路径
sys.path.append('..')
sys.path.append('.')

from fixed_machine_selection import ImprovedNeuralModel
from daniel_consistent_data_generator import DANIELConsistentDataGenerator, DANIELTrainingConfig


def load_high_quality_model():
    """加载高质量模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = ImprovedNeuralModel(d_model=128, n_heads=4, n_layers=3).to(device)
    
    model_path = "neural_column_generation/models/high_quality_best.pth"
    if os.path.exists(model_path):
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        return model, device
    return None, device


def create_test_instance():
    """创建一个SD1实例进行详细分析"""
    config = DANIELTrainingConfig(n_j=3, n_m=3, data_source='SD1')
    generator = DANIELConsistentDataGenerator(config)
    instances = generator.sample_training_instances_like_daniel(1)
    return instances[0]


def extract_features_for_model(instance):
    """提取模型特征"""
    job_length = instance['job_length']
    processing_times = np.array(instance['processing_times'])
    n_jobs = instance['n_jobs']
    n_machines = instance['n_machines']
    n_operations = instance['n_operations']
    
    # 作业特征
    job_features = []
    for job_id, n_ops in enumerate(job_length):
        op_start = sum(job_length[:job_id])
        op_end = op_start + n_ops
        
        total_workload = 0
        for op_idx in range(op_start, op_end):
            if op_idx < len(processing_times):
                valid_times = [t for t in processing_times[op_idx] if t > 0]
                if valid_times:
                    min_time = min(valid_times)
                    total_workload += min_time
        
        job_feat = [
            job_id / n_jobs, n_ops / n_operations, total_workload / 1000.0,
            n_ops / max(job_length), total_workload / max(1, n_ops * 20),
            0, 0, 0, 0, 0
        ]
        job_features.append(job_feat)
    
    # 操作特征
    operation_features = []
    for op_id in range(n_operations):
        if op_id < len(processing_times):
            proc_times = processing_times[op_id]
            
            job_id = 0
            op_in_job = op_id
            for j, n_ops in enumerate(job_length):
                if op_in_job < n_ops:
                    job_id = j
                    break
                op_in_job -= n_ops
            
            valid_times = [t for t in proc_times if t > 0]
            min_time = min(valid_times) if valid_times else 0
            max_time = max(valid_times) if valid_times else 0
            avg_time = np.mean(valid_times) if valid_times else 0
            n_machines_available = len(valid_times)
            
            op_feat = [
                op_id / n_operations, job_id / n_jobs, op_in_job / max(job_length),
                min_time / 50.0, max_time / 50.0, avg_time / 50.0,
                n_machines_available / n_machines, (max_time - min_time) / max(1, 50.0),
                0, 0, 0, 0, 0, 0, 0
            ]
            operation_features.append(op_feat)
    
    # 机器特征
    machine_features = []
    for machine_id in range(n_machines):
        total_load = 0
        n_operations_available = 0
        
        for op_id in range(n_operations):
            if op_id < len(processing_times) and machine_id < len(processing_times[op_id]):
                proc_time = processing_times[op_id][machine_id]
                if proc_time > 0:
                    total_load += proc_time
                    n_operations_available += 1
        
        avg_load = total_load / max(1, n_operations_available)
        
        machine_feat = [
            machine_id / n_machines, total_load / 1000.0,
            n_operations_available / n_operations, avg_load / 50.0,
            0, 0, 0, 0
        ]
        machine_features.append(machine_feat)
    
    return {
        'job_features': torch.FloatTensor(job_features),
        'operation_features': torch.FloatTensor(operation_features),
        'machine_features': torch.FloatTensor(machine_features),
        'processing_matrix': torch.FloatTensor(processing_times),
        'job_lengths': job_length
    }


def evaluate_schedule(schedule, instance):
    """评估调度"""
    if not schedule:
        return float('inf'), False
    
    processing_times = np.array(instance['processing_times'])
    job_lengths = instance['job_length']
    n_machines = instance['n_machines']
    n_operations = instance['n_operations']
    
    # 检查调度可行性
    job_progress = [0] * len(job_lengths)
    
    # 构建作业-操作映射
    job_operations = []
    op_id = 0
    for job_id, n_ops in enumerate(job_lengths):
        job_operations.append(list(range(op_id, op_id + n_ops)))
        op_id += n_ops
    
    machine_times = [0] * n_machines
    
    for op_id, machine_id in schedule:
        # 检查操作是否存在
        if op_id >= n_operations or machine_id >= n_machines:
            return float('inf'), False
        
        # 检查处理时间是否有效
        if processing_times[op_id, machine_id] <= 0:
            return float('inf'), False
        
        # 检查作业顺序约束
        target_job = None
        for job_id, ops in enumerate(job_operations):
            if op_id in ops:
                target_job = job_id
                break
        
        if target_job is None:
            return float('inf'), False
        
        op_index_in_job = job_operations[target_job].index(op_id)
        if job_progress[target_job] != op_index_in_job:
            return float('inf'), False
        
        # 更新机器时间和作业进度
        machine_times[machine_id] += processing_times[op_id, machine_id]
        job_progress[target_job] += 1
    
    # 检查是否所有操作都被调度
    if len(set(op for op, _ in schedule)) != n_operations:
        return float('inf'), False
    
    makespan = max(machine_times)
    return makespan, True


def create_optimal_schedule(instance):
    """创建理论最优调度（贪心选择最短处理时间）"""
    processing_times = np.array(instance['processing_times'])
    job_lengths = instance['job_length']
    n_operations = instance['n_operations']
    
    # 构建作业-操作映射
    job_operations = []
    op_id = 0
    for job_id, n_ops in enumerate(job_lengths):
        job_operations.append(list(range(op_id, op_id + n_ops)))
        op_id += n_ops
    
    schedule = []
    job_progress = [0] * len(job_lengths)
    scheduled_ops = set()
    
    # 贪心调度：总是选择当前可用操作中处理时间最短的
    while len(scheduled_ops) < n_operations:
        # 获取可调度的操作
        available_ops = []
        for job_id, operations in enumerate(job_operations):
            if job_progress[job_id] < len(operations):
                next_op = operations[job_progress[job_id]]
                if next_op not in scheduled_ops:
                    available_ops.append(next_op)
        
        if not available_ops:
            break
        
        # 为每个可用操作找到最优机器和处理时间
        best_choice = None
        best_time = float('inf')
        
        for op_id in available_ops:
            valid_machines = [m for m in range(processing_times.shape[1]) 
                             if processing_times[op_id, m] > 0]
            
            if valid_machines:
                best_machine = min(valid_machines, 
                                 key=lambda m: processing_times[op_id, m])
                best_proc_time = processing_times[op_id, best_machine]
                
                if best_proc_time < best_time:
                    best_time = best_proc_time
                    best_choice = (op_id, best_machine)
        
        if best_choice:
            op_id, machine_id = best_choice
            schedule.append((op_id, machine_id))
            scheduled_ops.add(op_id)
            
            # 更新作业进度
            for job_id, ops in enumerate(job_operations):
                if op_id in ops:
                    job_progress[job_id] += 1
                    break
    
    return schedule


def analyze_decision_quality(neural_schedule, optimal_schedule, instance):
    """分析决策质量"""
    processing_times = np.array(instance['processing_times'])
    
    print(f"📊 Decision Quality Analysis:")
    print(f"  Neural schedule length: {len(neural_schedule)}")
    print(f"  Optimal schedule length: {len(optimal_schedule)}")
    
    # 比较每个操作的机器选择
    neural_dict = {op: machine for op, machine in neural_schedule}
    optimal_dict = {op: machine for op, machine in optimal_schedule}
    
    correct_decisions = 0
    total_waste = 0
    
    for op_id in range(instance['n_operations']):
        if op_id in neural_dict and op_id in optimal_dict:
            neural_machine = neural_dict[op_id]
            optimal_machine = optimal_dict[op_id]
            
            neural_time = processing_times[op_id, neural_machine]
            optimal_time = processing_times[op_id, optimal_machine]
            
            if neural_machine == optimal_machine:
                correct_decisions += 1
                print(f"    Op {op_id}: ✅ M{neural_machine}({neural_time})")
            else:
                waste = neural_time - optimal_time
                total_waste += waste
                print(f"    Op {op_id}: ❌ M{neural_machine}({neural_time}) vs optimal M{optimal_machine}({optimal_time}) → waste {waste}")
    
    accuracy = correct_decisions / instance['n_operations'] * 100
    print(f"  Decision accuracy: {accuracy:.1f}% ({correct_decisions}/{instance['n_operations']})")
    print(f"  Total waste: {total_waste}")
    
    return accuracy, total_waste


def run_detailed_analysis():
    """运行详细分析"""
    print("🔍 SD DATASET DETAILED ANALYSIS")
    print("=" * 50)
    
    # 加载模型
    model, device = load_high_quality_model()
    if model is None:
        print("❌ Cannot load model")
        return
    
    # 创建测试实例
    instance = create_test_instance()
    processing_times = np.array(instance['processing_times'])
    
    print(f"📊 Test Instance Analysis:")
    print(f"  Size: {instance['n_jobs']}J×{instance['n_machines']}M ({instance['n_operations']} ops)")
    print(f"  Data source: {instance.get('data_source', 'Unknown')}")
    
    # 显示处理时间矩阵
    print(f"\n📋 Processing Times Matrix:")
    for op_id in range(min(10, instance['n_operations'])):  # 只显示前10个操作
        row = processing_times[op_id]
        optimal_machine = np.argmin([t for t in row if t > 0])
        optimal_time = min([t for t in row if t > 0])
        print(f"  Op {op_id}: {row} → optimal M{optimal_machine}({optimal_time})")
    
    if instance['n_operations'] > 10:
        print(f"  ... (showing first 10 of {instance['n_operations']} operations)")
    
    # 创建理论最优调度
    print(f"\n🎯 Creating theoretical optimal schedule...")
    optimal_schedule = create_optimal_schedule(instance)
    optimal_makespan, optimal_feasible = evaluate_schedule(optimal_schedule, instance)
    
    print(f"  Optimal schedule: {len(optimal_schedule)} operations")
    print(f"  Optimal makespan: {optimal_makespan}")
    print(f"  Optimal feasible: {optimal_feasible}")
    
    # 测试神经网络
    print(f"\n🧠 Testing neural network...")
    features = extract_features_for_model(instance)
    
    with torch.no_grad():
        columns = model.generate_improved_columns(
            features['job_features'].unsqueeze(0).to(device),
            features['operation_features'].unsqueeze(0).to(device),
            features['machine_features'].unsqueeze(0).to(device),
            features['processing_matrix'].unsqueeze(0).to(device),
            features['job_lengths'],
            num_columns=3
        )
    
    print(f"  Generated {len(columns)} columns")
    
    best_neural_makespan = float('inf')
    best_neural_schedule = None
    
    for i, column in enumerate(columns):
        if column.feasible:
            makespan, feasible = evaluate_schedule(column.schedule, instance)
            print(f"    Column {i+1}: makespan={makespan}, feasible={feasible}, decision_quality={column.decision_quality:.3f}")
            
            if feasible and makespan < best_neural_makespan:
                best_neural_makespan = makespan
                best_neural_schedule = column.schedule
    
    # 比较结果
    print(f"\n📈 Performance Comparison:")
    print(f"  Theoretical optimal: {optimal_makespan}")
    print(f"  Neural best: {best_neural_makespan}")
    
    if best_neural_makespan < float('inf') and optimal_makespan < float('inf'):
        gap = (best_neural_makespan - optimal_makespan) / optimal_makespan * 100
        print(f"  Quality gap: {gap:.1f}%")
        
        if gap <= 5:
            print(f"  🏆 EXCELLENT: Very close to optimal!")
        elif gap <= 15:
            print(f"  😊 GOOD: Competitive performance")
        elif gap <= 30:
            print(f"  😐 FAIR: Reasonable performance")
        else:
            print(f"  ⚠️  POOR: Significant gap")
    
    # 详细决策分析
    if best_neural_schedule and optimal_schedule:
        print(f"\n🔍 Detailed Decision Analysis:")
        accuracy, waste = analyze_decision_quality(best_neural_schedule, optimal_schedule, instance)
        
        # 分析为什么会有差异
        print(f"\n🤔 Analysis of Performance Gap:")
        
        if accuracy < 80:
            print(f"  ❌ Poor machine selection accuracy ({accuracy:.1f}%)")
            print(f"  💡 Suggestion: Improve machine selection logic")
        
        if waste > optimal_makespan * 0.1:
            print(f"  ❌ High processing time waste ({waste})")
            print(f"  💡 Suggestion: Strengthen time-based features")
        
        # 检查是否是特征工程问题
        print(f"\n🔧 Feature Engineering Analysis:")
        op_features = features['operation_features']
        min_time_features = op_features[:, 3]  # 归一化最短时间特征
        
        print(f"  Min time features range: [{min_time_features.min():.3f}, {min_time_features.max():.3f}]")
        
        if min_time_features.max() - min_time_features.min() < 0.1:
            print(f"  ⚠️  Low feature variance - may need better normalization")
        
        # 检查处理时间分布
        all_times = processing_times[processing_times > 0]
        print(f"  Processing times range: [{all_times.min()}, {all_times.max()}]")
        print(f"  Processing times std: {all_times.std():.1f}")
        
        if all_times.std() / all_times.mean() > 1.0:
            print(f"  ⚠️  High processing time variance - may need robust normalization")


def main():
    """主函数"""
    print("🔍 SD DATASET DETAILED ANALYSIS")
    print("=" * 60)
    
    run_detailed_analysis()
    
    print(f"\n📋 DETAILED ANALYSIS COMPLETED")


if __name__ == "__main__":
    main()
