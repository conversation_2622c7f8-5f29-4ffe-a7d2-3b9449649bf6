#!/usr/bin/env python3
"""
改进的损失函数
专门设计用于提升makespan质量的多目标损失函数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass


@dataclass
class ScheduleMetrics:
    """调度指标"""
    makespan: float
    feasible: bool
    utilization: float
    idle_time: float
    critical_path_ratio: float
    load_balance: float


class MakespanFocusedLoss(nn.Module):
    """专注于makespan优化的损失函数"""
    
    def __init__(self, alpha=2.0, beta=1.0, gamma=0.5, delta=0.3):
        super().__init__()
        self.alpha = alpha    # makespan损失权重
        self.beta = beta      # 可行性奖励权重
        self.gamma = gamma    # 负载均衡权重
        self.delta = delta    # 利用率权重
    
    def forward(self, predicted_columns, target_makespan, processing_matrix, job_lengths):
        """
        计算改进的损失
        
        Args:
            predicted_columns: 预测的调度列
            target_makespan: 目标makespan
            processing_matrix: 处理时间矩阵
            job_lengths: 作业长度
        """
        if not predicted_columns:
            return torch.tensor(1000.0, requires_grad=True)
        
        total_loss = 0.0
        valid_predictions = 0
        
        for column in predicted_columns:
            if column.feasible:
                metrics = self._calculate_detailed_metrics(
                    column.schedule, processing_matrix, job_lengths
                )
                
                # 1. Makespan损失 - 使用相对误差和平方损失
                makespan_error = abs(metrics.makespan - target_makespan) / (target_makespan + 1e-6)
                makespan_loss = self.alpha * (makespan_error ** 2)
                
                # 2. 可行性奖励 - 鼓励生成可行解
                feasibility_bonus = self.beta * 1.0
                
                # 3. 负载均衡损失 - 鼓励机器负载均衡
                load_balance_loss = self.gamma * (1.0 - metrics.load_balance)
                
                # 4. 利用率损失 - 鼓励高机器利用率
                utilization_loss = self.delta * (1.0 - metrics.utilization)
                
                # 5. 关键路径损失 - 鼓励接近理论下界
                critical_path_loss = 0.2 * max(0, 1.0 - metrics.critical_path_ratio)
                
                loss = (makespan_loss + load_balance_loss + utilization_loss + 
                       critical_path_loss - feasibility_bonus)
                
                total_loss += loss
                valid_predictions += 1
            else:
                # 不可行解的重惩罚
                total_loss += 100.0
        
        if valid_predictions == 0:
            return torch.tensor(1000.0, requires_grad=True)
        
        return torch.tensor(total_loss / len(predicted_columns), requires_grad=True)
    
    def _calculate_detailed_metrics(self, schedule, processing_matrix, job_lengths):
        """计算详细的调度指标"""
        if not schedule:
            return ScheduleMetrics(
                makespan=float('inf'), feasible=False, utilization=0.0,
                idle_time=float('inf'), critical_path_ratio=0.0, load_balance=0.0
            )
        
        try:
            # 转换为numpy数组
            if isinstance(processing_matrix, torch.Tensor):
                proc_matrix = processing_matrix.cpu().numpy()
            else:
                proc_matrix = np.array(processing_matrix)
            
            n_machines = proc_matrix.shape[1]
            n_operations = sum(job_lengths)
            
            # 计算机器完成时间
            machine_times = [0.0] * n_machines
            operation_start_times = {}
            operation_end_times = {}
            
            # 模拟调度执行
            for op_id, machine_id in schedule:
                if machine_id < n_machines and op_id < proc_matrix.shape[0]:
                    proc_time = proc_matrix[op_id, machine_id]
                    if proc_time > 0:
                        start_time = machine_times[machine_id]
                        end_time = start_time + proc_time
                        
                        operation_start_times[op_id] = start_time
                        operation_end_times[op_id] = end_time
                        machine_times[machine_id] = end_time
            
            # 计算makespan
            makespan = max(machine_times) if machine_times else 0.0
            
            # 计算利用率
            total_processing_time = sum(
                proc_matrix[op_id, machine_id] 
                for op_id, machine_id in schedule
                if machine_id < n_machines and op_id < proc_matrix.shape[0] and proc_matrix[op_id, machine_id] > 0
            )
            utilization = total_processing_time / (makespan * n_machines) if makespan > 0 else 0.0
            
            # 计算负载均衡
            if makespan > 0:
                load_balance = 1.0 - (np.std(machine_times) / np.mean(machine_times))
                load_balance = max(0.0, min(1.0, load_balance))
            else:
                load_balance = 0.0
            
            # 计算关键路径比率
            min_makespan = self._calculate_lower_bound(proc_matrix, job_lengths)
            critical_path_ratio = min_makespan / makespan if makespan > 0 else 0.0
            
            # 计算空闲时间
            total_idle_time = makespan * n_machines - total_processing_time
            
            return ScheduleMetrics(
                makespan=makespan,
                feasible=len(schedule) == n_operations,
                utilization=utilization,
                idle_time=total_idle_time,
                critical_path_ratio=critical_path_ratio,
                load_balance=load_balance
            )
            
        except Exception as e:
            return ScheduleMetrics(
                makespan=float('inf'), feasible=False, utilization=0.0,
                idle_time=float('inf'), critical_path_ratio=0.0, load_balance=0.0
            )
    
    def _calculate_lower_bound(self, processing_matrix, job_lengths):
        """计算makespan的理论下界"""
        # 机器负载下界
        machine_loads = np.sum(processing_matrix, axis=0)
        machine_bound = np.max(machine_loads)
        
        # 作业长度下界
        job_bounds = []
        op_idx = 0
        for job_length in job_lengths:
            job_time = 0
            for _ in range(job_length):
                if op_idx < processing_matrix.shape[0]:
                    min_time = np.min(processing_matrix[op_idx][processing_matrix[op_idx] > 0])
                    job_time += min_time
                    op_idx += 1
            job_bounds.append(job_time)
        
        job_bound = max(job_bounds) if job_bounds else 0
        
        return max(machine_bound, job_bound)


class SequenceQualityLoss(nn.Module):
    """序列质量损失函数"""
    
    def __init__(self, alpha=1.0, beta=0.5):
        super().__init__()
        self.alpha = alpha  # 序列一致性权重
        self.beta = beta    # 决策质量权重
    
    def forward(self, operation_scores, machine_probs, valid_actions_mask):
        """
        计算序列质量损失
        
        Args:
            operation_scores: 操作选择评分 [batch, steps, n_ops]
            machine_probs: 机器选择概率 [batch, steps, n_ops, n_machines]
            valid_actions_mask: 有效动作掩码
        """
        # 1. 操作选择一致性损失
        op_consistency_loss = self._calculate_consistency_loss(operation_scores)
        
        # 2. 机器选择质量损失
        machine_quality_loss = self._calculate_machine_quality_loss(machine_probs)
        
        total_loss = self.alpha * op_consistency_loss + self.beta * machine_quality_loss
        
        return total_loss
    
    def _calculate_consistency_loss(self, operation_scores):
        """计算操作选择的一致性损失"""
        # 鼓励在相似状态下做出一致的决策
        if operation_scores.size(1) < 2:
            return torch.tensor(0.0)
        
        # 计算相邻步骤之间的评分变化
        score_diff = torch.diff(operation_scores, dim=1)
        consistency_loss = torch.mean(torch.abs(score_diff))
        
        return consistency_loss
    
    def _calculate_machine_quality_loss(self, machine_probs):
        """计算机器选择的质量损失"""
        # 鼓励明确的机器选择（避免过于平均的概率分布）
        entropy = -torch.sum(machine_probs * torch.log(machine_probs + 1e-8), dim=-1)
        quality_loss = torch.mean(entropy)
        
        return quality_loss


class MultiObjectiveLoss(nn.Module):
    """多目标损失函数"""
    
    def __init__(self, makespan_weight=2.0, quality_weight=1.0, efficiency_weight=0.5):
        super().__init__()
        self.makespan_loss = MakespanFocusedLoss()
        self.quality_loss = SequenceQualityLoss()
        
        self.makespan_weight = makespan_weight
        self.quality_weight = quality_weight
        self.efficiency_weight = efficiency_weight
    
    def forward(self, predicted_columns, target_makespan, processing_matrix, job_lengths,
                operation_scores=None, machine_probs=None, valid_actions_mask=None):
        """
        计算多目标损失
        """
        # 1. Makespan导向损失
        makespan_loss = self.makespan_loss(
            predicted_columns, target_makespan, processing_matrix, job_lengths
        )
        
        total_loss = self.makespan_weight * makespan_loss
        
        # 2. 序列质量损失（如果提供了中间输出）
        if operation_scores is not None and machine_probs is not None:
            quality_loss = self.quality_loss(
                operation_scores, machine_probs, valid_actions_mask
            )
            total_loss += self.quality_weight * quality_loss
        
        # 3. 效率损失
        if predicted_columns:
            efficiency_loss = self._calculate_efficiency_loss(predicted_columns)
            total_loss += self.efficiency_weight * efficiency_loss
        
        return total_loss
    
    def _calculate_efficiency_loss(self, predicted_columns):
        """计算效率损失"""
        # 鼓励生成多样化但高质量的列
        if len(predicted_columns) < 2:
            return torch.tensor(0.0)
        
        # 计算列之间的多样性
        diversity_scores = []
        for i in range(len(predicted_columns)):
            for j in range(i + 1, len(predicted_columns)):
                col1, col2 = predicted_columns[i], predicted_columns[j]
                if col1.feasible and col2.feasible:
                    diversity = self._calculate_schedule_diversity(col1.schedule, col2.schedule)
                    diversity_scores.append(diversity)
        
        if diversity_scores:
            avg_diversity = np.mean(diversity_scores)
            # 鼓励适度的多样性（不要太相似，也不要太不同）
            diversity_loss = abs(avg_diversity - 0.3)  # 目标多样性为0.3
        else:
            diversity_loss = 1.0
        
        return torch.tensor(diversity_loss)
    
    def _calculate_schedule_diversity(self, schedule1, schedule2):
        """计算两个调度之间的多样性"""
        if not schedule1 or not schedule2:
            return 0.0
        
        # 计算操作-机器分配的差异
        set1 = set(schedule1)
        set2 = set(schedule2)
        
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        diversity = 1.0 - (intersection / union) if union > 0 else 0.0
        return diversity


class AdaptiveLoss(nn.Module):
    """自适应损失函数"""
    
    def __init__(self, initial_weights=None):
        super().__init__()
        if initial_weights is None:
            initial_weights = [2.0, 1.0, 0.5]  # makespan, quality, efficiency
        
        # 可学习的权重参数
        self.weights = nn.Parameter(torch.tensor(initial_weights, dtype=torch.float32))
        self.multi_objective_loss = MultiObjectiveLoss()
        
        # 权重历史记录
        self.weight_history = []
    
    def forward(self, predicted_columns, target_makespan, processing_matrix, job_lengths,
                operation_scores=None, machine_probs=None, valid_actions_mask=None):
        """
        自适应权重的多目标损失
        """
        # 使用softmax确保权重为正且和为1
        normalized_weights = F.softmax(self.weights, dim=0) * len(self.weights)
        
        # 更新多目标损失的权重
        self.multi_objective_loss.makespan_weight = normalized_weights[0]
        self.multi_objective_loss.quality_weight = normalized_weights[1]
        self.multi_objective_loss.efficiency_weight = normalized_weights[2]
        
        # 记录权重历史
        self.weight_history.append(normalized_weights.detach().cpu().numpy())
        
        return self.multi_objective_loss(
            predicted_columns, target_makespan, processing_matrix, job_lengths,
            operation_scores, machine_probs, valid_actions_mask
        )
    
    def get_current_weights(self):
        """获取当前权重"""
        return F.softmax(self.weights, dim=0) * len(self.weights)


def create_improved_loss(loss_type='multi_objective', **kwargs):
    """创建改进的损失函数"""
    if loss_type == 'makespan_focused':
        return MakespanFocusedLoss(**kwargs)
    elif loss_type == 'sequence_quality':
        return SequenceQualityLoss(**kwargs)
    elif loss_type == 'multi_objective':
        return MultiObjectiveLoss(**kwargs)
    elif loss_type == 'adaptive':
        return AdaptiveLoss(**kwargs)
    else:
        raise ValueError(f"Unknown loss type: {loss_type}")


def test_improved_loss():
    """测试改进的损失函数"""
    print("🧪 Testing Improved Loss Functions")
    print("=" * 50)
    
    # 创建测试数据
    from valid_schedule_neural_model import ValidScheduleOutput
    
    # 模拟调度列
    schedule1 = [(0, 0), (1, 1), (2, 0), (3, 1)]
    schedule2 = [(0, 1), (1, 0), (2, 1), (3, 0)]
    
    columns = [
        ValidScheduleOutput(schedule=schedule1, makespan=100.0, feasible=True, quality_score=0.8),
        ValidScheduleOutput(schedule=schedule2, makespan=110.0, feasible=True, quality_score=0.7)
    ]
    
    processing_matrix = torch.tensor([[10, 15, 0, 0],
                                    [0, 12, 18, 0],
                                    [14, 0, 16, 0],
                                    [0, 11, 0, 13]], dtype=torch.float32)
    
    job_lengths = [2, 2]
    target_makespan = 95.0
    
    # 测试不同损失函数
    loss_functions = {
        'Makespan Focused': MakespanFocusedLoss(),
        'Multi-Objective': MultiObjectiveLoss(),
        'Adaptive': AdaptiveLoss()
    }
    
    for name, loss_fn in loss_functions.items():
        try:
            loss_value = loss_fn(columns, target_makespan, processing_matrix, job_lengths)
            print(f"✅ {name}: {loss_value.item():.4f}")
        except Exception as e:
            print(f"❌ {name}: Error - {e}")
    
    print("✅ Loss function testing completed!")


if __name__ == "__main__":
    test_improved_loss()
