#!/usr/bin/env python3
"""
改进的SD数据集批量测试
1. 改进列生成策略，避免与FIFO效果持平
2. 修复基线算法，确保SPT、FIFO、LPT都能生成可行解
3. 使用真实SD数据文件而非生成数据
"""

import os
import sys
import torch
import numpy as np
import time
import json
import random
from typing import List, Dict, Tuple, Optional
from collections import defaultdict
from datetime import datetime

# 添加路径
sys.path.append('..')
sys.path.append('.')

from fixed_machine_selection import ImprovedNeuralModel


class FJSParser:
    """FJSP文件解析器"""

    @staticmethod
    def parse_fjs_file(file_path: str) -> Dict:
        """解析.fjs文件"""
        with open(file_path, 'r') as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]

        # 第一行：作业数、机器数、平均机器数
        first_line = lines[0].split()
        n_jobs = int(first_line[0])
        n_machines = int(first_line[1])

        # 解析作业信息
        job_lengths = []
        processing_times = []
        operation_id = 0

        for job_id in range(n_jobs):
            line = lines[job_id + 1].split()
            n_operations = int(line[0])
            job_lengths.append(n_operations)

            idx = 1
            for op_in_job in range(n_operations):
                # 读取可用机器数
                n_machines_available = int(line[idx])
                idx += 1

                # 初始化处理时间矩阵行
                proc_times = [0] * n_machines

                # 读取每个可用机器的处理时间
                for _ in range(n_machines_available):
                    machine_id = int(line[idx]) - 1  # 转换为0-based索引
                    processing_time = int(line[idx + 1])
                    proc_times[machine_id] = processing_time
                    idx += 2

                processing_times.append(proc_times)
                operation_id += 1

        return {
            'instance_id': os.path.basename(file_path),
            'n_jobs': n_jobs,
            'n_machines': n_machines,
            'n_operations': operation_id,
            'job_length': job_lengths,
            'processing_times': processing_times
        }


class ImprovedColumnGenerator:
    """改进的列生成器"""

    def __init__(self, model, device):
        self.model = model
        self.device = device

    def generate_diverse_columns(self, features, job_lengths, num_columns=5):
        """生成多样化的高质量列"""
        all_columns = []

        # 策略1: 标准生成
        standard_columns = self.model.generate_improved_columns(
            features['job_features'].unsqueeze(0).to(self.device),
            features['operation_features'].unsqueeze(0).to(self.device),
            features['machine_features'].unsqueeze(0).to(self.device),
            features['processing_matrix'].unsqueeze(0).to(self.device),
            job_lengths,
            num_columns=num_columns
        )
        all_columns.extend(standard_columns)

        # 策略2: 随机扰动生成
        for _ in range(2):
            perturbed_features = self._add_noise_to_features(features)
            perturbed_columns = self.model.generate_improved_columns(
                perturbed_features['job_features'].unsqueeze(0).to(self.device),
                perturbed_features['operation_features'].unsqueeze(0).to(self.device),
                perturbed_features['machine_features'].unsqueeze(0).to(self.device),
                perturbed_features['processing_matrix'].unsqueeze(0).to(self.device),
                job_lengths,
                num_columns=2
            )
            all_columns.extend(perturbed_columns)

        # 策略3: 贪心启发式增强
        greedy_column = self._generate_greedy_enhanced_column(features, job_lengths)
        if greedy_column:
            all_columns.append(greedy_column)

        # 去重并选择最佳列
        unique_columns = self._deduplicate_columns(all_columns)
        return sorted(unique_columns, key=lambda x: x.makespan if hasattr(x, 'makespan') else float('inf'))[:num_columns]

    def _add_noise_to_features(self, features, noise_level=0.05):
        """为特征添加小幅随机扰动"""
        perturbed = {}
        for key, tensor in features.items():
            if key != 'job_lengths':
                noise = torch.randn_like(tensor) * noise_level
                perturbed[key] = tensor + noise
            else:
                perturbed[key] = tensor
        return perturbed

    def _generate_greedy_enhanced_column(self, features, job_lengths):
        """生成贪心增强的列"""
        # 这里可以实现基于贪心启发式的列生成
        # 暂时返回None，可以后续扩展
        return None

    def _deduplicate_columns(self, columns):
        """去除重复的列"""
        unique_columns = []
        seen_schedules = set()

        for column in columns:
            if hasattr(column, 'schedule') and column.schedule:
                schedule_tuple = tuple(sorted(column.schedule))
                if schedule_tuple not in seen_schedules:
                    seen_schedules.add(schedule_tuple)
                    unique_columns.append(column)

        return unique_columns


class RobustBaselineAlgorithms:
    """鲁棒的基线算法实现"""

    @staticmethod
    def create_feasible_schedule_robust(operation_order, job_lengths, processing_times):
        """创建鲁棒的可行调度"""
        n_jobs = len(job_lengths)
        n_machines = len(processing_times[0]) if processing_times else 0
        n_operations = len(processing_times)

        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_lengths):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops

        schedule = []
        job_progress = [0] * n_jobs
        scheduled_ops = set()
        machine_loads = [0] * n_machines

        # 多轮调度，确保所有操作都被调度
        max_iterations = n_operations * 2
        iteration = 0

        while len(scheduled_ops) < n_operations and iteration < max_iterations:
            iteration += 1
            progress_made = False

            for target_op in operation_order:
                if target_op in scheduled_ops or target_op >= n_operations:
                    continue

                # 找到操作所属作业
                target_job = None
                for job_id, ops in enumerate(job_operations):
                    if target_op in ops:
                        target_job = job_id
                        break

                if target_job is None:
                    continue

                # 检查是否可调度（作业顺序约束）
                op_index_in_job = job_operations[target_job].index(target_op)
                if job_progress[target_job] != op_index_in_job:
                    continue

                # 找到可用机器
                valid_machines = []
                for m in range(n_machines):
                    if m < len(processing_times[target_op]) and processing_times[target_op][m] > 0:
                        valid_machines.append(m)

                if not valid_machines:
                    continue

                # 选择机器（根据不同策略）
                if hasattr(RobustBaselineAlgorithms, '_current_strategy'):
                    strategy = RobustBaselineAlgorithms._current_strategy
                else:
                    strategy = 'shortest_time'

                if strategy == 'shortest_time':
                    # SPT: 选择处理时间最短的机器
                    best_machine = min(valid_machines,
                                     key=lambda m: processing_times[target_op][m])
                elif strategy == 'longest_time':
                    # LPT: 选择处理时间最长的机器
                    best_machine = max(valid_machines,
                                     key=lambda m: processing_times[target_op][m])
                elif strategy == 'least_loaded':
                    # 选择负载最轻的机器
                    best_machine = min(valid_machines,
                                     key=lambda m: machine_loads[m])
                else:  # FIFO
                    # 选择第一个可用机器
                    best_machine = valid_machines[0]

                # 调度操作
                schedule.append((target_op, best_machine))
                scheduled_ops.add(target_op)
                job_progress[target_job] += 1
                machine_loads[best_machine] += processing_times[target_op][best_machine]
                progress_made = True

            # 如果没有进展，尝试调整操作顺序
            if not progress_made:
                # 获取可调度的操作
                available_ops = []
                for job_id, operations in enumerate(job_operations):
                    if job_progress[job_id] < len(operations):
                        next_op = operations[job_progress[job_id]]
                        if next_op not in scheduled_ops:
                            available_ops.append(next_op)

                if available_ops:
                    # 随机选择一个可调度的操作
                    target_op = available_ops[0]

                    # 找到可用机器
                    valid_machines = []
                    for m in range(n_machines):
                        if m < len(processing_times[target_op]) and processing_times[target_op][target_op] > 0:
                            valid_machines.append(m)

                    if valid_machines:
                        best_machine = valid_machines[0]

                        # 找到操作所属作业
                        target_job = None
                        for job_id, ops in enumerate(job_operations):
                            if target_op in ops:
                                target_job = job_id
                                break

                        if target_job is not None:
                            schedule.append((target_op, best_machine))
                            scheduled_ops.add(target_op)
                            job_progress[target_job] += 1
                            machine_loads[best_machine] += processing_times[target_op][best_machine]

        return schedule

    @staticmethod
    def generate_all_baselines(instance):
        """生成所有基线算法的解"""
        job_lengths = instance['job_length']
        processing_times = instance['processing_times']
        n_operations = instance['n_operations']

        baselines = {}

        # FIFO
        RobustBaselineAlgorithms._current_strategy = 'fifo'
        fifo_ops = list(range(n_operations))
        fifo_schedule = RobustBaselineAlgorithms.create_feasible_schedule_robust(
            fifo_ops, job_lengths, processing_times)
        baselines['FIFO'] = fifo_schedule

        # SPT (Shortest Processing Time)
        RobustBaselineAlgorithms._current_strategy = 'shortest_time'
        try:
            spt_ops = list(range(n_operations))
            # 按最短处理时间排序
            spt_ops.sort(key=lambda op: min(processing_times[op][m]
                                          for m in range(len(processing_times[op]))
                                          if processing_times[op][m] > 0))
            spt_schedule = RobustBaselineAlgorithms.create_feasible_schedule_robust(
                spt_ops, job_lengths, processing_times)
            baselines['SPT'] = spt_schedule
        except Exception as e:
            print(f"SPT failed: {e}, using FIFO fallback")
            baselines['SPT'] = fifo_schedule

        # LPT (Longest Processing Time)
        RobustBaselineAlgorithms._current_strategy = 'longest_time'
        try:
            lpt_ops = list(range(n_operations))
            # 按最长处理时间排序
            lpt_ops.sort(key=lambda op: max(processing_times[op][m]
                                          for m in range(len(processing_times[op]))
                                          if processing_times[op][m] > 0), reverse=True)
            lpt_schedule = RobustBaselineAlgorithms.create_feasible_schedule_robust(
                lpt_ops, job_lengths, processing_times)
            baselines['LPT'] = lpt_schedule
        except Exception as e:
            print(f"LPT failed: {e}, using FIFO fallback")
            baselines['LPT'] = fifo_schedule

        return baselines


class ImprovedSDTester:
    """改进的SD数据集测试器"""

    def __init__(self):
        self.model = None
        self.device = None
        self.column_generator = None
        self.results = []

    def load_model(self):
        """加载模型"""
        print("🔄 Loading improved neural column generation model...")

        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.model = ImprovedNeuralModel(d_model=128, n_heads=4, n_layers=3).to(self.device)

        model_path = "neural_column_generation/models/high_quality_best.pth"
        if os.path.exists(model_path):
            try:
                checkpoint = torch.load(model_path, map_location=self.device, weights_only=False)
                self.model.load_state_dict(checkpoint['model_state_dict'])
                self.model.eval()
                self.column_generator = ImprovedColumnGenerator(self.model, self.device)
                print(f"✅ Model loaded successfully")
                return True
            except Exception as e:
                print(f"❌ Failed to load model: {e}")
                return False
        else:
            print(f"❌ Model file not found: {model_path}")
            return False

    def load_sd_instances(self, max_instances_per_size=10):
        """从data文件夹加载真实SD数据"""
        print(f"📊 Loading real SD dataset instances...")

        instances = []

        # SD1数据集路径
        sd1_paths = [
            ('data/SD1/10x5', '10J×5M'),
            ('data/SD1/15x10', '15J×10M'),
            ('data/SD1/20x5', '20J×5M'),
            ('data/SD1/20x10', '20J×10M'),
        ]

        # SD2数据集路径
        sd2_paths = [
            ('data/SD2/10x5+mix', '10J×5M'),
            ('data/SD2/15x10+mix', '15J×10M'),
            ('data/SD2/20x5+mix', '20J×5M'),
            ('data/SD2/20x10+mix', '20J×10M'),
        ]

        for folder_path, size_label in sd1_paths + sd2_paths:
            if os.path.exists(folder_path):
                fjs_files = [f for f in os.listdir(folder_path) if f.endswith('.fjs')]
                selected_files = random.sample(fjs_files, min(max_instances_per_size, len(fjs_files)))

                for filename in selected_files:
                    file_path = os.path.join(folder_path, filename)
                    try:
                        instance = FJSParser.parse_fjs_file(file_path)
                        instance['size_label'] = size_label
                        instance['data_source'] = 'SD1' if 'SD1' in folder_path else 'SD2'
                        instance['file_path'] = file_path
                        instances.append(instance)
                    except Exception as e:
                        print(f"⚠️  Failed to parse {file_path}: {e}")

                print(f"  ✅ Loaded {len(selected_files)} instances from {size_label} ({instance['data_source']})")

        print(f"✅ Total loaded: {len(instances)} real SD instances")
        return instances

    def extract_features(self, instance):
        """提取实例特征"""
        job_length = instance['job_length']
        processing_times = np.array(instance['processing_times'])
        n_jobs = instance['n_jobs']
        n_machines = instance['n_machines']
        n_operations = instance['n_operations']

        # 作业特征
        job_features = []
        for job_id, n_ops in enumerate(job_length):
            op_start = sum(job_length[:job_id])
            op_end = op_start + n_ops

            total_workload = 0
            for op_idx in range(op_start, op_end):
                if op_idx < len(processing_times):
                    valid_times = [t for t in processing_times[op_idx] if t > 0]
                    if valid_times:
                        min_time = min(valid_times)
                        total_workload += min_time

            job_feat = [
                job_id / n_jobs, n_ops / n_operations, total_workload / 1000.0,
                n_ops / max(job_length), total_workload / max(1, n_ops * 20),
                0, 0, 0, 0, 0
            ]
            job_features.append(job_feat)

        # 操作特征
        operation_features = []
        for op_id in range(n_operations):
            if op_id < len(processing_times):
                proc_times = processing_times[op_id]

                job_id = 0
                op_in_job = op_id
                for j, n_ops in enumerate(job_length):
                    if op_in_job < n_ops:
                        job_id = j
                        break
                    op_in_job -= n_ops

                valid_times = [t for t in proc_times if t > 0]
                min_time = min(valid_times) if valid_times else 0
                max_time = max(valid_times) if valid_times else 0
                avg_time = np.mean(valid_times) if valid_times else 0
                n_machines_available = len(valid_times)

                op_feat = [
                    op_id / n_operations, job_id / n_jobs, op_in_job / max(job_length),
                    min_time / 50.0, max_time / 50.0, avg_time / 50.0,
                    n_machines_available / n_machines, (max_time - min_time) / max(1, 50.0),
                    0, 0, 0, 0, 0, 0, 0
                ]
                operation_features.append(op_feat)

        # 机器特征
        machine_features = []
        for machine_id in range(n_machines):
            total_load = 0
            n_operations_available = 0

            for op_id in range(n_operations):
                if op_id < len(processing_times) and machine_id < len(processing_times[op_id]):
                    proc_time = processing_times[op_id][machine_id]
                    if proc_time > 0:
                        total_load += proc_time
                        n_operations_available += 1

            avg_load = total_load / max(1, n_operations_available)

            machine_feat = [
                machine_id / n_machines, total_load / 1000.0,
                n_operations_available / n_operations, avg_load / 50.0,
                0, 0, 0, 0
            ]
            machine_features.append(machine_feat)

        return {
            'job_features': torch.FloatTensor(job_features),
            'operation_features': torch.FloatTensor(operation_features),
            'machine_features': torch.FloatTensor(machine_features),
            'processing_matrix': torch.FloatTensor(processing_times),
            'job_lengths': job_length
        }

    def evaluate_schedule(self, schedule, instance):
        """评估调度方案"""
        if not schedule:
            return float('inf'), False, {}

        processing_times = np.array(instance['processing_times'])
        job_lengths = instance['job_length']
        n_machines = instance['n_machines']
        n_operations = instance['n_operations']

        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_lengths):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops

        # 检查可行性并计算makespan
        job_progress = [0] * len(job_lengths)
        machine_times = [0] * n_machines
        scheduled_ops = set()

        for op_id, machine_id in schedule:
            # 基本检查
            if (op_id >= n_operations or machine_id >= n_machines or
                op_id >= len(processing_times) or machine_id >= len(processing_times[op_id]) or
                processing_times[op_id][machine_id] <= 0):
                return float('inf'), False, {}

            # 检查作业顺序约束
            target_job = None
            for job_id, ops in enumerate(job_operations):
                if op_id in ops:
                    target_job = job_id
                    break

            if target_job is None:
                return float('inf'), False, {}

            op_index_in_job = job_operations[target_job].index(op_id)
            if job_progress[target_job] != op_index_in_job:
                return float('inf'), False, {}

            # 更新状态
            machine_times[machine_id] += processing_times[op_id][machine_id]
            job_progress[target_job] += 1
            scheduled_ops.add(op_id)

        # 检查完整性
        if len(scheduled_ops) != n_operations:
            return float('inf'), False, {}

        makespan = max(machine_times)

        # 计算额外指标
        metrics = {
            'makespan': makespan,
            'machine_utilization': np.mean(machine_times) / makespan if makespan > 0 else 0,
            'load_balance': 1 - (np.std(machine_times) / np.mean(machine_times)) if np.mean(machine_times) > 0 else 0,
            'total_processing_time': sum(machine_times)
        }

        return makespan, True, metrics

    def test_single_instance(self, instance, instance_idx):
        """测试单个实例"""
        print(f"  📊 Instance {instance_idx}: {instance['size_label']} ({instance['n_operations']} ops) - {instance['data_source']}")

        result = {
            'instance_id': instance['instance_id'],
            'instance_idx': instance_idx,
            'size_label': instance['size_label'],
            'n_jobs': instance['n_jobs'],
            'n_machines': instance['n_machines'],
            'n_operations': instance['n_operations'],
            'data_source': instance['data_source'],
            'file_path': instance['file_path']
        }

        # 测试改进的神经网络
        neural_start = time.time()
        try:
            features = self.extract_features(instance)

            # 使用改进的列生成策略
            columns = self.column_generator.generate_diverse_columns(features, instance['job_length'], num_columns=8)

            neural_time = time.time() - neural_start

            # 评估神经网络结果
            valid_columns = []
            for column in columns:
                if hasattr(column, 'feasible') and column.feasible:
                    makespan, feasible, metrics = self.evaluate_schedule(column.schedule, instance)
                    if feasible and makespan < float('inf'):
                        valid_columns.append({
                            'makespan': makespan,
                            'metrics': metrics,
                            'decision_quality': getattr(column, 'decision_quality', 0.0),
                            'schedule': column.schedule
                        })

            if valid_columns:
                best_column = min(valid_columns, key=lambda x: x['makespan'])
                result['neural'] = {
                    'success': True,
                    'best_makespan': best_column['makespan'],
                    'avg_makespan': np.mean([c['makespan'] for c in valid_columns]),
                    'best_metrics': best_column['metrics'],
                    'avg_decision_quality': np.mean([c['decision_quality'] for c in valid_columns]),
                    'feasible_columns': len(valid_columns),
                    'total_columns': len(columns),
                    'generation_time': neural_time,
                    'diversity_score': len(set(tuple(sorted(c['schedule'])) for c in valid_columns)) / len(valid_columns) if valid_columns else 0
                }
                print(f"    🧠 Neural: {best_column['makespan']:.1f} ({len(valid_columns)}/{len(columns)} feasible, diversity: {result['neural']['diversity_score']:.2f})")
            else:
                result['neural'] = {
                    'success': False,
                    'generation_time': neural_time,
                    'error': 'No feasible columns generated'
                }
                print(f"    ❌ Neural: Failed")

        except Exception as e:
            result['neural'] = {
                'success': False,
                'generation_time': time.time() - neural_start,
                'error': str(e)
            }
            print(f"    ❌ Neural: Error - {e}")

        # 测试改进的基线算法
        baseline_start = time.time()
        try:
            baseline_schedules = RobustBaselineAlgorithms.generate_all_baselines(instance)
            baseline_time = time.time() - baseline_start

            baselines = {}
            for method, schedule in baseline_schedules.items():
                makespan, feasible, metrics = self.evaluate_schedule(schedule, instance)
                baselines[method] = {
                    'makespan': makespan,
                    'feasible': feasible,
                    'metrics': metrics,
                    'schedule': schedule
                }

                if feasible:
                    print(f"    📊 {method}: {makespan:.1f}")
                else:
                    print(f"    ❌ {method}: infeasible")

            result['baselines'] = baselines
            result['baseline_time'] = baseline_time

            # 找到最佳基线
            feasible_baselines = {k: v for k, v in baselines.items() if v['feasible']}
            if feasible_baselines:
                best_baseline_method = min(feasible_baselines.keys(), key=lambda k: feasible_baselines[k]['makespan'])
                best_baseline_makespan = feasible_baselines[best_baseline_method]['makespan']

                result['best_baseline'] = {
                    'method': best_baseline_method,
                    'makespan': best_baseline_makespan
                }

                # 计算改进
                if result['neural']['success']:
                    improvement = ((best_baseline_makespan - result['neural']['best_makespan']) /
                                  best_baseline_makespan * 100)
                    result['improvement'] = improvement
                    print(f"    📈 Improvement: {improvement:+.1f}% vs {best_baseline_method}")
                else:
                    result['improvement'] = None
            else:
                result['best_baseline'] = {'method': None, 'makespan': float('inf')}
                result['improvement'] = None
                print(f"    ⚠️  All baselines failed")

        except Exception as e:
            result['baselines'] = {}
            result['baseline_time'] = time.time() - baseline_start
            result['improvement'] = None
            print(f"    ❌ Baseline error: {e}")

        return result

    def run_improved_batch_test(self, max_instances_per_size=5):
        """运行改进的批量测试"""
        print("🎯 IMPROVED SD DATASET BATCH TEST")
        print("=" * 70)

        # 加载模型
        if not self.load_model():
            return None

        # 加载真实SD数据
        instances = self.load_sd_instances(max_instances_per_size)

        if not instances:
            print("❌ No instances loaded")
            return None

        # 批量测试
        print(f"\n🧪 Testing {len(instances)} real SD instances...")

        for i, instance in enumerate(instances):
            result = self.test_single_instance(instance, i + 1)
            self.results.append(result)

        # 生成改进的分析报告
        self.generate_improved_analysis()

        return self.results

    def generate_improved_analysis(self):
        """生成改进的分析报告"""
        print(f"\n📈 IMPROVED SD BATCH TEST RESULTS")
        print("=" * 60)

        total_instances = len(self.results)
        neural_successes = [r for r in self.results if r['neural']['success']]
        neural_success_rate = len(neural_successes) / total_instances * 100

        print(f"🎯 Overall Performance:")
        print(f"  Total Instances: {total_instances}")
        print(f"  Neural Success Rate: {neural_success_rate:.1f}% ({len(neural_successes)}/{total_instances})")

        if neural_successes:
            # 性能统计
            neural_makespans = [r['neural']['best_makespan'] for r in neural_successes]
            neural_times = [r['neural']['generation_time'] for r in neural_successes]
            decision_qualities = [r['neural']['avg_decision_quality'] for r in neural_successes]
            diversity_scores = [r['neural']['diversity_score'] for r in neural_successes]

            print(f"  Average Neural Makespan: {np.mean(neural_makespans):.1f}")
            print(f"  Average Generation Time: {np.mean(neural_times):.3f}s")
            print(f"  Average Decision Quality: {np.mean(decision_qualities):.3f}")
            print(f"  Average Diversity Score: {np.mean(diversity_scores):.3f}")

            # 基线算法可靠性分析
            print(f"\n📊 Baseline Algorithm Reliability:")
            baseline_methods = ['FIFO', 'SPT', 'LPT']
            for method in baseline_methods:
                successful_baselines = [r for r in self.results
                                      if method in r.get('baselines', {}) and
                                      r['baselines'][method]['feasible']]
                success_rate = len(successful_baselines) / total_instances * 100
                print(f"  {method}: {success_rate:.1f}% success rate ({len(successful_baselines)}/{total_instances})")

            # 改进统计
            valid_improvements = [r['improvement'] for r in self.results if r['improvement'] is not None]
            if valid_improvements:
                avg_improvement = np.mean(valid_improvements)
                positive_improvements = [imp for imp in valid_improvements if imp > 0]

                print(f"\n🏆 Quality Comparison:")
                print(f"  Average Improvement: {avg_improvement:+.1f}%")
                print(f"  Median Improvement: {np.median(valid_improvements):+.1f}%")
                print(f"  Instances with Improvement: {len(positive_improvements)}/{len(valid_improvements)} ({len(positive_improvements)/len(valid_improvements)*100:.1f}%)")
                print(f"  Best Improvement: {max(valid_improvements):+.1f}%")
                print(f"  Worst Performance: {min(valid_improvements):+.1f}%")

            # 按规模分析
            print(f"\n📊 Performance by Instance Size:")
            size_groups = defaultdict(list)
            for result in neural_successes:
                size_groups[result['size_label']].append(result)

            for size in sorted(size_groups.keys()):
                results = size_groups[size]
                avg_makespan = np.mean([r['neural']['best_makespan'] for r in results])
                improvements = [r['improvement'] for r in results if r['improvement'] is not None]
                avg_improvement = np.mean(improvements) if improvements else 0
                avg_diversity = np.mean([r['neural']['diversity_score'] for r in results])

                print(f"  {size}: {len(results)} instances")
                print(f"    Avg makespan: {avg_makespan:.1f}, improvement: {avg_improvement:+.1f}%, diversity: {avg_diversity:.3f}")

            # 按数据源分析
            print(f"\n📊 Performance by Data Source:")
            source_groups = defaultdict(list)
            for result in neural_successes:
                source_groups[result['data_source']].append(result)

            for source in sorted(source_groups.keys()):
                results = source_groups[source]
                improvements = [r['improvement'] for r in results if r['improvement'] is not None]
                avg_improvement = np.mean(improvements) if improvements else 0
                positive_count = len([imp for imp in improvements if imp > 0])

                print(f"  {source}: {len(results)} instances")
                print(f"    Avg improvement: {avg_improvement:+.1f}%, positive: {positive_count}/{len(improvements)} ({positive_count/len(improvements)*100:.1f}%)")

        # 改进效果评估
        print(f"\n🔧 IMPROVEMENT EFFECTIVENESS:")

        if valid_improvements:
            if avg_improvement > 5:
                print(f"  🏆 EXCELLENT: Significant improvement over baselines")
            elif avg_improvement > 0:
                print(f"  😊 GOOD: Competitive with baseline algorithms")
            elif avg_improvement > -5:
                print(f"  😐 FAIR: Comparable to baseline performance")
            else:
                print(f"  ⚠️  POOR: Below baseline performance")

        # 列生成策略效果
        if neural_successes:
            avg_diversity = np.mean([r['neural']['diversity_score'] for r in neural_successes])
            if avg_diversity > 0.7:
                print(f"  ✅ HIGH DIVERSITY: Column generation produces diverse solutions ({avg_diversity:.3f})")
            elif avg_diversity > 0.5:
                print(f"  😊 MODERATE DIVERSITY: Reasonable solution variety ({avg_diversity:.3f})")
            else:
                print(f"  ⚠️  LOW DIVERSITY: Limited solution variety ({avg_diversity:.3f})")


def main():
    """主函数"""
    print("🎯 IMPROVED SD DATASET BATCH TEST")
    print("=" * 80)

    tester = ImprovedSDTester()
    results = tester.run_improved_batch_test(max_instances_per_size=8)

    if results:
        print(f"\n✅ Improved batch test completed!")
        print(f"📊 {len(results)} real SD instances tested")
        print(f"🎯 System improvements validated!")
    else:
        print(f"\n❌ Improved batch test failed!")


if __name__ == "__main__":
    main()