#!/usr/bin/env python3
"""
综合最终测试
验证所有优化后的神经列生成系统性能
"""

import os
import sys
import torch
import numpy as np
import time
from typing import List, Dict, Tuple
import matplotlib.pyplot as plt

# 添加路径
sys.path.append('..')
sys.path.append('.')

from valid_schedule_neural_model import ValidScheduleNeuralModel
from daniel_consistent_data_generator import DANIELConsistentDataGenerator, DANIELTrainingConfig
from branch_price_integration import create_column_generator, FJSPInstance, ColumnGenerationRequest


def load_models():
    """加载不同版本的模型"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    models = {}
    
    # 1. 原始模型
    try:
        original_model = ValidScheduleNeuralModel(d_model=128, n_heads=4, n_layers=3).to(device)
        original_path = "neural_column_generation/models/final_best_model.pth"
        if os.path.exists(original_path):
            checkpoint = torch.load(original_path, map_location=device, weights_only=False)
            original_model.load_state_dict(checkpoint['model_state_dict'])
            original_model.eval()
            models['original'] = original_model
            print(f"✅ Original model loaded")
        else:
            print(f"⚠️  Original model not found")
    except Exception as e:
        print(f"❌ Failed to load original model: {e}")
    
    # 2. 质量优化模型
    try:
        quality_model = ValidScheduleNeuralModel(d_model=128, n_heads=4, n_layers=3).to(device)
        quality_path = "neural_column_generation/models/quality_optimized_best.pth"
        if os.path.exists(quality_path):
            checkpoint = torch.load(quality_path, map_location=device, weights_only=False)
            quality_model.load_state_dict(checkpoint['model_state_dict'])
            quality_model.eval()
            models['quality_optimized'] = quality_model
            print(f"✅ Quality optimized model loaded")
        else:
            print(f"⚠️  Quality optimized model not found")
    except Exception as e:
        print(f"❌ Failed to load quality optimized model: {e}")
    
    # 3. 压缩优化模型
    try:
        compressed_path = "neural_column_generation/models/optimized_model.pth"
        if os.path.exists(compressed_path):
            checkpoint = torch.load(compressed_path, map_location=device, weights_only=False)
            # 注意：压缩模型可能需要特殊处理
            print(f"✅ Compressed model found (may need special handling)")
            models['compressed'] = compressed_path  # 存储路径而非模型
        else:
            print(f"⚠️  Compressed model not found")
    except Exception as e:
        print(f"❌ Failed to load compressed model: {e}")
    
    return models, device


def create_test_instances(num_instances=5):
    """创建多样化的测试实例"""
    instances = []
    
    # 小规模实例
    config_small = DANIELTrainingConfig(n_j=5, n_m=3, data_source='SD1')
    generator_small = DANIELConsistentDataGenerator(config_small)
    small_instances = generator_small.sample_training_instances_like_daniel(2)
    
    # 中等规模实例
    config_medium = DANIELTrainingConfig(n_j=10, n_m=5, data_source='SD1')
    generator_medium = DANIELConsistentDataGenerator(config_medium)
    medium_instances = generator_medium.sample_training_instances_like_daniel(2)
    
    # 大规模实例
    config_large = DANIELTrainingConfig(n_j=15, n_m=8, data_source='SD1')
    generator_large = DANIELConsistentDataGenerator(config_large)
    large_instances = generator_large.sample_training_instances_like_daniel(1)
    
    # 转换为标准格式
    for instance_data in small_instances + medium_instances + large_instances:
        instance = FJSPInstance(
            n_jobs=instance_data['n_jobs'],
            n_machines=instance_data['n_machines'],
            n_operations=instance_data['n_operations'],
            job_lengths=instance_data['job_length'],
            processing_times=np.array(instance_data['processing_times']),
            instance_id=instance_data['instance_id']
        )
        instances.append(instance)
    
    return instances


def extract_features_for_model(instance):
    """提取模型特征"""
    job_length = instance.job_lengths
    processing_times = instance.processing_times
    n_jobs = instance.n_jobs
    n_machines = instance.n_machines
    n_operations = instance.n_operations
    
    # 作业特征
    job_features = []
    for job_id, n_ops in enumerate(job_length):
        op_start = sum(job_length[:job_id])
        op_end = op_start + n_ops
        
        total_workload = 0
        for op_idx in range(op_start, op_end):
            if op_idx < len(processing_times):
                min_time = min(t for t in processing_times[op_idx] if t > 0)
                total_workload += min_time
        
        job_feat = [
            job_id / n_jobs, n_ops / n_operations, total_workload / 1000.0,
            n_ops / max(job_length), total_workload / (n_ops * 20),
            0, 0, 0, 0, 0
        ]
        job_features.append(job_feat)
    
    # 操作特征
    operation_features = []
    for op_id in range(n_operations):
        if op_id < len(processing_times):
            proc_times = processing_times[op_id]
            
            job_id = 0
            op_in_job = op_id
            for j, n_ops in enumerate(job_length):
                if op_in_job < n_ops:
                    job_id = j
                    break
                op_in_job -= n_ops
            
            valid_times = [t for t in proc_times if t > 0]
            min_time = min(valid_times) if valid_times else 0
            max_time = max(valid_times) if valid_times else 0
            avg_time = np.mean(valid_times) if valid_times else 0
            n_machines_available = len(valid_times)
            
            op_feat = [
                op_id / n_operations, job_id / n_jobs, op_in_job / max(job_length),
                min_time / 50.0, max_time / 50.0, avg_time / 50.0,
                n_machines_available / n_machines, (max_time - min_time) / 50.0,
                0, 0, 0, 0, 0, 0, 0
            ]
            operation_features.append(op_feat)
    
    # 机器特征
    machine_features = []
    for machine_id in range(n_machines):
        total_load = 0
        n_operations_available = 0
        
        for op_id in range(n_operations):
            if op_id < len(processing_times) and machine_id < len(processing_times[op_id]):
                proc_time = processing_times[op_id][machine_id]
                if proc_time > 0:
                    total_load += proc_time
                    n_operations_available += 1
        
        avg_load = total_load / max(1, n_operations_available)
        
        machine_feat = [
            machine_id / n_machines, total_load / 1000.0,
            n_operations_available / n_operations, avg_load / 50.0,
            0, 0, 0, 0
        ]
        machine_features.append(machine_feat)
    
    return {
        'job_features': torch.FloatTensor(job_features),
        'operation_features': torch.FloatTensor(operation_features),
        'machine_features': torch.FloatTensor(machine_features),
        'processing_matrix': torch.FloatTensor(processing_times),
        'job_lengths': job_length
    }


def evaluate_schedule(schedule, instance):
    """评估调度"""
    if not schedule:
        return float('inf'), False
    
    try:
        from schedule_evaluator import AccurateScheduleEvaluator
        evaluator = AccurateScheduleEvaluator(instance.job_lengths, instance.processing_times)
        result = evaluator.evaluate_schedule(schedule)
        return result.makespan, result.feasible
    except:
        # 简单评估
        machine_times = [0] * instance.n_machines
        
        for op_id, machine_id in schedule:
            if (machine_id < instance.n_machines and 
                op_id < instance.n_operations and 
                instance.processing_times[op_id, machine_id] > 0):
                machine_times[machine_id] += instance.processing_times[op_id, machine_id]
        
        makespan = max(machine_times) if machine_times else float('inf')
        return makespan, makespan < float('inf')


def test_model_performance(model, instance, device, model_name):
    """测试单个模型性能"""
    features = extract_features_for_model(instance)
    
    start_time = time.time()
    
    try:
        with torch.no_grad():
            columns = model.generate_valid_columns(
                features['job_features'].unsqueeze(0).to(device),
                features['operation_features'].unsqueeze(0).to(device),
                features['machine_features'].unsqueeze(0).to(device),
                features['processing_matrix'].unsqueeze(0).to(device),
                features['job_lengths'],
                num_columns=5
            )
        
        generation_time = time.time() - start_time
        
        # 评估生成的列
        results = []
        for column in columns:
            if column.feasible:
                makespan, feasible = evaluate_schedule(column.schedule, instance)
                if feasible:
                    results.append({
                        'makespan': makespan,
                        'feasible': True,
                        'schedule_length': len(column.schedule),
                        'neural_decisions': getattr(column, 'neural_decisions', 0),
                        'fallback_decisions': getattr(column, 'fallback_decisions', 0)
                    })
        
        return {
            'model_name': model_name,
            'generation_time': generation_time,
            'columns_generated': len(columns),
            'feasible_columns': len(results),
            'best_makespan': min(r['makespan'] for r in results) if results else float('inf'),
            'avg_makespan': np.mean([r['makespan'] for r in results]) if results else float('inf'),
            'feasibility_rate': len(results) / len(columns) if columns else 0,
            'neural_decision_ratio': np.mean([
                r['neural_decisions'] / max(1, r['neural_decisions'] + r['fallback_decisions']) 
                for r in results
            ]) if results else 0,
            'success': len(results) > 0
        }
        
    except Exception as e:
        return {
            'model_name': model_name,
            'generation_time': time.time() - start_time,
            'error': str(e),
            'success': False
        }


def generate_baseline_solutions(instance):
    """生成基线解决方案"""
    # SPT启发式
    operations = list(range(instance.n_operations))
    operations.sort(key=lambda op: min(instance.processing_times[op, m] 
                                     for m in range(instance.n_machines) 
                                     if instance.processing_times[op, m] > 0))
    
    spt_schedule = create_feasible_schedule(operations, instance)
    spt_makespan, spt_feasible = evaluate_schedule(spt_schedule, instance)
    
    # FIFO启发式
    fifo_operations = list(range(instance.n_operations))
    fifo_schedule = create_feasible_schedule(fifo_operations, instance)
    fifo_makespan, fifo_feasible = evaluate_schedule(fifo_schedule, instance)
    
    return {
        'SPT': {'makespan': spt_makespan, 'feasible': spt_feasible},
        'FIFO': {'makespan': fifo_makespan, 'feasible': fifo_feasible}
    }


def create_feasible_schedule(operation_order, instance):
    """创建可行调度"""
    schedule = []
    job_progress = [0] * instance.n_jobs
    scheduled_ops = set()
    
    # 构建作业-操作映射
    job_operations = []
    op_id = 0
    for job_id, n_ops in enumerate(instance.job_lengths):
        job_operations.append(list(range(op_id, op_id + n_ops)))
        op_id += n_ops
    
    for target_op in operation_order:
        if target_op in scheduled_ops:
            continue
        
        # 找到操作所属作业
        target_job = None
        for job_id, ops in enumerate(job_operations):
            if target_op in ops:
                target_job = job_id
                break
        
        if target_job is None:
            continue
        
        # 检查是否可调度
        op_index_in_job = job_operations[target_job].index(target_op)
        if job_progress[target_job] == op_index_in_job:
            valid_machines = [m for m in range(instance.n_machines) 
                             if instance.processing_times[target_op, m] > 0]
            
            if valid_machines:
                best_machine = min(valid_machines, 
                                 key=lambda m: instance.processing_times[target_op, m])
                
                schedule.append((target_op, best_machine))
                scheduled_ops.add(target_op)
                job_progress[target_job] += 1
    
    return schedule


def run_comprehensive_test():
    """运行综合测试"""
    print("🎯 COMPREHENSIVE FINAL TEST - Neural Column Generation")
    print("=" * 70)
    
    # 加载模型
    models, device = load_models()
    
    if not models:
        print("❌ No models available for testing")
        return
    
    # 创建测试实例
    print(f"\n📊 Creating test instances...")
    instances = create_test_instances()
    print(f"✅ Created {len(instances)} test instances")
    
    # 测试结果
    all_results = []
    
    for i, instance in enumerate(instances):
        print(f"\n🧪 Testing Instance {i+1}: {instance.n_jobs}J×{instance.n_machines}M ({instance.n_operations} ops)")
        
        instance_results = {
            'instance_id': instance.instance_id,
            'size': f"{instance.n_jobs}J×{instance.n_machines}M",
            'n_operations': instance.n_operations,
            'models': {},
            'baselines': {}
        }
        
        # 测试神经网络模型
        for model_name, model in models.items():
            if model_name == 'compressed':
                continue  # 跳过压缩模型（需要特殊处理）
            
            print(f"  🧠 Testing {model_name} model...")
            result = test_model_performance(model, instance, device, model_name)
            instance_results['models'][model_name] = result
            
            if result['success']:
                print(f"    ✅ Best makespan: {result['best_makespan']:.1f}")
                print(f"    📊 Feasibility: {result['feasibility_rate']:.2%}")
                print(f"    ⚡ Time: {result['generation_time']:.3f}s")
            else:
                print(f"    ❌ Failed: {result.get('error', 'Unknown error')}")
        
        # 测试基线方法
        print(f"  📊 Testing baseline methods...")
        baseline_start = time.time()
        baselines = generate_baseline_solutions(instance)
        baseline_time = time.time() - baseline_start
        
        instance_results['baselines'] = baselines
        instance_results['baseline_time'] = baseline_time
        
        for method, result in baselines.items():
            if result['feasible']:
                print(f"    {method}: makespan={result['makespan']:.1f}")
            else:
                print(f"    {method}: infeasible")
        
        all_results.append(instance_results)
    
    # 分析结果
    print(f"\n📈 COMPREHENSIVE ANALYSIS")
    print("=" * 50)
    
    # 统计各模型性能
    model_stats = {}
    for model_name in ['original', 'quality_optimized']:
        if model_name in models:
            successes = []
            makespans = []
            times = []
            feasibility_rates = []
            
            for result in all_results:
                if model_name in result['models']:
                    model_result = result['models'][model_name]
                    if model_result['success']:
                        successes.append(1)
                        makespans.append(model_result['best_makespan'])
                        times.append(model_result['generation_time'])
                        feasibility_rates.append(model_result['feasibility_rate'])
                    else:
                        successes.append(0)
            
            model_stats[model_name] = {
                'success_rate': np.mean(successes) if successes else 0,
                'avg_makespan': np.mean(makespans) if makespans else float('inf'),
                'avg_time': np.mean(times) if times else 0,
                'avg_feasibility': np.mean(feasibility_rates) if feasibility_rates else 0
            }
    
    # 基线统计
    baseline_makespans = []
    for result in all_results:
        for method, baseline in result['baselines'].items():
            if baseline['feasible']:
                baseline_makespans.append(baseline['makespan'])
    
    avg_baseline_makespan = np.mean(baseline_makespans) if baseline_makespans else float('inf')
    
    # 打印统计结果
    print(f"🧠 Neural Network Models:")
    for model_name, stats in model_stats.items():
        print(f"  {model_name.upper()}:")
        print(f"    Success Rate: {stats['success_rate']:.1%}")
        print(f"    Avg Makespan: {stats['avg_makespan']:.1f}")
        print(f"    Avg Time: {stats['avg_time']:.3f}s")
        print(f"    Avg Feasibility: {stats['avg_feasibility']:.1%}")
        
        if stats['avg_makespan'] < float('inf') and avg_baseline_makespan < float('inf'):
            improvement = (avg_baseline_makespan - stats['avg_makespan']) / avg_baseline_makespan * 100
            print(f"    Quality vs Baseline: {improvement:+.1f}%")
        print()
    
    print(f"📊 Baseline Performance:")
    print(f"  Average Makespan: {avg_baseline_makespan:.1f}")
    
    # 最终评估
    print(f"\n🏁 FINAL EVALUATION:")
    
    if 'quality_optimized' in model_stats:
        quality_stats = model_stats['quality_optimized']
        
        if quality_stats['success_rate'] >= 0.8:
            print(f"  ✅ RELIABILITY: EXCELLENT ({quality_stats['success_rate']:.1%} success rate)")
        elif quality_stats['success_rate'] >= 0.6:
            print(f"  😊 RELIABILITY: GOOD ({quality_stats['success_rate']:.1%} success rate)")
        else:
            print(f"  ⚠️  RELIABILITY: NEEDS IMPROVEMENT ({quality_stats['success_rate']:.1%} success rate)")
        
        if quality_stats['avg_feasibility'] >= 0.9:
            print(f"  ✅ CONSTRAINT SATISFACTION: EXCELLENT ({quality_stats['avg_feasibility']:.1%})")
        elif quality_stats['avg_feasibility'] >= 0.7:
            print(f"  😊 CONSTRAINT SATISFACTION: GOOD ({quality_stats['avg_feasibility']:.1%})")
        else:
            print(f"  ⚠️  CONSTRAINT SATISFACTION: NEEDS IMPROVEMENT ({quality_stats['avg_feasibility']:.1%})")
        
        if quality_stats['avg_time'] <= 1.0:
            print(f"  ✅ SPEED: EXCELLENT ({quality_stats['avg_time']:.3f}s)")
        elif quality_stats['avg_time'] <= 2.0:
            print(f"  😊 SPEED: GOOD ({quality_stats['avg_time']:.3f}s)")
        else:
            print(f"  ⚠️  SPEED: NEEDS IMPROVEMENT ({quality_stats['avg_time']:.3f}s)")
        
        if (quality_stats['avg_makespan'] < float('inf') and 
            avg_baseline_makespan < float('inf')):
            improvement = (avg_baseline_makespan - quality_stats['avg_makespan']) / avg_baseline_makespan * 100
            
            if improvement >= 5:
                print(f"  🏆 QUALITY: EXCELLENT ({improvement:+.1f}% vs baseline)")
            elif improvement >= 0:
                print(f"  😊 QUALITY: COMPETITIVE ({improvement:+.1f}% vs baseline)")
            else:
                print(f"  ⚠️  QUALITY: BELOW BASELINE ({improvement:+.1f}% vs baseline)")
    
    # 系统就绪状态
    print(f"\n🚀 SYSTEM READINESS:")
    
    if ('quality_optimized' in model_stats and 
        model_stats['quality_optimized']['success_rate'] >= 0.8 and
        model_stats['quality_optimized']['avg_feasibility'] >= 0.9):
        print(f"  🎉 READY FOR PRODUCTION!")
        print(f"  ✅ Neural column generation system is fully functional")
        print(f"  🚀 Ready for branch-and-price integration")
    else:
        print(f"  🔧 NEEDS FURTHER DEVELOPMENT")
        print(f"  ⚠️  System requires additional optimization")
    
    return all_results


def main():
    """主函数"""
    print("🚀 NEURAL COLUMN GENERATION - COMPREHENSIVE FINAL TEST")
    print("=" * 80)
    
    results = run_comprehensive_test()
    
    print(f"\n📋 TEST COMPLETED")
    print(f"🎯 Neural column generation system evaluation finished!")


if __name__ == "__main__":
    main()
