#!/usr/bin/env python3
"""
修复的训练管道
解决Loss为inf的问题，确保训练过程稳定
"""

import os
import sys
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import DataLoader
from tqdm import tqdm
import time
from datetime import datetime

# 添加路径
sys.path.append('..')
sys.path.append('.')

from valid_schedule_neural_model import ValidScheduleNeuralModel
from final_training_pipeline import DANIELDataset


class StableLoss(nn.Module):
    """稳定的损失函数，避免inf和nan"""
    
    def __init__(self, alpha=1.0, beta=0.5, max_loss=100.0):
        super().__init__()
        self.alpha = alpha      # makespan损失权重
        self.beta = beta        # 可行性奖励权重
        self.max_loss = max_loss  # 最大损失值，防止爆炸
    
    def forward(self, predicted_columns, target_makespan):
        """计算稳定的损失"""
        if not predicted_columns:
            return torch.tensor(self.max_loss, requires_grad=True)
        
        # 确保target_makespan是有效值
        if not isinstance(target_makespan, (int, float)) or target_makespan <= 0:
            target_makespan = 100.0  # 默认值
        
        total_loss = 0.0
        valid_count = 0
        
        for column in predicted_columns:
            if hasattr(column, 'feasible') and column.feasible:
                # 检查makespan是否有效
                makespan = getattr(column, 'makespan', float('inf'))
                
                if makespan == float('inf') or makespan <= 0 or np.isnan(makespan):
                    # 无效makespan，给予惩罚
                    loss = self.max_loss * 0.5
                else:
                    # 计算相对误差，使用稳定的归一化
                    makespan_error = abs(makespan - target_makespan) / max(target_makespan, 1.0)
                    
                    # 限制误差范围，防止爆炸
                    makespan_error = min(makespan_error, 10.0)
                    
                    # 计算损失
                    makespan_loss = self.alpha * (makespan_error ** 2)
                    feasibility_bonus = self.beta
                    
                    loss = makespan_loss - feasibility_bonus
                    
                    # 限制单个损失的范围
                    loss = max(-self.max_loss, min(loss, self.max_loss))
                
                total_loss += loss
                valid_count += 1
            else:
                # 不可行解的惩罚
                total_loss += self.max_loss * 0.8
        
        # 计算平均损失
        if valid_count == 0:
            final_loss = self.max_loss
        else:
            final_loss = total_loss / len(predicted_columns)
        
        # 最终检查，确保损失值有效
        if np.isnan(final_loss) or np.isinf(final_loss):
            final_loss = self.max_loss
        
        return torch.tensor(float(final_loss), requires_grad=True)


class RobustTrainer:
    """鲁棒的训练器，处理各种异常情况"""
    
    def __init__(self, model, device, config=None):
        self.model = model.to(device)
        self.device = device
        
        # 训练配置
        self.config = config or {
            'learning_rate': 0.001,
            'weight_decay': 0.01,
            'num_epochs': 25,
            'patience': 8,
            'gradient_clip': 1.0,
            'max_loss': 50.0
        }
        
        # 优化器
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=self.config['learning_rate'],
            weight_decay=self.config['weight_decay']
        )
        
        # 学习率调度器
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=self.config['num_epochs'], eta_min=1e-6
        )
        
        # 稳定的损失函数
        self.criterion = StableLoss(
            alpha=1.0, 
            beta=0.5, 
            max_loss=self.config['max_loss']
        )
        
        # 训练历史
        self.train_history = {
            'loss': [],
            'makespan_error': [],
            'feasibility_rate': [],
            'learning_rate': [],
            'valid_batches': []
        }
        
        # 早停
        self.best_loss = float('inf')
        self.patience_counter = 0
        
        print(f"🛡️  Robust Trainer initialized")
        print(f"📱 Device: {self.device}")
        print(f"📊 Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        print(f"🎯 Max loss threshold: {self.config['max_loss']}")
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        epoch_loss = 0.0
        epoch_makespan_error = 0.0
        epoch_feasibility = 0.0
        valid_batches = 0
        total_batches = 0
        
        for batch in tqdm(train_loader, desc="Training"):
            total_batches += 1
            
            try:
                self.optimizer.zero_grad()
                
                # 准备数据
                features = batch['features'][0]
                target_makespan = batch['target_makespan'][0]
                
                # 数据验证
                if not isinstance(target_makespan, (int, float)) or target_makespan <= 0:
                    print(f"⚠️  Invalid target makespan: {target_makespan}, skipping batch")
                    continue
                
                # 前向传播
                predicted_columns = self.model.generate_valid_columns(
                    features['job_features'].unsqueeze(0).to(self.device),
                    features['operation_features'].unsqueeze(0).to(self.device),
                    features['machine_features'].unsqueeze(0).to(self.device),
                    features['processing_matrix'].unsqueeze(0).to(self.device),
                    features['job_lengths'],
                    num_columns=3
                )
                
                # 验证生成的列
                if not predicted_columns:
                    print(f"⚠️  No columns generated, skipping batch")
                    continue
                
                # 计算损失
                loss = self.criterion(predicted_columns, target_makespan)
                
                # 检查损失值
                if torch.isnan(loss) or torch.isinf(loss):
                    print(f"⚠️  Invalid loss detected: {loss.item()}, skipping batch")
                    continue
                
                if loss.item() > self.config['max_loss'] * 2:
                    print(f"⚠️  Extremely high loss: {loss.item()}, skipping batch")
                    continue
                
                # 反向传播
                loss.backward()
                
                # 检查梯度
                total_norm = 0
                for p in self.model.parameters():
                    if p.grad is not None:
                        param_norm = p.grad.data.norm(2)
                        total_norm += param_norm.item() ** 2
                total_norm = total_norm ** (1. / 2)
                
                if np.isnan(total_norm) or np.isinf(total_norm):
                    print(f"⚠️  Invalid gradients detected, skipping batch")
                    self.optimizer.zero_grad()
                    continue
                
                # 梯度裁剪
                torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    max_norm=self.config['gradient_clip']
                )
                
                self.optimizer.step()
                
                # 统计指标
                epoch_loss += loss.item()
                valid_batches += 1
                
                # 计算其他指标
                if predicted_columns:
                    valid_makespans = []
                    feasible_count = 0
                    
                    for col in predicted_columns:
                        if hasattr(col, 'feasible') and col.feasible:
                            feasible_count += 1
                            makespan = getattr(col, 'makespan', float('inf'))
                            if makespan != float('inf') and makespan > 0 and not np.isnan(makespan):
                                valid_makespans.append(makespan)
                    
                    # 可行性率
                    feasibility_rate = feasible_count / len(predicted_columns)
                    epoch_feasibility += feasibility_rate
                    
                    # makespan误差
                    if valid_makespans:
                        best_makespan = min(valid_makespans)
                        makespan_error = abs(best_makespan - target_makespan) / target_makespan
                        epoch_makespan_error += makespan_error
                
            except Exception as e:
                print(f"❌ Training error in batch {total_batches}: {e}")
                # 清理梯度，继续下一个batch
                self.optimizer.zero_grad()
                continue
        
        # 更新学习率
        self.scheduler.step()
        
        # 计算平均指标
        if valid_batches > 0:
            avg_loss = epoch_loss / valid_batches
            avg_makespan_error = epoch_makespan_error / valid_batches
            avg_feasibility = epoch_feasibility / valid_batches
        else:
            avg_loss = self.config['max_loss']
            avg_makespan_error = 1.0
            avg_feasibility = 0.0
        
        return {
            'loss': avg_loss,
            'makespan_error': avg_makespan_error,
            'feasibility_rate': avg_feasibility,
            'valid_batches': valid_batches,
            'total_batches': total_batches
        }
    
    def train(self, train_loader):
        """完整训练过程"""
        print(f"🚀 Starting robust training for {self.config['num_epochs']} epochs...")
        
        for epoch in range(self.config['num_epochs']):
            print(f"\n📊 Epoch {epoch + 1}/{self.config['num_epochs']}")
            
            # 训练
            train_stats = self.train_epoch(train_loader)
            
            # 记录历史
            self.train_history['loss'].append(train_stats['loss'])
            self.train_history['makespan_error'].append(train_stats['makespan_error'])
            self.train_history['feasibility_rate'].append(train_stats['feasibility_rate'])
            self.train_history['learning_rate'].append(self.optimizer.param_groups[0]['lr'])
            self.train_history['valid_batches'].append(train_stats['valid_batches'])
            
            # 打印统计
            print(f"  📈 Loss: {train_stats['loss']:.4f}")
            print(f"  📊 Makespan Error: {train_stats['makespan_error']:.4f}")
            print(f"  ✅ Feasibility Rate: {train_stats['feasibility_rate']:.3f}")
            print(f"  📊 Valid Batches: {train_stats['valid_batches']}/{train_stats['total_batches']}")
            print(f"  📊 LR: {self.optimizer.param_groups[0]['lr']:.6f}")
            
            # 早停检查
            current_loss = train_stats['loss']
            if current_loss < self.best_loss and train_stats['valid_batches'] > 0:
                self.best_loss = current_loss
                self.patience_counter = 0
                self.save_model("neural_column_generation/models/robust_best_model.pth")
                print(f"  🎯 New best model saved! Loss: {current_loss:.4f}")
            else:
                self.patience_counter += 1
                if self.patience_counter >= self.config['patience']:
                    print(f"  ⏹️  Early stopping triggered!")
                    break
            
            # 检查训练健康状况
            if train_stats['valid_batches'] == 0:
                print(f"  ⚠️  No valid batches in this epoch!")
                if epoch > 5:  # 给模型一些时间适应
                    print(f"  🛑 Stopping training due to persistent failures")
                    break
            
            # 定期保存
            if (epoch + 1) % 10 == 0:
                self.save_checkpoint(epoch + 1)
        
        print(f"✅ Robust training completed!")
        print(f"📊 Best loss achieved: {self.best_loss:.4f}")
        return self.train_history
    
    def save_model(self, path):
        """保存模型"""
        model_data = {
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'train_history': self.train_history,
            'config': self.config,
            'best_loss': self.best_loss,
            'timestamp': datetime.now().isoformat()
        }
        
        os.makedirs(os.path.dirname(path), exist_ok=True)
        torch.save(model_data, path)
    
    def save_checkpoint(self, epoch):
        """保存检查点"""
        checkpoint_path = f"neural_column_generation/models/checkpoints/robust_epoch_{epoch}.pth"
        self.save_model(checkpoint_path)
        print(f"  💾 Checkpoint saved: epoch {epoch}")


def main():
    """主函数"""
    print("🛡️  Fixed Training Pipeline - Robust Loss Handling")
    print("=" * 60)
    
    # 检查数据集
    dataset_path = "neural_column_generation/daniel_data/daniel_consistent_dataset.pkl"
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset not found: {dataset_path}")
        print(f"Please run daniel_consistent_data_generator.py first")
        return
    
    # 创建数据集
    dataset = DANIELDataset(dataset_path)
    
    def custom_collate_fn(batch):
        return {
            'features': [item['features'] for item in batch],
            'target_schedule': [item['target_schedule'] for item in batch],
            'target_makespan': [item['target_makespan'] for item in batch],
            'instance_id': [item['instance_id'] for item in batch]
        }
    
    train_loader = DataLoader(
        dataset, batch_size=1, shuffle=True, collate_fn=custom_collate_fn
    )
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = ValidScheduleNeuralModel(d_model=128, n_heads=4, n_layers=3)
    
    print(f"📱 Device: {device}")
    print(f"📊 Training samples: {len(dataset)}")
    
    # 训练配置
    config = {
        'learning_rate': 0.001,
        'weight_decay': 0.01,
        'num_epochs': 20,
        'patience': 6,
        'gradient_clip': 1.0,
        'max_loss': 50.0
    }
    
    # 创建鲁棒训练器
    trainer = RobustTrainer(model, device, config)
    
    # 开始训练
    history = trainer.train(train_loader)
    
    print(f"\n✅ Fixed training completed!")
    print(f"📊 Best loss: {trainer.best_loss:.4f}")
    print(f"📊 Final feasibility rate: {history['feasibility_rate'][-1]:.3f}")
    print(f"🎯 Model saved to: neural_column_generation/models/robust_best_model.pth")
    
    # 训练历史分析
    if len(history['loss']) > 0:
        print(f"\n📈 Training History Analysis:")
        print(f"  Initial loss: {history['loss'][0]:.4f}")
        print(f"  Final loss: {history['loss'][-1]:.4f}")
        print(f"  Loss reduction: {(history['loss'][0] - history['loss'][-1]):.4f}")
        print(f"  Best feasibility rate: {max(history['feasibility_rate']):.3f}")
        
        # 检查训练稳定性
        recent_losses = history['loss'][-5:] if len(history['loss']) >= 5 else history['loss']
        loss_std = np.std(recent_losses)
        print(f"  Recent loss stability (std): {loss_std:.4f}")
        
        if loss_std < 1.0:
            print(f"  ✅ Training appears stable")
        else:
            print(f"  ⚠️  Training may be unstable")


if __name__ == "__main__":
    main()
