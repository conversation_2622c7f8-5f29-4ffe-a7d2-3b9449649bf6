#!/usr/bin/env python3
"""
优化的神经网络模型
减少启发式回退，提升神经网络决策质量
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass
import math


@dataclass
class OptimizedScheduleOutput:
    """优化的调度输出"""
    schedule: List[Tuple[int, int]]
    makespan: float
    feasible: bool
    quality_score: float
    neural_decisions: int = 0  # 神经网络决策数
    fallback_decisions: int = 0  # 启发式回退数


class AttentionBasedFeatureExtractor(nn.Module):
    """基于注意力的特征提取器"""

    def __init__(self, d_model=256, n_heads=8):
        super().__init__()
        self.d_model = d_model

        # 多层特征编码器
        self.job_encoder = nn.Sequential(
            nn.Linear(10, d_model // 4),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 4, d_model // 2),
            nn.ReL<PERSON>(),
            nn.Linear(d_model // 2, d_model),
            nn.LayerNorm(d_model)
        )

        self.operation_encoder = nn.Sequential(
            nn.Linear(15, d_model // 4),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 4, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, d_model),
            nn.LayerNorm(d_model)
        )

        self.machine_encoder = nn.Sequential(
            nn.Linear(8, d_model // 4),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 4, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, d_model),
            nn.LayerNorm(d_model)
        )

        # 跨模态注意力
        self.cross_attention = nn.MultiheadAttention(
            embed_dim=d_model,
            num_heads=n_heads,
            dropout=0.1,
            batch_first=True
        )

        # 上下文融合层
        self.context_fusion = nn.Sequential(
            nn.Linear(d_model * 3, d_model * 2),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model * 2, d_model),
            nn.LayerNorm(d_model)
        )

    def forward(self, job_features, operation_features, machine_features, processing_matrix):
        """前向传播"""
        batch_size = job_features.size(0)

        # 编码各类特征
        job_embeddings = self.job_encoder(job_features)  # [batch, n_jobs, d_model]
        operation_embeddings = self.operation_encoder(operation_features)  # [batch, n_ops, d_model]
        machine_embeddings = self.machine_encoder(machine_features)  # [batch, n_machines, d_model]

        # 跨模态注意力增强
        # 操作-机器注意力
        op_machine_attn, _ = self.cross_attention(
            operation_embeddings, machine_embeddings, machine_embeddings
        )

        # 操作-作业注意力
        op_job_attn, _ = self.cross_attention(
            operation_embeddings, job_embeddings, job_embeddings
        )

        # 融合上下文信息
        enhanced_op_embeddings = []
        for b in range(batch_size):
            for op_idx in range(operation_embeddings.size(1)):
                op_emb = operation_embeddings[b, op_idx]  # [d_model]
                op_mach_emb = op_machine_attn[b, op_idx]  # [d_model]
                op_job_emb = op_job_attn[b, op_idx]  # [d_model]

                # 融合三种表示
                combined = torch.cat([op_emb, op_mach_emb, op_job_emb], dim=0)  # [3*d_model]
                fused = self.context_fusion(combined.unsqueeze(0)).squeeze(0)  # [d_model]
                enhanced_op_embeddings.append(fused)

        # 重新组织为批次格式
        enhanced_operations = torch.stack(enhanced_op_embeddings).view(
            batch_size, operation_embeddings.size(1), self.d_model
        )

        return enhanced_operations, machine_embeddings


class SmartConstraintDecoder(nn.Module):
    """智能约束解码器"""

    def __init__(self, d_model=256, n_heads=8):
        super().__init__()
        self.d_model = d_model

        # 约束感知的操作选择器
        self.operation_selector = nn.Sequential(
            nn.Linear(d_model + 10, d_model),  # +10 for constraint features
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, 1)
        )

        # 智能机器选择器
        self.machine_selector = nn.Sequential(
            nn.Linear(d_model * 2 + 5, d_model),  # +5 for machine-operation features
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, 1)
        )

        # 决策置信度评估器
        self.confidence_estimator = nn.Sequential(
            nn.Linear(d_model, d_model // 4),
            nn.ReLU(),
            nn.Linear(d_model // 4, 1),
            nn.Sigmoid()
        )

        # 全局状态跟踪器
        self.state_tracker = nn.LSTM(
            input_size=d_model,
            hidden_size=d_model // 2,
            num_layers=2,
            batch_first=True,
            dropout=0.1
        )

    def forward(self, operation_embeddings, machine_embeddings, processing_matrix, job_lengths):
        """智能解码"""
        batch_size = operation_embeddings.size(0)
        schedules = []

        for b in range(batch_size):
            schedule = self._generate_smart_schedule(
                operation_embeddings[b],
                machine_embeddings[b],
                processing_matrix[b],
                job_lengths
            )
            schedules.append(schedule)

        return schedules

    def _generate_smart_schedule(self, op_embeddings, machine_embeddings, processing_matrix, job_lengths):
        """生成智能调度"""
        n_operations = op_embeddings.size(0)
        n_machines = machine_embeddings.size(0)

        schedule = []
        scheduled_ops = set()
        job_progress = [0] * len(job_lengths)
        neural_decisions = 0
        fallback_decisions = 0

        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_lengths):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops

        # 初始化LSTM状态
        hidden_state = None

        for step in range(n_operations):
            # 获取可调度的操作
            available_operations = self._get_available_operations(
                job_operations, job_progress, scheduled_ops
            )

            if not available_operations:
                break

            # 使用神经网络选择操作
            selected_op, confidence = self._smart_operation_selection(
                available_operations, op_embeddings, processing_matrix,
                scheduled_ops, job_progress
            )

            if confidence > 0.7:  # 高置信度使用神经网络决策
                neural_decisions += 1
            else:  # 低置信度使用启发式回退
                selected_op = self._fallback_operation_selection(
                    available_operations, processing_matrix
                )
                fallback_decisions += 1

            # 选择机器
            selected_machine, machine_confidence = self._smart_machine_selection(
                selected_op, machine_embeddings, processing_matrix
            )

            if machine_confidence < 0.5:  # 机器选择置信度低时使用启发式
                selected_machine = self._fallback_machine_selection(
                    selected_op, processing_matrix
                )
                fallback_decisions += 1
            else:
                neural_decisions += 1

            # 验证并添加到调度
            if self._is_valid_assignment(selected_op, selected_machine, processing_matrix):
                schedule.append((selected_op, selected_machine))
                scheduled_ops.add(selected_op)

                # 更新作业进度
                for job_id, ops in enumerate(job_operations):
                    if selected_op in ops:
                        job_progress[job_id] += 1
                        break

                # 更新LSTM状态
                op_state = op_embeddings[selected_op].unsqueeze(0).unsqueeze(0)
                _, hidden_state = self.state_tracker(op_state, hidden_state)

        return OptimizedScheduleOutput(
            schedule=schedule,
            makespan=0.0,  # 将在后续计算
            feasible=len(scheduled_ops) == n_operations,
            quality_score=0.0,  # 将在后续计算
            neural_decisions=neural_decisions,
            fallback_decisions=fallback_decisions
        )

    def _smart_operation_selection(self, available_operations, op_embeddings,
                                 processing_matrix, scheduled_ops, job_progress):
        """智能操作选择"""
        if len(available_operations) == 1:
            return available_operations[0], 1.0

        op_scores = []
        confidences = []

        for op_id in available_operations:
            # 构建约束特征
            constraint_features = self._build_constraint_features(
                op_id, scheduled_ops, job_progress, processing_matrix
            )

            # 结合操作嵌入和约束特征
            op_embedding = op_embeddings[op_id]
            combined_input = torch.cat([op_embedding, constraint_features], dim=0)

            # 计算评分和置信度
            score = self.operation_selector(combined_input.unsqueeze(0)).squeeze()
            confidence = self.confidence_estimator(op_embedding.unsqueeze(0)).squeeze()

            op_scores.append((score.item(), op_id))
            confidences.append(confidence.item())

        # 选择评分最高的操作
        op_scores.sort(reverse=True)
        selected_op = op_scores[0][1]
        avg_confidence = np.mean(confidences)

        return selected_op, avg_confidence

    def _smart_machine_selection(self, operation_id, machine_embeddings, processing_matrix):
        """智能机器选择"""
        valid_machines = [m for m in range(processing_matrix.size(1))
                         if processing_matrix[operation_id, m] > 0]

        if not valid_machines:
            return None, 0.0

        if len(valid_machines) == 1:
            return valid_machines[0], 1.0

        machine_scores = []
        confidences = []

        for machine_id in valid_machines:
            # 构建机器-操作特征
            machine_op_features = self._build_machine_operation_features(
                operation_id, machine_id, processing_matrix
            )

            # 结合机器嵌入和特征
            machine_embedding = machine_embeddings[machine_id]
            combined_input = torch.cat([
                machine_embedding,
                machine_embedding,  # 重复以匹配维度
                machine_op_features
            ], dim=0)

            # 计算评分和置信度
            score = self.machine_selector(combined_input.unsqueeze(0)).squeeze()
            confidence = self.confidence_estimator(machine_embedding.unsqueeze(0)).squeeze()

            machine_scores.append((score.item(), machine_id))
            confidences.append(confidence.item())

        # 选择评分最高的机器
        machine_scores.sort(reverse=True)
        selected_machine = machine_scores[0][1]
        avg_confidence = np.mean(confidences)

        return selected_machine, avg_confidence

    def _build_constraint_features(self, op_id, scheduled_ops, job_progress, processing_matrix):
        """构建约束特征"""
        features = []

        # 1. 操作紧急度（在作业中的位置）
        job_id = self._get_job_for_operation(op_id, job_progress)
        urgency = job_progress[job_id] / max(1, len(job_progress))
        features.append(urgency)

        # 2. 可用机器数量
        available_machines = torch.sum(processing_matrix[op_id] > 0).float()
        features.append(available_machines / processing_matrix.size(1))

        # 3. 最短处理时间
        valid_times = processing_matrix[op_id][processing_matrix[op_id] > 0]
        min_time = torch.min(valid_times) if len(valid_times) > 0 else 0
        features.append(min_time / 100.0)  # 归一化

        # 4. 平均处理时间
        avg_time = torch.mean(valid_times) if len(valid_times) > 0 else 0
        features.append(avg_time / 100.0)

        # 5. 处理时间方差
        time_var = torch.var(valid_times) if len(valid_times) > 1 else 0
        features.append(time_var / 100.0)

        # 6. 已调度操作比例
        scheduled_ratio = len(scheduled_ops) / processing_matrix.size(0)
        features.append(scheduled_ratio)

        # 填充到10维
        while len(features) < 10:
            features.append(0.0)

        return torch.tensor(features[:10], dtype=torch.float32)

    def _build_machine_operation_features(self, operation_id, machine_id, processing_matrix):
        """构建机器-操作特征"""
        features = []

        # 1. 处理时间
        proc_time = processing_matrix[operation_id, machine_id]
        features.append(proc_time / 100.0)

        # 2. 相对处理时间（与最短时间的比值）
        valid_times = processing_matrix[operation_id][processing_matrix[operation_id] > 0]
        min_time = torch.min(valid_times) if len(valid_times) > 0 else proc_time
        relative_time = proc_time / max(min_time, 1e-6)
        features.append(relative_time)

        # 3. 机器负载（该机器的总工作量）
        machine_load = torch.sum(processing_matrix[:, machine_id])
        features.append(machine_load / 1000.0)

        # 4. 机器利用率
        machine_ops = torch.sum(processing_matrix[:, machine_id] > 0).float()
        utilization = machine_ops / processing_matrix.size(0)
        features.append(utilization)

        # 5. 机器效率（平均处理时间）
        machine_times = processing_matrix[:, machine_id][processing_matrix[:, machine_id] > 0]
        avg_efficiency = torch.mean(machine_times) if len(machine_times) > 0 else 0
        features.append(avg_efficiency / 100.0)

        return torch.tensor(features, dtype=torch.float32)

    def _get_job_for_operation(self, op_id, job_progress):
        """获取操作所属的作业ID"""
        current_op = 0
        for job_id, progress in enumerate(job_progress):
            job_size = len(job_progress)  # 简化处理
            if current_op <= op_id < current_op + job_size:
                return job_id
            current_op += job_size
        return 0

    def _get_available_operations(self, job_operations, job_progress, scheduled_ops):
        """获取可调度的操作"""
        available_ops = []
        for job_id, operations in enumerate(job_operations):
            if job_progress[job_id] < len(operations):
                next_op = operations[job_progress[job_id]]
                if next_op not in scheduled_ops:
                    available_ops.append(next_op)
        return available_ops

    def _fallback_operation_selection(self, available_operations, processing_matrix):
        """启发式操作选择回退"""
        # SPT启发式：选择最短处理时间的操作
        min_time_op = None
        min_time = float('inf')

        for op_id in available_operations:
            valid_times = processing_matrix[op_id][processing_matrix[op_id] > 0]
            if len(valid_times) > 0:
                op_min_time = torch.min(valid_times).item()
                if op_min_time < min_time:
                    min_time = op_min_time
                    min_time_op = op_id

        return min_time_op if min_time_op is not None else available_operations[0]

    def _fallback_machine_selection(self, operation_id, processing_matrix):
        """启发式机器选择回退"""
        valid_machines = [m for m in range(processing_matrix.size(1))
                         if processing_matrix[operation_id, m] > 0]

        if not valid_machines:
            return None

        # 选择处理时间最短的机器
        best_machine = min(valid_machines,
                          key=lambda m: processing_matrix[operation_id, m].item())
        return best_machine

    def _is_valid_assignment(self, operation_id, machine_id, processing_matrix):
        """验证分配的有效性"""
        if machine_id is None or operation_id >= processing_matrix.size(0):
            return False
        return processing_matrix[operation_id, machine_id] > 0


class OptimizedNeuralModel(nn.Module):
    """优化的神经网络模型"""

    def __init__(self, d_model=256, n_heads=8, n_layers=4, dropout=0.1):
        super().__init__()
        self.d_model = d_model

        # 优化的特征提取器
        self.feature_extractor = AttentionBasedFeatureExtractor(d_model, n_heads)

        # Transformer编码器
        encoder_layer = nn.TransformerEncoderLayer(
            d_model=d_model,
            nhead=n_heads,
            dim_feedforward=d_model * 4,
            dropout=dropout,
            batch_first=True,
            activation='gelu'  # 使用GELU激活函数
        )
        self.transformer = nn.TransformerEncoder(encoder_layer, num_layers=n_layers)

        # 智能约束解码器
        self.decoder = SmartConstraintDecoder(d_model, n_heads)

        # 初始化权重
        self._init_weights()

    def _init_weights(self):
        """改进的权重初始化"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                # 使用Xavier初始化
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.LayerNorm):
                nn.init.ones_(module.weight)
                nn.init.zeros_(module.bias)
            elif isinstance(module, nn.LSTM):
                # LSTM特殊初始化
                for name, param in module.named_parameters():
                    if 'weight' in name:
                        nn.init.orthogonal_(param)
                    elif 'bias' in name:
                        nn.init.zeros_(param)

    def forward(self, job_features, operation_features, machine_features,
                processing_matrix, job_lengths, num_columns=1):
        """前向传播"""
        # 特征提取和增强
        enhanced_operations, machine_embeddings = self.feature_extractor(
            job_features, operation_features, machine_features, processing_matrix
        )

        # Transformer编码
        enhanced_operations = self.transformer(enhanced_operations)
        machine_embeddings = self.transformer(machine_embeddings)

        # 生成多个优化的列
        all_columns = []

        for col_idx in range(num_columns):
            # 为每个列添加不同的噪声以增加多样性
            if col_idx > 0:
                noise_scale = 0.03 * col_idx
                op_embeddings_noisy = enhanced_operations + torch.randn_like(enhanced_operations) * noise_scale
                machine_embeddings_noisy = machine_embeddings + torch.randn_like(machine_embeddings) * noise_scale
            else:
                op_embeddings_noisy = enhanced_operations
                machine_embeddings_noisy = machine_embeddings

            # 智能解码
            schedules = self.decoder(
                op_embeddings_noisy,
                machine_embeddings_noisy,
                processing_matrix,
                job_lengths
            )

            # 评估并添加到结果
            for schedule_output in schedules:
                if schedule_output.feasible:
                    # 计算makespan和质量评分
                    makespan, feasible = self._evaluate_schedule(
                        schedule_output.schedule, processing_matrix[0].cpu().numpy(), job_lengths
                    )

                    if feasible:
                        schedule_output.makespan = makespan
                        schedule_output.quality_score = 1.0 / (1.0 + makespan / 100.0)
                        all_columns.append(schedule_output)

        return all_columns

    def _evaluate_schedule(self, schedule, processing_matrix, job_lengths):
        """评估调度"""
        try:
            from schedule_evaluator import AccurateScheduleEvaluator
            evaluator = AccurateScheduleEvaluator(job_lengths, processing_matrix)
            result = evaluator.evaluate_schedule(schedule)
            return result.makespan, result.feasible
        except:
            # 简单评估
            if not schedule:
                return float('inf'), False

            n_machines = processing_matrix.shape[1]
            machine_times = [0] * n_machines

            for op_id, machine_id in schedule:
                if (machine_id < n_machines and
                    op_id < processing_matrix.shape[0] and
                    processing_matrix[op_id, machine_id] > 0):
                    machine_times[machine_id] += processing_matrix[op_id, machine_id]

            makespan = max(machine_times) if machine_times else float('inf')
            return makespan, makespan < float('inf')

    def generate_optimized_columns(self, job_features, operation_features, machine_features,
                                  processing_matrix, job_lengths, num_columns=5):
        """生成优化的调度列"""
        self.eval()
        with torch.no_grad():
            columns = self.forward(
                job_features, operation_features, machine_features,
                processing_matrix, job_lengths, num_columns
            )

        # 按质量排序
        valid_columns = [col for col in columns if col.feasible]
        valid_columns.sort(key=lambda x: x.makespan)

        return valid_columns


def test_optimized_model():
    """测试优化模型"""
    print("🧪 Testing Optimized Neural Model")
    print("=" * 50)

    # 创建模型
    model = OptimizedNeuralModel(d_model=128, n_heads=4, n_layers=3)

    print(f"✅ Model created with {sum(p.numel() for p in model.parameters()):,} parameters")

    # 创建测试数据
    batch_size = 1
    n_jobs = 3
    n_operations = 10
    n_machines = 4
    job_lengths = [3, 3, 4]

    job_features = torch.randn(batch_size, n_jobs, 10)
    operation_features = torch.randn(batch_size, n_operations, 15)
    machine_features = torch.randn(batch_size, n_machines, 8)

    # 创建有效的处理时间矩阵
    processing_matrix = torch.zeros(batch_size, n_operations, n_machines)
    for b in range(batch_size):
        for op in range(n_operations):
            available_machines = np.random.choice(n_machines, size=np.random.randint(2, 4), replace=False)
            for m in available_machines:
                processing_matrix[b, op, m] = np.random.randint(5, 20)

    print(f"🧪 Testing with {n_jobs} jobs, {n_machines} machines, {n_operations} operations")

    # 测试模型
    try:
        columns = model.generate_optimized_columns(
            job_features, operation_features, machine_features,
            processing_matrix, job_lengths, num_columns=3
        )

        print(f"✅ Generated {len(columns)} optimized columns!")

        total_neural = 0
        total_fallback = 0

        for i, column in enumerate(columns):
            print(f"  Column {i+1}: makespan={column.makespan:.1f}, "
                  f"feasible={column.feasible}, quality={column.quality_score:.3f}")
            print(f"    Neural decisions: {column.neural_decisions}, "
                  f"Fallback decisions: {column.fallback_decisions}")

            total_neural += column.neural_decisions
            total_fallback += column.fallback_decisions

        if columns:
            neural_ratio = total_neural / (total_neural + total_fallback) * 100
            print(f"🧠 Neural decision ratio: {neural_ratio:.1f}%")
            print(f"🎉 All generated schedules are valid and optimized!")
        else:
            print(f"⚠️  No valid schedules generated")

    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_optimized_model()