#!/usr/bin/env python3
"""
质量诊断工具
深入分析神经网络质量差的根本原因
"""

import os
import sys
import torch
import numpy as np
import time
from typing import List, Dict, Tuple
import matplotlib.pyplot as plt

# 添加路径
sys.path.append('..')
sys.path.append('.')

from valid_schedule_neural_model import ValidScheduleNeuralModel
from daniel_consistent_data_generator import DANIELConsistentDataGenerator, DANIELTrainingConfig


def load_model_and_analyze():
    """加载模型并分析其内部状态"""
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # 创建模型
    model = ValidScheduleNeuralModel(d_model=128, n_heads=4, n_layers=3).to(device)
    
    # 加载权重
    model_path = "neural_column_generation/models/robust_best_model.pth"
    if os.path.exists(model_path):
        checkpoint = torch.load(model_path, map_location=device, weights_only=False)
        model.load_state_dict(checkpoint['model_state_dict'])
        model.eval()
        
        # 分析训练历史
        if 'train_history' in checkpoint:
            history = checkpoint['train_history']
            print(f"📊 Training History Analysis:")
            print(f"  Final Loss: {history['loss'][-1]:.4f}")
            print(f"  Final Feasibility Rate: {history['feasibility_rate'][-1]:.3f}")
            print(f"  Loss Trend: {history['loss'][0]:.4f} → {history['loss'][-1]:.4f}")
            
            # 检查是否过拟合
            if len(history['loss']) > 10:
                recent_losses = history['loss'][-5:]
                early_losses = history['loss'][:5]
                print(f"  Early avg loss: {np.mean(early_losses):.4f}")
                print(f"  Recent avg loss: {np.mean(recent_losses):.4f}")
                
                if np.mean(recent_losses) > np.mean(early_losses):
                    print(f"  ⚠️  Possible overfitting detected!")
        
        return model, device, checkpoint
    else:
        print(f"❌ Model not found")
        return None, device, None


def create_simple_test_instance():
    """创建一个简单的测试实例用于详细分析"""
    # 3个作业，3个机器的简单实例
    job_lengths = [2, 2, 2]  # 每个作业2个操作
    n_jobs = 3
    n_machines = 3
    n_operations = 6
    
    # 手工设计的处理时间矩阵，确保有明显的最优解
    processing_times = np.array([
        [10, 20, 30],  # Job 1, Op 1: 机器0最快
        [30, 10, 20],  # Job 1, Op 2: 机器1最快
        [20, 30, 10],  # Job 2, Op 1: 机器2最快
        [10, 20, 30],  # Job 2, Op 2: 机器0最快
        [30, 10, 20],  # Job 3, Op 1: 机器1最快
        [20, 30, 10],  # Job 3, Op 2: 机器2最快
    ])
    
    return {
        'instance_id': 'diagnostic_simple',
        'job_length': job_lengths,
        'processing_times': processing_times.tolist(),
        'n_jobs': n_jobs,
        'n_machines': n_machines,
        'n_operations': n_operations
    }


def analyze_optimal_solution():
    """分析最优解"""
    instance = create_simple_test_instance()
    processing_times = np.array(instance['processing_times'])
    job_lengths = instance['job_length']
    
    print(f"🎯 Optimal Solution Analysis:")
    print(f"Processing Times Matrix:")
    for i, row in enumerate(processing_times):
        print(f"  Op {i}: {row}")
    
    # 理论最优调度：每个操作选择最快的机器
    optimal_schedule = [
        (0, 0),  # Job1-Op1 → Machine0 (10)
        (1, 1),  # Job1-Op2 → Machine1 (10)
        (2, 2),  # Job2-Op1 → Machine2 (10)
        (3, 0),  # Job2-Op2 → Machine0 (10)
        (4, 1),  # Job3-Op1 → Machine1 (10)
        (5, 2),  # Job3-Op2 → Machine2 (10)
    ]
    
    # 计算理论最优makespan
    machine_times = [0, 0, 0]
    for op_id, machine_id in optimal_schedule:
        machine_times[machine_id] += processing_times[op_id, machine_id]
    
    optimal_makespan = max(machine_times)
    print(f"  Theoretical Optimal Schedule: {optimal_schedule}")
    print(f"  Machine loads: {machine_times}")
    print(f"  Theoretical Optimal Makespan: {optimal_makespan}")
    
    return instance, optimal_schedule, optimal_makespan


def extract_features_for_model(instance):
    """提取模型特征"""
    job_length = instance['job_length']
    processing_times = np.array(instance['processing_times'])
    n_jobs = instance['n_jobs']
    n_machines = instance['n_machines']
    n_operations = instance['n_operations']
    
    # 作业特征
    job_features = []
    for job_id, n_ops in enumerate(job_length):
        op_start = sum(job_length[:job_id])
        op_end = op_start + n_ops
        
        total_workload = 0
        for op_idx in range(op_start, op_end):
            if op_idx < len(processing_times):
                min_time = min(t for t in processing_times[op_idx] if t > 0)
                total_workload += min_time
        
        job_feat = [
            job_id / n_jobs, n_ops / n_operations, total_workload / 1000.0,
            n_ops / max(job_length), total_workload / (n_ops * 20),
            0, 0, 0, 0, 0
        ]
        job_features.append(job_feat)
    
    # 操作特征
    operation_features = []
    for op_id in range(n_operations):
        if op_id < len(processing_times):
            proc_times = processing_times[op_id]
            
            job_id = 0
            op_in_job = op_id
            for j, n_ops in enumerate(job_length):
                if op_in_job < n_ops:
                    job_id = j
                    break
                op_in_job -= n_ops
            
            valid_times = [t for t in proc_times if t > 0]
            min_time = min(valid_times) if valid_times else 0
            max_time = max(valid_times) if valid_times else 0
            avg_time = np.mean(valid_times) if valid_times else 0
            n_machines_available = len(valid_times)
            
            op_feat = [
                op_id / n_operations, job_id / n_jobs, op_in_job / max(job_length),
                min_time / 50.0, max_time / 50.0, avg_time / 50.0,
                n_machines_available / n_machines, (max_time - min_time) / 50.0,
                0, 0, 0, 0, 0, 0, 0
            ]
            operation_features.append(op_feat)
    
    # 机器特征
    machine_features = []
    for machine_id in range(n_machines):
        total_load = 0
        n_operations_available = 0
        
        for op_id in range(n_operations):
            if op_id < len(processing_times) and machine_id < len(processing_times[op_id]):
                proc_time = processing_times[op_id][machine_id]
                if proc_time > 0:
                    total_load += proc_time
                    n_operations_available += 1
        
        avg_load = total_load / max(1, n_operations_available)
        
        machine_feat = [
            machine_id / n_machines, total_load / 1000.0,
            n_operations_available / n_operations, avg_load / 50.0,
            0, 0, 0, 0
        ]
        machine_features.append(machine_feat)
    
    return {
        'job_features': torch.FloatTensor(job_features),
        'operation_features': torch.FloatTensor(operation_features),
        'machine_features': torch.FloatTensor(machine_features),
        'processing_matrix': torch.FloatTensor(processing_times),
        'job_lengths': job_length
    }


def analyze_neural_decisions(model, instance, device):
    """分析神经网络的决策过程"""
    print(f"\n🧠 Neural Network Decision Analysis:")
    
    features = extract_features_for_model(instance)
    
    # 生成多个调度并分析
    with torch.no_grad():
        columns = model.generate_valid_columns(
            features['job_features'].unsqueeze(0).to(device),
            features['operation_features'].unsqueeze(0).to(device),
            features['machine_features'].unsqueeze(0).to(device),
            features['processing_matrix'].unsqueeze(0).to(device),
            features['job_lengths'],
            num_columns=5
        )
    
    print(f"Generated {len(columns)} columns:")
    
    processing_times = np.array(instance['processing_times'])
    
    for i, column in enumerate(columns):
        if column.feasible:
            schedule = column.schedule
            makespan = evaluate_schedule_simple(schedule, processing_times)
            
            print(f"\n  Column {i+1}:")
            print(f"    Schedule: {schedule}")
            print(f"    Makespan: {makespan}")
            
            # 分析每个决策
            machine_loads = [0, 0, 0]
            for op_id, machine_id in schedule:
                proc_time = processing_times[op_id, machine_id]
                machine_loads[machine_id] += proc_time
                
                # 找到最优机器
                optimal_machine = np.argmin(processing_times[op_id])
                optimal_time = processing_times[op_id, optimal_machine]
                
                if machine_id != optimal_machine:
                    waste = proc_time - optimal_time
                    print(f"      Op {op_id}: chose M{machine_id}({proc_time}) vs optimal M{optimal_machine}({optimal_time}) → waste {waste}")
            
            print(f"    Machine loads: {machine_loads}")
            print(f"    Load balance: {np.std(machine_loads):.2f}")
    
    return columns


def evaluate_schedule_simple(schedule, processing_times):
    """简单评估调度"""
    if not schedule:
        return float('inf')
    
    machine_times = [0] * processing_times.shape[1]
    
    for op_id, machine_id in schedule:
        if (machine_id < processing_times.shape[1] and 
            op_id < processing_times.shape[0]):
            machine_times[machine_id] += processing_times[op_id, machine_id]
    
    return max(machine_times)


def analyze_baseline_algorithms(instance):
    """分析基线算法"""
    print(f"\n📊 Baseline Algorithm Analysis:")
    
    processing_times = np.array(instance['processing_times'])
    job_lengths = instance['job_length']
    n_operations = instance['n_operations']
    
    # SPT启发式
    operations = list(range(n_operations))
    operations.sort(key=lambda op: min(processing_times[op, m] 
                                     for m in range(processing_times.shape[1]) 
                                     if processing_times[op, m] > 0))
    
    print(f"SPT order: {operations}")
    spt_schedule = create_feasible_schedule(operations, job_lengths, processing_times)
    spt_makespan = evaluate_schedule_simple(spt_schedule, processing_times)
    
    print(f"SPT Schedule: {spt_schedule}")
    print(f"SPT Makespan: {spt_makespan}")
    
    # FIFO启发式
    fifo_operations = list(range(n_operations))
    fifo_schedule = create_feasible_schedule(fifo_operations, job_lengths, processing_times)
    fifo_makespan = evaluate_schedule_simple(fifo_schedule, processing_times)
    
    print(f"FIFO Schedule: {fifo_schedule}")
    print(f"FIFO Makespan: {fifo_makespan}")
    
    return {
        'SPT': {'schedule': spt_schedule, 'makespan': spt_makespan},
        'FIFO': {'schedule': fifo_schedule, 'makespan': fifo_makespan}
    }


def create_feasible_schedule(operation_order, job_length, processing_times):
    """创建可行调度"""
    schedule = []
    job_progress = [0] * len(job_length)
    scheduled_ops = set()
    
    # 构建作业-操作映射
    job_operations = []
    op_id = 0
    for job_id, n_ops in enumerate(job_length):
        job_operations.append(list(range(op_id, op_id + n_ops)))
        op_id += n_ops
    
    for target_op in operation_order:
        if target_op in scheduled_ops:
            continue
        
        # 找到操作所属作业
        target_job = None
        for job_id, ops in enumerate(job_operations):
            if target_op in ops:
                target_job = job_id
                break
        
        if target_job is None:
            continue
        
        # 检查是否可调度
        op_index_in_job = job_operations[target_job].index(target_op)
        if job_progress[target_job] == op_index_in_job:
            valid_machines = [m for m in range(processing_times.shape[1]) 
                             if processing_times[target_op, m] > 0]
            
            if valid_machines:
                best_machine = min(valid_machines, 
                                 key=lambda m: processing_times[target_op, m])
                
                schedule.append((target_op, best_machine))
                scheduled_ops.add(target_op)
                job_progress[target_job] += 1
    
    return schedule


def diagnose_quality_issues():
    """诊断质量问题"""
    print("🔍 NEURAL NETWORK QUALITY DIAGNOSIS")
    print("=" * 60)
    
    # 1. 加载和分析模型
    model, device, checkpoint = load_model_and_analyze()
    
    if model is None:
        print("❌ Cannot proceed without model")
        return
    
    # 2. 分析最优解
    instance, optimal_schedule, optimal_makespan = analyze_optimal_solution()
    
    # 3. 分析神经网络决策
    neural_columns = analyze_neural_decisions(model, instance, device)
    
    # 4. 分析基线算法
    baseline_results = analyze_baseline_algorithms(instance)
    
    # 5. 综合分析
    print(f"\n📈 COMPREHENSIVE ANALYSIS:")
    print(f"=" * 40)
    
    print(f"🎯 Theoretical Optimal: {optimal_makespan}")
    
    if baseline_results:
        spt_makespan = baseline_results['SPT']['makespan']
        fifo_makespan = baseline_results['FIFO']['makespan']
        print(f"📊 SPT Baseline: {spt_makespan}")
        print(f"📊 FIFO Baseline: {fifo_makespan}")
        
        best_baseline = min(spt_makespan, fifo_makespan)
        baseline_gap = (best_baseline - optimal_makespan) / optimal_makespan * 100
        print(f"📊 Best Baseline Gap: {baseline_gap:.1f}%")
    
    if neural_columns:
        neural_makespans = []
        for column in neural_columns:
            if column.feasible:
                makespan = evaluate_schedule_simple(column.schedule, np.array(instance['processing_times']))
                neural_makespans.append(makespan)
        
        if neural_makespans:
            best_neural = min(neural_makespans)
            avg_neural = np.mean(neural_makespans)
            print(f"🧠 Best Neural: {best_neural}")
            print(f"🧠 Avg Neural: {avg_neural:.1f}")
            
            neural_gap = (best_neural - optimal_makespan) / optimal_makespan * 100
            print(f"🧠 Neural Gap: {neural_gap:.1f}%")
            
            if baseline_results:
                vs_baseline = (best_neural - best_baseline) / best_baseline * 100
                print(f"🧠 vs Baseline: {vs_baseline:+.1f}%")
    
    # 6. 问题诊断
    print(f"\n🔍 PROBLEM DIAGNOSIS:")
    print(f"=" * 30)
    
    # 检查是否是训练数据问题
    if checkpoint and 'train_history' in checkpoint:
        history = checkpoint['train_history']
        if history['feasibility_rate'][-1] < 0.9:
            print(f"❌ Issue 1: Low training feasibility ({history['feasibility_rate'][-1]:.1%})")
        
        if len(history['loss']) < 10:
            print(f"❌ Issue 2: Insufficient training ({len(history['loss'])} epochs)")
        
        loss_improvement = history['loss'][0] - history['loss'][-1]
        if loss_improvement < 0.1:
            print(f"❌ Issue 3: Poor loss improvement ({loss_improvement:.4f})")
    
    # 检查是否是模型架构问题
    if neural_columns:
        decision_quality = []
        for column in neural_columns:
            if column.feasible:
                schedule = column.schedule
                total_waste = 0
                for op_id, machine_id in schedule:
                    processing_times = np.array(instance['processing_times'])
                    optimal_machine = np.argmin(processing_times[op_id])
                    optimal_time = processing_times[op_id, optimal_machine]
                    actual_time = processing_times[op_id, machine_id]
                    waste = actual_time - optimal_time
                    total_waste += waste
                decision_quality.append(total_waste)
        
        if decision_quality:
            avg_waste = np.mean(decision_quality)
            print(f"🧠 Average decision waste: {avg_waste:.1f}")
            if avg_waste > optimal_makespan * 0.2:
                print(f"❌ Issue 4: Poor machine selection decisions")
    
    # 检查是否是特征工程问题
    features = extract_features_for_model(instance)
    print(f"\n📊 Feature Analysis:")
    print(f"  Job features shape: {features['job_features'].shape}")
    print(f"  Operation features shape: {features['operation_features'].shape}")
    print(f"  Machine features shape: {features['machine_features'].shape}")
    
    # 检查特征是否包含足够信息
    op_features = features['operation_features']
    min_time_feature = op_features[:, 3]  # 归一化最短时间特征
    print(f"  Min time features: {min_time_feature}")
    
    if torch.all(min_time_feature == 0):
        print(f"❌ Issue 5: Min time features are all zero - feature extraction problem!")
    
    return {
        'optimal_makespan': optimal_makespan,
        'neural_makespans': neural_makespans if 'neural_makespans' in locals() else [],
        'baseline_results': baseline_results,
        'issues_found': []
    }


def main():
    """主函数"""
    print("🔍 NEURAL NETWORK QUALITY DIAGNOSIS")
    print("=" * 70)
    
    results = diagnose_quality_issues()
    
    print(f"\n📋 DIAGNOSIS COMPLETED")
    print(f"🎯 Key issues identified and ready for fixing!")


if __name__ == "__main__":
    main()
