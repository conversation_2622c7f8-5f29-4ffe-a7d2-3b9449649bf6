#!/usr/bin/env python3
"""
最终训练管道
使用DANIEL一致的数据和确保有效调度的神经网络模型
"""

import os
import sys
import json
import pickle
import torch
import torch.nn as nn
import torch.optim as optim
import numpy as np
from torch.utils.data import Dataset, DataLoader
from tqdm import tqdm
from typing import List, Dict, Tuple
import time
from datetime import datetime

# 添加路径
sys.path.append('..')
sys.path.append('.')

from valid_schedule_neural_model import ValidScheduleNeuralModel, ValidScheduleOutput


class DANIELDataset(Dataset):
    """DANIEL一致的数据集"""
    
    def __init__(self, dataset_path="neural_column_generation/daniel_data/daniel_consistent_dataset.pkl"):
        # 加载数据集
        with open(dataset_path, 'rb') as f:
            data = pickle.load(f)
        
        self.instances = data['instances']
        self.solutions = data['solutions']
        
        # 创建实例ID到解的映射
        self.instance_solutions = {}
        for solution in self.solutions:
            instance_id = solution['instance_id']
            if instance_id not in self.instance_solutions:
                self.instance_solutions[instance_id] = []
            if solution['feasible']:  # 只使用可行解
                self.instance_solutions[instance_id].append(solution)
        
        # 创建训练样本
        self.samples = []
        for instance in self.instances:
            instance_id = instance['instance_id']
            if instance_id in self.instance_solutions:
                solutions = self.instance_solutions[instance_id]
                if solutions:
                    # 选择最佳解作为目标
                    best_solution = min(solutions, key=lambda x: x['makespan'])
                    self.samples.append({
                        'instance': instance,
                        'target_solution': best_solution
                    })
        
        print(f"📊 Dataset loaded with {len(self.samples)} training samples")
    
    def __len__(self):
        return len(self.samples)
    
    def __getitem__(self, idx):
        sample = self.samples[idx]
        instance = sample['instance']
        target_solution = sample['target_solution']
        
        # 提取特征
        features = self._extract_features(instance)
        
        return {
            'features': features,
            'target_schedule': target_solution['schedule'],
            'target_makespan': target_solution['makespan'],
            'instance_id': instance['instance_id']
        }
    
    def _extract_features(self, instance):
        """提取特征（与之前保持一致）"""
        job_length = instance['job_length']
        processing_times = np.array(instance['processing_times'])
        n_jobs = len(job_length)
        n_machines = processing_times.shape[1]
        n_operations = sum(job_length)
        
        # 作业特征
        job_features = []
        for job_id, n_ops in enumerate(job_length):
            op_start = sum(job_length[:job_id])
            op_end = op_start + n_ops
            
            total_workload = 0
            for op_idx in range(op_start, op_end):
                if op_idx < len(processing_times):
                    min_time = min(t for t in processing_times[op_idx] if t > 0)
                    total_workload += min_time
            
            job_feat = [
                job_id / n_jobs,
                n_ops / n_operations,
                total_workload / 1000.0,
                n_ops / max(job_length),
                total_workload / (n_ops * 20),
                0, 0, 0, 0, 0
            ]
            job_features.append(job_feat)
        
        # 操作特征
        operation_features = []
        for op_id in range(n_operations):
            if op_id < len(processing_times):
                proc_times = processing_times[op_id]
                
                job_id = 0
                op_in_job = op_id
                for j, n_ops in enumerate(job_length):
                    if op_in_job < n_ops:
                        job_id = j
                        break
                    op_in_job -= n_ops
                
                valid_times = [t for t in proc_times if t > 0]
                min_time = min(valid_times) if valid_times else 0
                max_time = max(valid_times) if valid_times else 0
                avg_time = np.mean(valid_times) if valid_times else 0
                n_machines_available = len(valid_times)
                
                op_feat = [
                    op_id / n_operations,
                    job_id / n_jobs,
                    op_in_job / max(job_length),
                    min_time / 50.0,
                    max_time / 50.0,
                    avg_time / 50.0,
                    n_machines_available / n_machines,
                    (max_time - min_time) / 50.0,
                    0, 0, 0, 0, 0, 0, 0
                ]
                operation_features.append(op_feat)
        
        # 机器特征
        machine_features = []
        for machine_id in range(n_machines):
            total_load = 0
            n_operations_available = 0
            
            for op_id in range(n_operations):
                if op_id < len(processing_times) and machine_id < len(processing_times[op_id]):
                    proc_time = processing_times[op_id][machine_id]
                    if proc_time > 0:
                        total_load += proc_time
                        n_operations_available += 1
            
            avg_load = total_load / max(1, n_operations_available)
            
            machine_feat = [
                machine_id / n_machines,
                total_load / 1000.0,
                n_operations_available / n_operations,
                avg_load / 50.0,
                0, 0, 0, 0
            ]
            machine_features.append(machine_feat)
        
        return {
            'job_features': torch.FloatTensor(job_features),
            'operation_features': torch.FloatTensor(operation_features),
            'machine_features': torch.FloatTensor(machine_features),
            'processing_matrix': torch.FloatTensor(processing_times),
            'job_lengths': job_length
        }


class ValidScheduleLoss(nn.Module):
    """有效调度损失函数"""
    
    def __init__(self, alpha=1.0, beta=0.5):
        super().__init__()
        self.alpha = alpha  # makespan损失权重
        self.beta = beta    # 可行性奖励权重
    
    def forward(self, predicted_columns, target_makespan, target_schedule):
        """计算损失"""
        if not predicted_columns:
            return torch.tensor(1000.0, requires_grad=True)
        
        total_loss = 0.0
        valid_predictions = 0
        
        for column in predicted_columns:
            if column.feasible:
                # Makespan损失
                makespan_error = abs(column.makespan - target_makespan) / (target_makespan + 1e-6)
                makespan_loss = makespan_error ** 2
                
                # 可行性奖励
                feasibility_bonus = 1.0
                
                loss = self.alpha * makespan_loss - self.beta * feasibility_bonus
                total_loss += loss
                valid_predictions += 1
            else:
                # 不可行解的惩罚
                total_loss += 100.0
        
        if valid_predictions == 0:
            return torch.tensor(1000.0, requires_grad=True)
        
        return torch.tensor(total_loss / len(predicted_columns), requires_grad=True)


class FinalTrainer:
    """最终训练器"""
    
    def __init__(self, model, device, learning_rate=0.001):
        self.model = model.to(device)
        self.device = device
        
        self.optimizer = optim.AdamW(
            self.model.parameters(),
            lr=learning_rate,
            weight_decay=0.01
        )
        
        self.scheduler = optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=50
        )
        
        self.criterion = ValidScheduleLoss()
        
        self.train_history = {
            'loss': [],
            'valid_columns': [],
            'avg_makespan': []
        }
    
    def train_epoch(self, train_loader):
        """训练一个epoch"""
        self.model.train()
        epoch_loss = 0.0
        epoch_valid_columns = 0
        epoch_makespan = 0.0
        n_batches = 0
        
        for batch in tqdm(train_loader, desc="Training"):
            self.optimizer.zero_grad()
            
            try:
                # 准备数据
                features = batch['features'][0]  # 假设批次大小为1
                target_makespan = batch['target_makespan'][0]
                target_schedule = batch['target_schedule'][0]
                
                # 前向传播
                predicted_columns = self.model.generate_valid_columns(
                    features['job_features'].unsqueeze(0).to(self.device),
                    features['operation_features'].unsqueeze(0).to(self.device),
                    features['machine_features'].unsqueeze(0).to(self.device),
                    features['processing_matrix'].unsqueeze(0).to(self.device),
                    features['job_lengths'],
                    num_columns=3
                )
                
                # 计算损失
                loss = self.criterion(predicted_columns, target_makespan, target_schedule)
                
                # 反向传播
                loss.backward()
                torch.nn.utils.clip_grad_norm_(self.model.parameters(), max_norm=1.0)
                self.optimizer.step()
                
                epoch_loss += loss.item()
                epoch_valid_columns += len([c for c in predicted_columns if c.feasible])
                
                if predicted_columns:
                    avg_makespan = np.mean([c.makespan for c in predicted_columns if c.feasible])
                    epoch_makespan += avg_makespan if not np.isnan(avg_makespan) else 0
                
                n_batches += 1
                
            except Exception as e:
                print(f"Training error: {e}")
                continue
        
        self.scheduler.step()
        
        return {
            'loss': epoch_loss / max(1, n_batches),
            'valid_columns': epoch_valid_columns / max(1, n_batches),
            'avg_makespan': epoch_makespan / max(1, n_batches)
        }
    
    def train(self, train_loader, num_epochs=30):
        """完整训练过程"""
        print(f"🚀 Starting training for {num_epochs} epochs...")
        
        best_loss = float('inf')
        
        for epoch in range(num_epochs):
            print(f"\n📊 Epoch {epoch + 1}/{num_epochs}")
            
            train_stats = self.train_epoch(train_loader)
            
            self.train_history['loss'].append(train_stats['loss'])
            self.train_history['valid_columns'].append(train_stats['valid_columns'])
            self.train_history['avg_makespan'].append(train_stats['avg_makespan'])
            
            print(f"  📈 Loss: {train_stats['loss']:.4f}")
            print(f"  ✅ Valid columns: {train_stats['valid_columns']:.1f}")
            print(f"  📊 Avg makespan: {train_stats['avg_makespan']:.1f}")
            print(f"  📊 LR: {self.scheduler.get_last_lr()[0]:.6f}")
            
            # 保存最佳模型
            if train_stats['loss'] < best_loss:
                best_loss = train_stats['loss']
                self.save_model("neural_column_generation/models/final_best_model.pth")
                print(f"  🎯 New best model saved!")
        
        print(f"✅ Training completed!")
        return self.train_history
    
    def save_model(self, path):
        """保存模型"""
        model_data = {
            'model_state_dict': self.model.state_dict(),
            'train_history': self.train_history,
            'timestamp': datetime.now().isoformat()
        }
        
        os.makedirs(os.path.dirname(path), exist_ok=True)
        torch.save(model_data, path)


def main():
    """主函数"""
    print("🎯 Final Training Pipeline - DANIEL Consistent + Valid Schedules")
    print("=" * 70)
    
    # 检查数据集
    dataset_path = "neural_column_generation/daniel_data/daniel_consistent_dataset.pkl"
    if not os.path.exists(dataset_path):
        print(f"❌ Dataset not found: {dataset_path}")
        print(f"Please run daniel_consistent_data_generator.py first")
        return
    
    # 创建数据集和数据加载器
    dataset = DANIELDataset(dataset_path)
    
    def custom_collate_fn(batch):
        return {
            'features': [item['features'] for item in batch],
            'target_schedule': [item['target_schedule'] for item in batch],
            'target_makespan': [item['target_makespan'] for item in batch],
            'instance_id': [item['instance_id'] for item in batch]
        }
    
    train_loader = DataLoader(
        dataset,
        batch_size=1,
        shuffle=True,
        collate_fn=custom_collate_fn
    )
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = ValidScheduleNeuralModel(d_model=128, n_heads=4, n_layers=3)
    
    print(f"📱 Device: {device}")
    print(f"📊 Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    print(f"📊 Training samples: {len(dataset)}")
    
    # 创建训练器
    trainer = FinalTrainer(model, device, learning_rate=0.001)
    
    # 开始训练
    history = trainer.train(train_loader, num_epochs=20)
    
    print(f"\n✅ Final training completed!")
    print(f"📊 Final loss: {history['loss'][-1]:.4f}")
    print(f"✅ Valid columns per batch: {history['valid_columns'][-1]:.1f}")
    print(f"📊 Final avg makespan: {history['avg_makespan'][-1]:.1f}")
    print(f"🎯 Model saved to: neural_column_generation/models/final_best_model.pth")


if __name__ == "__main__":
    main()
