#!/usr/bin/env python3
"""
Visualize Results Script
结果可视化脚本 - 生成各种图表和可视化报告
"""

import os
import sys
import argparse
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from evaluation.visualizer import ResultVisualizer


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Visualize Benchmark Results')
    
    parser.add_argument('--results_file', type=str, required=True,
                       help='Path to the benchmark results JSON file')
    parser.add_argument('--output_dir', type=str, default='results/visualization',
                       help='Output directory for visualization')
    parser.add_argument('--format', type=str, default='png',
                       choices=['png', 'pdf', 'svg'],
                       help='Output image format')
    parser.add_argument('--dpi', type=int, default=300,
                       help='Image resolution (DPI)')
    parser.add_argument('--style', type=str, default='seaborn-v0_8',
                       help='Matplotlib style')
    parser.add_argument('--figsize', type=int, nargs=2, default=[12, 8],
                       help='Figure size (width, height)')
    
    return parser.parse_args()


def load_results(results_file: str) -> dict:
    """加载结果文件"""
    print(f"📂 Loading results from {results_file}")
    
    try:
        with open(results_file, 'r', encoding='utf-8') as f:
            results = json.load(f)
        
        print("✅ Results loaded successfully")
        return results
        
    except Exception as e:
        print(f"❌ Failed to load results: {e}")
        sys.exit(1)


def main():
    """主函数"""
    print("🚀 Neural Column Generation Results Visualization")
    print("=" * 60)
    
    # 解析参数
    args = parse_arguments()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    try:
        # 加载结果
        results = load_results(args.results_file)
        
        # 创建可视化器
        visualizer = ResultVisualizer(
            style=args.style,
            figsize=tuple(args.figsize)
        )
        
        # 生成可视化报告
        print("📈 Generating visualization report...")
        
        # 检查结果结构
        if 'comprehensive_report' in results:
            report_data = results['comprehensive_report']
        elif 'dataset_results' in results:
            report_data = results
        else:
            print("⚠️ Unknown results format, using raw data")
            report_data = results
        
        # 生成报告
        visualizer.create_comprehensive_report(report_data, args.output_dir)
        
        print(f"✅ Visualization completed. Results saved to {args.output_dir}")
        
        # 打印生成的文件列表
        print("\n📁 Generated files:")
        for file_path in Path(args.output_dir).glob('*'):
            if file_path.is_file():
                print(f"  - {file_path.name}")
        
    except KeyboardInterrupt:
        print("\n⏹️ Visualization interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Visualization failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
