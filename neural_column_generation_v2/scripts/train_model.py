#!/usr/bin/env python3
"""
Training Script
训练脚本
"""

import argparse
import sys
import os

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from config import Config
from training import NeuralColumnTrainer

def main():
    parser = argparse.ArgumentParser(description='Train Neural Column Generation Model')
    parser.add_argument('--config', type=str, default='../configs/default.json',
                       help='Configuration file path')
    parser.add_argument('--epochs', type=int, help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, help='Batch size')
    parser.add_argument('--lr', type=float, help='Learning rate')
    
    args = parser.parse_args()
    
    # 加载配置
    if os.path.exists(args.config):
        config = Config.load(args.config)
    else:
        from config import get_config
        config = get_config()
    
    # 更新配置
    if args.epochs:
        config.training.num_epochs = args.epochs
    if args.batch_size:
        config.training.batch_size = args.batch_size
    if args.lr:
        config.training.learning_rate = args.lr
    
    # 开始训练
    trainer = NeuralColumnTrainer(config)
    trainer.train()

if __name__ == '__main__':
    main()
