#!/usr/bin/env python3
"""
Evaluate Model Script
模型评估脚本 - 评估神经列生成模型的性能
"""

import os
import sys
import argparse
import json
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

import torch
import numpy as np

from config import get_config
from core.neural_model import NeuralColumnModel
from core.column_generator import ColumnGenerator
from core.schedule_evaluator import ScheduleEvaluator
from core.feature_extractor import FeatureExtractor
from core.data_parser import DataParser
from evaluation.benchmark import BenchmarkRunner
from evaluation.visualizer import ResultVisualizer


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Evaluate Neural Column Generation Model')
    
    parser.add_argument('--model', type=str, required=True,
                       help='Path to the trained model')
    parser.add_argument('--config', type=str, default='configs/default.json',
                       help='Configuration file path')
    parser.add_argument('--data_dir', type=str, default='../data/SD1',
                       help='Test data directory')
    parser.add_argument('--output_dir', type=str, default='results/evaluation',
                       help='Output directory for evaluation results')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (auto, cpu, cuda)')
    parser.add_argument('--visualize', action='store_true',
                       help='Generate visualization report')
    parser.add_argument('--compare', action='store_true',
                       help='Compare with baseline methods')
    parser.add_argument('--time_limit', type=float, default=300.0,
                       help='Time limit for each instance (seconds)')
    
    return parser.parse_args()


def setup_device(device_arg: str) -> torch.device:
    """设置设备"""
    if device_arg == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device_arg)
    
    print(f"🔧 Using device: {device}")
    return device


def load_model(model_path: str, config, device: torch.device) -> NeuralColumnModel:
    """加载模型"""
    print(f"📂 Loading model from {model_path}")
    
    # 创建模型
    model = NeuralColumnModel(config).to(device)
    
    # 加载权重
    try:
        checkpoint = torch.load(model_path, map_location=device)
        
        if 'model_state_dict' in checkpoint:
            model.load_state_dict(checkpoint['model_state_dict'])
        else:
            model.load_state_dict(checkpoint)
        
        print("✅ Model loaded successfully")
        
    except Exception as e:
        print(f"❌ Failed to load model: {e}")
        sys.exit(1)
    
    # 设置为评估模式
    model.eval()
    
    return model


def main():
    """主函数"""
    print("🚀 Neural Column Generation Model Evaluation")
    print("=" * 60)
    
    # 解析参数
    args = parse_arguments()
    
    # 加载配置
    config = get_config(args.config)
    
    # 设置设备
    device = setup_device(args.device)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    try:
        # 加载模型
        model = load_model(args.model, config, device)
        
        # 创建组件
        column_generator = ColumnGenerator(model, config)
        evaluator = ScheduleEvaluator()
        
        # 创建基准测试运行器
        benchmark_runner = BenchmarkRunner(model, config)
        
        # 运行基准测试
        print(f"📊 Running benchmark on {args.data_dir}")
        
        # 设置时间限制
        config.time_limit = args.time_limit
        
        # 运行基准测试
        data_dirs = [args.data_dir]
        results = benchmark_runner.run_comprehensive_benchmark(
            data_dirs, 
            output_dir=args.output_dir
        )
        
        # 打印结果摘要
        print("\n📋 Results Summary:")
        
        for dataset_name, dataset_results in results.get('dataset_results', {}).items():
            neural_stats = dataset_results.get('statistics', {}).get('neural', {})
            
            print(f"\n  Dataset: {dataset_name}")
            print(f"    Success Rate: {neural_stats.get('success_rate', 0):.1%}")
            print(f"    Average Makespan: {neural_stats.get('avg_makespan', 0):.2f}")
            print(f"    Average Solution Time: {neural_stats.get('avg_solution_time', 0):.3f}s")
            
            # 打印与基线的比较
            if args.compare:
                print("\n    Comparison with baselines:")
                for method, comparison in dataset_results.get('comparison_results', {}).items():
                    if comparison.get('comparison_valid', False):
                        makespan_imp = comparison.get('makespan_improvement_pct', 0)
                        time_imp = comparison.get('time_improvement_pct', 0)
                        print(f"      vs {method}: Makespan {makespan_imp:+.1f}%, Time {time_imp:+.1f}%")
        
        # 生成可视化报告
        if args.visualize:
            print("\n📈 Generating visualization report...")
            visualizer = ResultVisualizer()
            visualizer.create_comprehensive_report(
                results.get('comprehensive_report', {}),
                output_dir=args.output_dir
            )
        
        print(f"\n✅ Evaluation completed. Results saved to {args.output_dir}")
        
    except KeyboardInterrupt:
        print("\n⏹️ Evaluation interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Evaluation failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
