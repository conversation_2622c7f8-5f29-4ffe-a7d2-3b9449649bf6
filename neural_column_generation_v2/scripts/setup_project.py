#!/usr/bin/env python3
"""
Project Setup Script
项目设置脚本 - 初始化和配置神经列生成项目
"""

import os
import sys
import shutil
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from config import get_config


def create_directory_structure():
    """创建项目目录结构"""
    print("📁 Creating project directory structure...")
    
    directories = [
        "data/raw",
        "data/processed", 
        "data/models",
        "results/experiments",
        "results/plots",
        "logs/training",
        "logs/evaluation",
        "checkpoints",
        "outputs"
    ]
    
    for directory in directories:
        dir_path = project_root / directory
        dir_path.mkdir(parents=True, exist_ok=True)
        print(f"  ✅ Created: {directory}")


def copy_existing_data():
    """复制现有的数据和模型"""
    print("\n📊 Copying existing data and models...")
    
    # 源路径（旧项目）
    old_project = project_root.parent / "neural_column_generation"
    
    # 复制数据
    data_sources = [
        ("data/improved_train_dataset.pkl", "data/processed/train_dataset.pkl"),
        ("data/improved_val_dataset.pkl", "data/processed/val_dataset.pkl"),
        ("models/high_quality_best.pth", "data/models/pretrained_model.pth"),
        ("fjsp_data", "data/raw/fjsp_data")
    ]
    
    for src, dst in data_sources:
        src_path = old_project / src
        dst_path = project_root / dst
        
        if src_path.exists():
            if src_path.is_file():
                shutil.copy2(src_path, dst_path)
                print(f"  ✅ Copied file: {src} -> {dst}")
            else:
                shutil.copytree(src_path, dst_path, dirs_exist_ok=True)
                print(f"  ✅ Copied directory: {src} -> {dst}")
        else:
            print(f"  ⚠️  Source not found: {src}")


def create_config_files():
    """创建配置文件"""
    print("\n⚙️  Creating configuration files...")
    
    # 创建默认配置
    config = get_config()
    config.save(project_root / "configs" / "default.json")
    print("  ✅ Created: configs/default.json")
    
    # 创建调试配置
    from config import get_debug_config
    debug_config = get_debug_config()
    debug_config.save(project_root / "configs" / "debug.json")
    print("  ✅ Created: configs/debug.json")
    
    # 创建生产配置
    from config import get_production_config
    prod_config = get_production_config()
    prod_config.save(project_root / "configs" / "production.json")
    print("  ✅ Created: configs/production.json")


def create_example_scripts():
    """创建示例脚本"""
    print("\n📝 Creating example scripts...")
    
    # 基本使用示例
    basic_example = '''#!/usr/bin/env python3
"""
Basic Usage Example
基本使用示例
"""

import sys
sys.path.append('..')

from core import NeuralColumnModel, ColumnGenerator, FeatureExtractor, ScheduleEvaluator, FJSPParser
from config import get_config

def main():
    # 加载配置
    config = get_config()
    
    # 解析FJSP实例
    parser = FJSPParser()
    instance = parser.parse_file("../data/raw/example.fjs")
    
    # 提取特征
    feature_extractor = FeatureExtractor(config)
    features = feature_extractor.extract_features(instance)
    
    # 加载模型
    model = NeuralColumnModel(config)
    # model.load_state_dict(torch.load("../data/models/pretrained_model.pth"))
    
    # 生成列
    column_generator = ColumnGenerator(model, config)
    columns = column_generator.generate_columns(features, instance['job_length'])
    
    # 评估结果
    evaluator = ScheduleEvaluator(config)
    evaluated_columns = evaluator.evaluate_columns(columns, instance)
    
    # 显示结果
    for i, column in enumerate(evaluated_columns):
        print(f"Column {i+1}: Makespan={column.makespan:.1f}, Feasible={column.feasible}")

if __name__ == "__main__":
    main()
'''
    
    with open(project_root / "examples" / "basic_usage.py", "w") as f:
        f.write(basic_example)
    print("  ✅ Created: examples/basic_usage.py")


def create_test_files():
    """创建测试文件"""
    print("\n🧪 Creating test files...")
    
    # 创建基本测试
    test_content = '''#!/usr/bin/env python3
"""
Basic Tests
基本测试
"""

import unittest
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core import NeuralColumnModel, FeatureExtractor
from config import get_debug_config

class TestNeuralModel(unittest.TestCase):
    
    def setUp(self):
        self.config = get_debug_config()
        self.model = NeuralColumnModel(self.config)
    
    def test_model_creation(self):
        """测试模型创建"""
        self.assertIsNotNone(self.model)
        self.assertEqual(self.model.d_model, self.config.model.d_model)
    
    def test_feature_extractor(self):
        """测试特征提取器"""
        extractor = FeatureExtractor(self.config)
        self.assertIsNotNone(extractor)

if __name__ == '__main__':
    unittest.main()
'''
    
    with open(project_root / "tests" / "test_basic.py", "w") as f:
        f.write(test_content)
    print("  ✅ Created: tests/test_basic.py")


def create_main_scripts():
    """创建主要脚本"""
    print("\n🚀 Creating main scripts...")
    
    # 训练脚本
    train_script = '''#!/usr/bin/env python3
"""
Training Script
训练脚本
"""

import argparse
import sys
import os

sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from config import Config
from training import NeuralColumnTrainer

def main():
    parser = argparse.ArgumentParser(description='Train Neural Column Generation Model')
    parser.add_argument('--config', type=str, default='../configs/default.json',
                       help='Configuration file path')
    parser.add_argument('--epochs', type=int, help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, help='Batch size')
    parser.add_argument('--lr', type=float, help='Learning rate')
    
    args = parser.parse_args()
    
    # 加载配置
    if os.path.exists(args.config):
        config = Config.load(args.config)
    else:
        from config import get_config
        config = get_config()
    
    # 更新配置
    if args.epochs:
        config.training.num_epochs = args.epochs
    if args.batch_size:
        config.training.batch_size = args.batch_size
    if args.lr:
        config.training.learning_rate = args.lr
    
    # 开始训练
    trainer = NeuralColumnTrainer(config)
    trainer.train()

if __name__ == '__main__':
    main()
'''
    
    with open(project_root / "scripts" / "train_model.py", "w") as f:
        f.write(train_script)
    print("  ✅ Created: scripts/train_model.py")


def setup_git_ignore():
    """设置.gitignore文件"""
    print("\n📋 Creating .gitignore...")
    
    gitignore_content = '''# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# PyTorch
*.pth
*.pt

# Data files
*.pkl
*.json
*.csv
*.txt
*.dat

# Logs
logs/
*.log

# Results
results/
outputs/
checkpoints/

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
.DS_Store
Thumbs.db

# Jupyter
.ipynb_checkpoints/

# Environment
.env
.venv
env/
venv/
'''
    
    with open(project_root / ".gitignore", "w") as f:
        f.write(gitignore_content)
    print("  ✅ Created: .gitignore")


def print_project_summary():
    """打印项目摘要"""
    print("\n" + "="*60)
    print("🎉 PROJECT SETUP COMPLETED!")
    print("="*60)
    
    print("\n📁 Project Structure:")
    print("neural_column_generation_v2/")
    print("├── core/                    # 核心模块")
    print("├── training/                # 训练模块")
    print("├── evaluation/              # 评估模块")
    print("├── integration/             # 集成模块")
    print("├── data/                    # 数据目录")
    print("├── scripts/                 # 脚本目录")
    print("├── examples/                # 示例目录")
    print("├── tests/                   # 测试目录")
    print("├── configs/                 # 配置目录")
    print("└── results/                 # 结果目录")
    
    print("\n🚀 Quick Start:")
    print("1. Install dependencies:")
    print("   pip install -r requirements.txt")
    print("\n2. Run basic example:")
    print("   python examples/basic_usage.py")
    print("\n3. Train model:")
    print("   python scripts/train_model.py")
    print("\n4. Run tests:")
    print("   python -m pytest tests/")
    
    print("\n📚 Next Steps:")
    print("- Complete the training module implementation")
    print("- Add evaluation and visualization modules")
    print("- Implement integration with branch-and-price")
    print("- Add comprehensive tests")
    
    print("\n✅ Ready for development!")


def main():
    """主函数"""
    print("🎯 NEURAL COLUMN GENERATION PROJECT SETUP")
    print("="*60)
    
    # 创建configs目录
    (project_root / "configs").mkdir(exist_ok=True)
    (project_root / "examples").mkdir(exist_ok=True)
    (project_root / "tests").mkdir(exist_ok=True)
    (project_root / "scripts").mkdir(exist_ok=True)
    
    # 执行设置步骤
    create_directory_structure()
    copy_existing_data()
    create_config_files()
    create_example_scripts()
    create_test_files()
    create_main_scripts()
    setup_git_ignore()
    
    # 打印摘要
    print_project_summary()


if __name__ == "__main__":
    main()
