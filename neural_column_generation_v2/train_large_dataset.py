#!/usr/bin/env python3
"""
Large Dataset Training Script
大数据集训练脚本 - 使用更多训练数据进行深度训练
"""

import os
import sys
import argparse
import time
import random
import torch
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from config import get_config
from core.neural_model import NeuralColumnModel
from training.data_generator import TrainingDataGenerator
from improved_train import ImprovedTrainer


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Large Dataset Training')
    
    parser.add_argument('--config', type=str, default='configs/improved_training.json',
                       help='Configuration file path')
    parser.add_argument('--output_dir', type=str, default='outputs/large_dataset_training',
                       help='Output directory')
    parser.add_argument('--epochs', type=int, default=200,
                       help='Number of training epochs')
    parser.add_argument('--num_instances', type=int, default=1000,
                       help='Number of training instances')
    parser.add_argument('--val_split', type=float, default=0.15,
                       help='Validation split ratio')
    parser.add_argument('--batch_size', type=int, default=8,
                       help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=0.0005,
                       help='Learning rate')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (auto, cpu, cuda)')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed')
    
    return parser.parse_args()


def setup_device(device_arg):
    """设置设备"""
    if device_arg == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device_arg)
    
    print(f"🔧 Using device: {device}")
    if device.type == 'cuda':
        print(f"   GPU: {torch.cuda.get_device_name()}")
        print(f"   GPU Memory: {torch.cuda.get_device_properties(0).total_memory / 1e9:.1f} GB")
    
    return device


def set_seed(seed):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    
    # 设置CUDA的确定性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    print(f"🔧 Random seed set to {seed}")


def generate_large_training_data(config, num_instances, val_split):
    """生成大规模训练数据"""
    print(f"📊 Generating {num_instances} diverse training instances...")
    
    data_generator = TrainingDataGenerator(config)
    
    # 生成基础数据
    base_instances = data_generator.generate_synthetic_data(num_instances)
    
    # 数据增强
    enable_augmentation = getattr(config.data, 'enable_augmentation', True)
    augmentation_factor = getattr(config.data, 'augmentation_factor', 2)

    if enable_augmentation:
        print(f"🔄 Applying data augmentation (factor: {augmentation_factor})...")
        augmented_instances = data_generator._augment_data(base_instances)
        all_instances = base_instances + augmented_instances

        # 限制总数量
        if len(all_instances) > num_instances * 2:
            all_instances = random.sample(all_instances, num_instances * 2)
    else:
        all_instances = base_instances
    
    # 分割训练和验证数据
    random.shuffle(all_instances)
    val_size = int(len(all_instances) * val_split)
    train_size = len(all_instances) - val_size
    
    train_data = all_instances[:train_size]
    val_data = all_instances[train_size:] if val_size > 0 else None
    
    print(f"✅ Generated {len(train_data)} training instances and {len(val_data) if val_data else 0} validation instances")
    
    # 打印数据统计
    print(f"📈 Dataset Statistics:")
    job_counts = [inst['n_jobs'] for inst in train_data]
    machine_counts = [inst['n_machines'] for inst in train_data]
    op_counts = [inst['n_operations'] for inst in train_data]
    
    print(f"   Jobs: min={min(job_counts)}, max={max(job_counts)}, avg={np.mean(job_counts):.1f}")
    print(f"   Machines: min={min(machine_counts)}, max={max(machine_counts)}, avg={np.mean(machine_counts):.1f}")
    print(f"   Operations: min={min(op_counts)}, max={max(op_counts)}, avg={np.mean(op_counts):.1f}")
    
    return train_data, val_data


def main():
    """主函数"""
    print("🚀 Large Dataset Neural Column Generation Training")
    print("=" * 70)
    
    # 解析参数
    args = parse_arguments()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 设置设备
    device = setup_device(args.device)
    
    # 加载配置
    config = get_config(args.config)
    
    # 更新配置
    config.training.batch_size = args.batch_size
    config.training.learning_rate = args.learning_rate
    config.training.num_epochs = args.epochs
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    try:
        # 创建模型
        model = NeuralColumnModel(config).to(device)
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"✅ Model created:")
        print(f"   Total parameters: {total_params:,}")
        print(f"   Trainable parameters: {trainable_params:,}")
        print(f"   Model size: {total_params * 4 / 1e6:.2f} MB")
        
        # 生成大规模训练数据
        train_data, val_data = generate_large_training_data(config, args.num_instances, args.val_split)
        
        # 创建训练器
        trainer = ImprovedTrainer(model, config, device)
        trainer.output_dir = args.output_dir
        
        # 调整训练器参数以适应大数据集
        trainer.patience = 50  # 增加早停耐心
        trainer.min_delta = 0.005  # 调整最小改进阈值
        
        print(f"🎯 Training Configuration:")
        print(f"   Epochs: {args.epochs}")
        print(f"   Batch size: {args.batch_size}")
        print(f"   Learning rate: {args.learning_rate}")
        print(f"   Early stopping patience: {trainer.patience}")
        print(f"   Min improvement delta: {trainer.min_delta}")
        
        # 训练模型
        start_time = time.time()
        results = trainer.train(train_data, val_data, num_epochs=args.epochs)
        total_training_time = time.time() - start_time
        
        # 打印结果
        print("\n📋 Training Results:")
        print(f"   Best validation loss: {results['best_loss']:.4f}")
        print(f"   Total training time: {total_training_time:.2f}s ({total_training_time/60:.1f} min)")
        print(f"   Final learning rate: {results['learning_rates'][-1]:.8f}")
        print(f"   Training epochs completed: {len(results['train_losses'])}")
        
        # 保存最终模型
        final_model_path = os.path.join(args.output_dir, 'final_model.pth')
        torch.save(model.state_dict(), final_model_path)
        print(f"💾 Final model saved to {final_model_path}")
        
        # 保存训练配置
        config_path = os.path.join(args.output_dir, 'training_config.json')
        import json
        with open(config_path, 'w') as f:
            json.dump({
                'args': vars(args),
                'config': config.to_dict() if hasattr(config, 'to_dict') else str(config),
                'results': results,
                'total_training_time': total_training_time
            }, f, indent=2)
        
        print(f"📄 Training configuration saved to {config_path}")
        print("\n🎉 Large dataset training completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⏹️ Training interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
