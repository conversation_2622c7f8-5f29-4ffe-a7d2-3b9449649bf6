#!/usr/bin/env python3
"""
Train with Visualization
带可视化的训练脚本 - 训练神经列生成模型并实时可视化训练过程
"""

import os
import sys
import argparse
import json
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import torch
import torch.nn as nn
import numpy as np

from config import get_config
from core.neural_model import NeuralColumnModel
from core.column_generator import ColumnGenerator
from core.schedule_evaluator import ScheduleEvaluator
from core.feature_extractor import FeatureExtractor
from core.data_parser import DataParser
from training.trainer import NeuralColumnTrainer
from training.data_generator import TrainingDataGenerator
from training.loss_functions import CombinedLoss
from training.training_visualizer import TrainingVisualizer


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Train Neural Column Generation Model with Visualization')
    
    parser.add_argument('--config', type=str, default='configs/default.json',
                       help='Configuration file path')
    parser.add_argument('--data_dir', type=str, default='../data/SD1',
                       help='Training data directory')
    parser.add_argument('--output_dir', type=str, default='outputs/training_with_viz',
                       help='Output directory for models and logs')
    parser.add_argument('--epochs', type=int, default=50,
                       help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=4,
                       help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                       help='Learning rate')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (auto, cpu, cuda)')
    parser.add_argument('--enable_visualization', action='store_true', default=True,
                       help='Enable training visualization')
    parser.add_argument('--visualization_update_interval', type=int, default=5,
                       help='Visualization update interval (seconds)')
    parser.add_argument('--synthetic_data', action='store_true',
                       help='Use synthetic data for training')
    parser.add_argument('--num_synthetic_instances', type=int, default=100,
                       help='Number of synthetic instances to generate')
    
    return parser.parse_args()


def setup_device(device_arg: str) -> torch.device:
    """设置设备"""
    if device_arg == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device_arg)
    
    print(f"🔧 Using device: {device}")
    if device.type == 'cuda':
        print(f"   GPU: {torch.cuda.get_device_name()}")
        print(f"   Memory: {torch.cuda.get_device_properties(device).total_memory / 1e9:.1f} GB")
    
    return device


def create_synthetic_data(config, num_instances: int = 100):
    """创建合成训练数据"""
    print(f"🔧 Generating {num_instances} synthetic training instances...")
    
    data_generator = TrainingDataGenerator(config)
    
    # 生成训练数据
    train_data = data_generator.generate_synthetic_data(num_instances)
    
    # 分割训练和验证数据
    split_idx = int(len(train_data) * 0.8)
    val_data = train_data[split_idx:]
    train_data = train_data[:split_idx]
    
    print(f"   Training instances: {len(train_data)}")
    print(f"   Validation instances: {len(val_data)}")
    
    return train_data, val_data


def load_real_data(config, data_dir: str):
    """加载真实数据"""
    print(f"📊 Loading real data from {data_dir}...")
    
    data_generator = TrainingDataGenerator(config)
    
    try:
        # 从目录加载数据
        all_data = data_generator.load_from_directory(data_dir, max_instances=50)
        
        if not all_data:
            print("⚠️ No data found, falling back to synthetic data")
            return create_synthetic_data(config, 50)
        
        # 分割数据
        split_idx = int(len(all_data) * 0.8)
        train_data = all_data[:split_idx]
        val_data = all_data[split_idx:]
        
        print(f"   Training instances: {len(train_data)}")
        print(f"   Validation instances: {len(val_data)}")
        
        return train_data, val_data
        
    except Exception as e:
        print(f"⚠️ Error loading real data: {e}")
        print("   Falling back to synthetic data")
        return create_synthetic_data(config, 50)


def main():
    """主函数"""
    print("🚀 Neural Column Generation Training with Visualization")
    print("=" * 70)
    
    # 解析参数
    args = parse_arguments()
    
    # 加载配置
    config = get_config(args.config)
    
    # 更新配置参数
    config.training.num_epochs = args.epochs
    config.training.batch_size = args.batch_size
    config.training.learning_rate = args.learning_rate
    config.training.enable_visualization = args.enable_visualization
    config.training.visualization_update_interval = args.visualization_update_interval
    
    # 设置设备
    device = setup_device(args.device)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 保存配置
    config_path = os.path.join(args.output_dir, 'training_config.json')
    with open(config_path, 'w') as f:
        json.dump(config.__dict__, f, indent=2, default=str)
    
    try:
        # 加载数据
        if args.synthetic_data:
            train_data, val_data = create_synthetic_data(config, args.num_synthetic_instances)
        else:
            train_data, val_data = load_real_data(config, args.data_dir)
        
        # 创建模型
        print("🧠 Creating model and components...")
        model = NeuralColumnModel(config).to(device)
        
        # 创建组件
        column_generator = ColumnGenerator(model, config)
        evaluator = ScheduleEvaluator()
        feature_extractor = FeatureExtractor(config)
        
        # 创建损失函数
        criterion = CombinedLoss(config)
        
        # 创建优化器
        optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=config.training.learning_rate,
            weight_decay=config.training.weight_decay
        )
        
        # 创建学习率调度器
        scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            optimizer,
            T_max=config.training.num_epochs,
            eta_min=config.training.learning_rate * 0.01
        )
        
        # 打印模型信息
        total_params = sum(p.numel() for p in model.parameters())
        trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
        
        print(f"   Total parameters: {total_params:,}")
        print(f"   Trainable parameters: {trainable_params:,}")
        print(f"   Model size: {total_params * 4 / 1e6:.2f} MB")
        
        # 创建训练器
        trainer = NeuralColumnTrainer(
            model=model,
            column_generator=column_generator,
            evaluator=evaluator,
            feature_extractor=feature_extractor,
            criterion=criterion,
            optimizer=optimizer,
            scheduler=scheduler,
            config=config,
            device=device,
            output_dir=args.output_dir
        )
        
        # 开始训练
        print("🎯 Starting training...")
        start_time = time.time()
        
        training_history = trainer.train(train_data, val_data)
        
        training_time = time.time() - start_time
        print(f"✅ Training completed in {training_time:.2f} seconds")
        
        # 保存训练历史
        history_path = os.path.join(args.output_dir, 'training_history.json')
        with open(history_path, 'w') as f:
            json.dump(training_history, f, indent=2, default=str)
        
        # 打印最终结果
        if training_history:
            best_epoch = min(training_history, key=lambda x: x.get('val_loss', float('inf')))
            print(f"\n📊 Training Summary:")
            print(f"   Best epoch: {best_epoch.get('epoch', 'N/A')}")
            print(f"   Best validation loss: {best_epoch.get('val_loss', 'N/A'):.4f}")
            print(f"   Final learning rate: {scheduler.get_last_lr()[0]:.6f}")
            print(f"   Total training time: {training_time:.2f}s")
        
        print(f"\n💾 Results saved to: {args.output_dir}")
        
        if args.enable_visualization:
            viz_dir = os.path.join(args.output_dir, 'visualization')
            print(f"📊 Visualization results saved to: {viz_dir}")
            print("   Check the 'plots' subdirectory for training progress charts")
        
    except KeyboardInterrupt:
        print("\n⏹️ Training interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
