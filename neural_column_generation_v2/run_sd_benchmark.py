#!/usr/bin/env python3
"""
SD Dataset Benchmark
SD数据集基准测试 - 在SD数据集上运行完整的对比实验
"""

import os
import sys
import argparse
import json
import time
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

import torch
import numpy as np
import pandas as pd

from config import get_config
from core.neural_model import NeuralColumnModel
from core.column_generator import ColumnGenerator
from core.schedule_evaluator import ScheduleEvaluator
from core.feature_extractor import FeatureExtractor
from core.data_parser import DataParser
from evaluation.benchmark import BenchmarkRunner
from evaluation.visualizer import ResultVisualizer
from evaluation.metrics import PerformanceMetrics
from integration.branch_and_price import BranchAndPriceSolver
from integration.solver_interface import NeuralColumnSolver, HeuristicSolver


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Run SD Dataset Benchmark')
    
    parser.add_argument('--model_v1', type=str, default='../neural_column_generation/models/best_model.pth',
                       help='Path to v1 model')
    parser.add_argument('--model_v2', type=str, default='outputs/training_with_viz/best_model.pth',
                       help='Path to v2 model')
    parser.add_argument('--config_v1', type=str, default='../neural_column_generation/configs/default.json',
                       help='v1 configuration file')
    parser.add_argument('--config_v2', type=str, default='configs/default.json',
                       help='v2 configuration file')
    parser.add_argument('--data_dirs', type=str, nargs='+', 
                       default=['../data/SD1', '../data/SD2'],
                       help='SD dataset directories')
    parser.add_argument('--output_dir', type=str, default='results/sd_benchmark',
                       help='Output directory for benchmark results')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (auto, cpu, cuda)')
    parser.add_argument('--time_limit', type=float, default=300.0,
                       help='Time limit for each instance (seconds)')
    parser.add_argument('--num_instances_per_dataset', type=int, default=20,
                       help='Number of instances to test per dataset')
    parser.add_argument('--generate_report', action='store_true', default=True,
                       help='Generate comprehensive report')
    
    return parser.parse_args()


def setup_device(device_arg: str) -> torch.device:
    """设置设备"""
    if device_arg == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device_arg)
    
    print(f"🔧 Using device: {device}")
    return device


def load_model(model_path: str, config, device: torch.device, model_version: str) -> NeuralColumnModel:
    """加载模型"""
    print(f"📂 Loading {model_version} model from {model_path}")
    
    try:
        # 创建模型
        model = NeuralColumnModel(config).to(device)
        
        # 加载权重
        if os.path.exists(model_path):
            checkpoint = torch.load(model_path, map_location=device)
            
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
            else:
                model.load_state_dict(checkpoint)
            
            print(f"✅ {model_version} model loaded successfully")
        else:
            print(f"⚠️ {model_version} model file not found, using untrained model")
        
        model.eval()
        return model
        
    except Exception as e:
        print(f"❌ Failed to load {model_version} model: {e}")
        # 返回未训练的模型
        model = NeuralColumnModel(config).to(device)
        model.eval()
        return model


def run_single_solver_benchmark(solver, instances, solver_name: str):
    """运行单个求解器的基准测试"""
    print(f"🔄 Testing {solver_name}...")
    
    results = []
    
    for i, instance in enumerate(instances):
        print(f"  Instance {i+1}/{len(instances)}: {instance.get('filename', 'unknown')}")
        
        try:
            start_time = time.time()
            result = solver.solve(instance)
            
            results.append({
                'instance': instance.get('filename', f'instance_{i}'),
                'makespan': result.optimal_makespan,
                'solution_time': result.solution_time,
                'status': result.status,
                'feasible': result.status in ['optimal', 'feasible'],
                'success': result.status == 'optimal'
            })
            
            print(f"    Result: makespan={result.optimal_makespan:.2f}, "
                  f"time={result.solution_time:.3f}s, status={result.status}")
            
        except Exception as e:
            print(f"    ❌ Error: {e}")
            results.append({
                'instance': instance.get('filename', f'instance_{i}'),
                'makespan': float('inf'),
                'solution_time': 0.0,
                'status': 'error',
                'feasible': False,
                'success': False
            })
    
    return results


def create_comparison_report(all_results: dict, output_dir: str):
    """创建对比报告"""
    print("📊 Creating comparison report...")
    
    # 创建DataFrame
    comparison_data = []
    
    for dataset_name, dataset_results in all_results.items():
        for solver_name, solver_results in dataset_results.items():
            for result in solver_results:
                comparison_data.append({
                    'Dataset': dataset_name,
                    'Solver': solver_name,
                    'Instance': result['instance'],
                    'Makespan': result['makespan'],
                    'Solution_Time': result['solution_time'],
                    'Status': result['status'],
                    'Success': result['success']
                })
    
    df = pd.DataFrame(comparison_data)
    
    # 保存详细结果
    detailed_path = os.path.join(output_dir, 'detailed_results.csv')
    df.to_csv(detailed_path, index=False)
    
    # 计算汇总统计
    summary_stats = []
    
    for dataset in df['Dataset'].unique():
        for solver in df['Solver'].unique():
            subset = df[(df['Dataset'] == dataset) & (df['Solver'] == solver)]
            
            if len(subset) > 0:
                successful = subset[subset['Success'] == True]
                
                stats = {
                    'Dataset': dataset,
                    'Solver': solver,
                    'Total_Instances': len(subset),
                    'Successful_Instances': len(successful),
                    'Success_Rate': len(successful) / len(subset) if len(subset) > 0 else 0,
                    'Avg_Makespan': successful['Makespan'].mean() if len(successful) > 0 else float('inf'),
                    'Std_Makespan': successful['Makespan'].std() if len(successful) > 0 else 0,
                    'Min_Makespan': successful['Makespan'].min() if len(successful) > 0 else float('inf'),
                    'Max_Makespan': successful['Makespan'].max() if len(successful) > 0 else float('inf'),
                    'Avg_Solution_Time': successful['Solution_Time'].mean() if len(successful) > 0 else 0,
                    'Total_Solution_Time': subset['Solution_Time'].sum()
                }
                
                summary_stats.append(stats)
    
    summary_df = pd.DataFrame(summary_stats)
    
    # 保存汇总统计
    summary_path = os.path.join(output_dir, 'summary_statistics.csv')
    summary_df.to_csv(summary_path, index=False)
    
    # 生成文本报告
    report_lines = []
    report_lines.append("=" * 80)
    report_lines.append("SD Dataset Benchmark Report")
    report_lines.append("=" * 80)
    report_lines.append("")
    
    for dataset in summary_df['Dataset'].unique():
        report_lines.append(f"Dataset: {dataset}")
        report_lines.append("-" * 40)
        
        dataset_stats = summary_df[summary_df['Dataset'] == dataset]
        
        for _, row in dataset_stats.iterrows():
            solver = row['Solver']
            report_lines.append(f"  {solver}:")
            report_lines.append(f"    Success Rate: {row['Success_Rate']:.1%}")
            report_lines.append(f"    Average Makespan: {row['Avg_Makespan']:.2f}")
            report_lines.append(f"    Average Solution Time: {row['Avg_Solution_Time']:.3f}s")
            report_lines.append("")
        
        report_lines.append("")
    
    # 计算v1 vs v2对比
    report_lines.append("V1 vs V2 Comparison:")
    report_lines.append("-" * 40)
    
    for dataset in summary_df['Dataset'].unique():
        v1_stats = summary_df[(summary_df['Dataset'] == dataset) & (summary_df['Solver'] == 'Neural_v1')]
        v2_stats = summary_df[(summary_df['Dataset'] == dataset) & (summary_df['Solver'] == 'Neural_v2')]
        
        if len(v1_stats) > 0 and len(v2_stats) > 0:
            v1_row = v1_stats.iloc[0]
            v2_row = v2_stats.iloc[0]
            
            makespan_improvement = (v1_row['Avg_Makespan'] - v2_row['Avg_Makespan']) / v1_row['Avg_Makespan'] * 100
            time_improvement = (v1_row['Avg_Solution_Time'] - v2_row['Avg_Solution_Time']) / v1_row['Avg_Solution_Time'] * 100
            success_improvement = v2_row['Success_Rate'] - v1_row['Success_Rate']
            
            report_lines.append(f"  {dataset}:")
            report_lines.append(f"    Makespan improvement: {makespan_improvement:+.1f}%")
            report_lines.append(f"    Time improvement: {time_improvement:+.1f}%")
            report_lines.append(f"    Success rate improvement: {success_improvement:+.1%}")
            report_lines.append("")
    
    # 保存文本报告
    report_path = os.path.join(output_dir, 'benchmark_report.txt')
    with open(report_path, 'w') as f:
        f.write('\n'.join(report_lines))
    
    print(f"✅ Comparison report saved to {output_dir}")
    print(f"   - Detailed results: {detailed_path}")
    print(f"   - Summary statistics: {summary_path}")
    print(f"   - Text report: {report_path}")
    
    return summary_df


def main():
    """主函数"""
    print("🚀 SD Dataset Benchmark - Neural Column Generation v1 vs v2")
    print("=" * 80)
    
    # 解析参数
    args = parse_arguments()
    
    # 设置设备
    device = setup_device(args.device)
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    try:
        # 加载配置
        config_v1 = get_config(args.config_v1)
        config_v2 = get_config(args.config_v2)
        
        # 加载模型
        model_v1 = load_model(args.model_v1, config_v1, device, "v1")
        model_v2 = load_model(args.model_v2, config_v2, device, "v2")
        
        # 创建求解器
        print("🔧 Creating solvers...")
        
        # v1求解器
        column_generator_v1 = ColumnGenerator(model_v1, config_v1)
        evaluator_v1 = ScheduleEvaluator()
        solver_v1 = NeuralColumnSolver(model_v1, column_generator_v1, evaluator_v1, config_v1)
        
        # v2求解器
        column_generator_v2 = ColumnGenerator(model_v2, config_v2)
        evaluator_v2 = ScheduleEvaluator()
        solver_v2 = NeuralColumnSolver(model_v2, column_generator_v2, evaluator_v2, config_v2)
        
        # 基线求解器
        solver_spt = HeuristicSolver('SPT', config_v2)
        solver_lpt = HeuristicSolver('LPT', config_v2)
        
        solvers = {
            'Neural_v1': solver_v1,
            'Neural_v2': solver_v2,
            'SPT': solver_spt,
            'LPT': solver_lpt
        }
        
        # 加载数据并运行基准测试
        all_results = {}
        data_parser = DataParser()
        
        for data_dir in args.data_dirs:
            dataset_name = os.path.basename(data_dir)
            print(f"\n📁 Processing dataset: {dataset_name}")
            
            # 加载实例
            instances = []
            if os.path.exists(data_dir):
                for file_path in Path(data_dir).glob('*.fjs'):
                    try:
                        instance_data = data_parser.parse_file(str(file_path))
                        if instance_data:
                            instance_data['filename'] = file_path.name
                            instances.append(instance_data)
                            
                            if len(instances) >= args.num_instances_per_dataset:
                                break
                    except Exception as e:
                        print(f"⚠️ Error loading {file_path}: {e}")
            
            if not instances:
                print(f"⚠️ No instances found in {data_dir}")
                continue
            
            print(f"   Loaded {len(instances)} instances")
            
            # 运行所有求解器
            dataset_results = {}
            for solver_name, solver in solvers.items():
                dataset_results[solver_name] = run_single_solver_benchmark(
                    solver, instances, solver_name
                )
            
            all_results[dataset_name] = dataset_results
        
        # 保存原始结果
        results_path = os.path.join(args.output_dir, 'raw_results.json')
        with open(results_path, 'w') as f:
            json.dump(all_results, f, indent=2, default=str)
        
        # 生成对比报告
        if args.generate_report:
            summary_df = create_comparison_report(all_results, args.output_dir)
            
            # 打印简要结果
            print("\n📊 Benchmark Results Summary:")
            print(summary_df.to_string(index=False))
        
        print(f"\n✅ SD Dataset benchmark completed!")
        print(f"💾 Results saved to: {args.output_dir}")
        
    except KeyboardInterrupt:
        print("\n⏹️ Benchmark interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Benchmark failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
