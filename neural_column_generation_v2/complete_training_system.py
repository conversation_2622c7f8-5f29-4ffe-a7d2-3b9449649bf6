#!/usr/bin/env python3
"""
Complete Training System
完整训练系统 - 包含充足训练样本的完整训练流程
"""

import sys
import os
import torch
import torch.nn as nn
import numpy as np
import time
import pickle
import random
from typing import List, Dict, Tuple
from tqdm import tqdm

# 添加路径
sys.path.insert(0, '.')

# 导入模块
from core import NeuralColumnModel, ColumnGenerator, FeatureExtractor, ScheduleEvaluator, FJSPParser
from config import get_config


class ComprehensiveDataGenerator:
    """综合数据生成器"""
    
    def __init__(self, config):
        self.config = config
        self.parser = FJSPParser()
    
    def generate_training_dataset(self, num_instances=1000):
        """生成训练数据集"""
        print(f"🔄 Generating {num_instances} training instances...")
        
        instances = []
        
        # 生成不同规模的实例
        size_configs = [
            (3, 3, 200),   # 小规模：3作业×3机器
            (4, 3, 250),   # 中小规模：4作业×3机器
            (5, 4, 300),   # 中等规模：5作业×4机器
            (6, 4, 150),   # 中大规模：6作业×4机器
            (8, 5, 100),   # 大规模：8作业×5机器
        ]
        
        for n_jobs, n_machines, count in size_configs:
            for i in range(count):
                instance = self._generate_single_instance(n_jobs, n_machines, 
                                                        f"{n_jobs}x{n_machines}_{i:03d}")
                if self.parser.validate_instance(instance):
                    instances.append(instance)
        
        print(f"✅ Generated {len(instances)} valid training instances")
        return instances
    
    def _generate_single_instance(self, n_jobs, n_machines, instance_id):
        """生成单个实例"""
        # 生成作业长度
        job_lengths = []
        total_operations = 0
        
        for job_id in range(n_jobs):
            # 每个作业2-5个操作
            n_ops = random.randint(2, min(5, 15 // n_jobs + 2))
            job_lengths.append(n_ops)
            total_operations += n_ops
        
        # 生成处理时间矩阵
        processing_times = []
        
        for op_id in range(total_operations):
            proc_times = [0] * n_machines
            
            # 每个操作至少在1个机器上可用，最多在所有机器上可用
            n_available = random.randint(1, n_machines)
            available_machines = random.sample(range(n_machines), n_available)
            
            # 生成处理时间，考虑机器效率差异
            base_time = random.randint(10, 50)
            
            for machine_id in available_machines:
                # 机器效率因子：0.8-1.2
                efficiency_factor = random.uniform(0.8, 1.2)
                proc_time = int(base_time * efficiency_factor)
                proc_times[machine_id] = max(1, proc_time)
            
            processing_times.append(proc_times)
        
        return {
            'instance_id': instance_id,
            'n_jobs': n_jobs,
            'n_machines': n_machines,
            'n_operations': total_operations,
            'job_length': job_lengths,
            'processing_times': processing_times,
            'format': 'synthetic'
        }
    
    def load_real_data(self):
        """加载真实数据"""
        print("📊 Loading real FJSP instances...")
        
        real_instances = []
        
        # 尝试从不同位置加载SD数据
        sd_paths = [
            '../data/SD1',
            '../data/SD2', 
            'data/raw/SD1',
            'data/raw/SD2'
        ]
        
        for sd_path in sd_paths:
            if os.path.exists(sd_path):
                for subdir in os.listdir(sd_path):
                    subdir_path = os.path.join(sd_path, subdir)
                    if os.path.isdir(subdir_path):
                        try:
                            instances = self.parser.parse_directory(subdir_path, "*.fjs")
                            real_instances.extend(instances)
                            print(f"  ✅ Loaded {len(instances)} from {subdir_path}")
                        except Exception as e:
                            print(f"  ⚠️  Failed to load from {subdir_path}: {e}")
        
        print(f"✅ Total real instances loaded: {len(real_instances)}")
        return real_instances


class ImprovedTrainer:
    """改进的训练器"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.system.device)
        
        # 初始化组件
        self.model = NeuralColumnModel(config).to(self.device)
        self.column_generator = ColumnGenerator(self.model, config)
        self.feature_extractor = FeatureExtractor(config)
        self.evaluator = ScheduleEvaluator(config)
        
        # 优化器和调度器
        self.optimizer = torch.optim.AdamW(
            self.model.parameters(),
            lr=config.training.learning_rate,
            weight_decay=config.training.weight_decay
        )
        
        self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
            self.optimizer, T_max=config.training.num_epochs
        )
        
        # 训练统计
        self.train_losses = []
        self.val_losses = []
        self.best_val_loss = float('inf')
        
        print(f"🎯 Improved trainer initialized")
        print(f"   Device: {self.device}")
        print(f"   Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
    
    def compute_comprehensive_loss(self, columns, instances):
        """计算综合损失"""
        makespan_losses = []
        feasibility_losses = []
        decision_quality_losses = []
        
        for batch_columns, instance in zip(columns, instances):
            # 评估列
            evaluated_columns = self.evaluator.evaluate_columns(batch_columns, instance)
            
            # Makespan损失
            makespans = []
            feasible_count = 0
            decision_qualities = []
            
            for column in evaluated_columns:
                if column.feasible and column.makespan < float('inf'):
                    makespans.append(column.makespan)
                    feasible_count += 1
                    decision_qualities.append(column.decision_quality)
            
            # 计算各项损失
            if makespans:
                makespan_loss = min(makespans) / 100.0
                feasibility_loss = 1.0 - (feasible_count / len(evaluated_columns))
                decision_quality_loss = 1.0 - np.mean(decision_qualities)
            else:
                makespan_loss = 10.0
                feasibility_loss = 1.0
                decision_quality_loss = 1.0
            
            makespan_losses.append(makespan_loss)
            feasibility_losses.append(feasibility_loss)
            decision_quality_losses.append(decision_quality_loss)
        
        # 组合损失
        total_makespan_loss = np.mean(makespan_losses)
        total_feasibility_loss = np.mean(feasibility_losses)
        total_decision_quality_loss = np.mean(decision_quality_losses)
        
        # 加权组合
        combined_loss = (
            2.0 * total_makespan_loss +
            1.0 * total_feasibility_loss +
            1.0 * total_decision_quality_loss
        )
        
        return torch.tensor(combined_loss, device=self.device, requires_grad=True)
    
    def train_epoch(self, train_instances, batch_size=4):
        """训练一个epoch"""
        self.model.train()
        
        # 随机打乱训练数据
        random.shuffle(train_instances)
        
        epoch_losses = []
        
        # 分批训练
        for i in range(0, len(train_instances), batch_size):
            batch_instances = train_instances[i:i+batch_size]
            
            # 生成列
            all_columns = []
            
            for instance in batch_instances:
                features = self.feature_extractor.extract_features(instance)
                
                # 移动特征到设备
                device_features = {}
                for key, value in features.items():
                    if isinstance(value, torch.Tensor):
                        device_features[key] = value.to(self.device)
                    else:
                        device_features[key] = value
                
                # 生成列
                columns = self.column_generator.generate_columns(
                    device_features,
                    instance['job_length'],
                    num_columns=3
                )
                
                all_columns.append(columns)
            
            # 计算损失
            loss = self.compute_comprehensive_loss(all_columns, batch_instances)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss.backward()
            
            # 梯度裁剪
            torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
            
            self.optimizer.step()
            
            epoch_losses.append(loss.item())
        
        return np.mean(epoch_losses)
    
    def validate_epoch(self, val_instances):
        """验证一个epoch"""
        self.model.eval()
        
        val_losses = []
        
        with torch.no_grad():
            for instance in val_instances:
                features = self.feature_extractor.extract_features(instance)
                
                # 移动特征到设备
                device_features = {}
                for key, value in features.items():
                    if isinstance(value, torch.Tensor):
                        device_features[key] = value.to(self.device)
                    else:
                        device_features[key] = value
                
                # 生成列
                columns = self.column_generator.generate_columns(
                    device_features,
                    instance['job_length'],
                    num_columns=3
                )
                
                # 计算损失
                loss = self.compute_comprehensive_loss([columns], [instance])
                val_losses.append(loss.item())
        
        return np.mean(val_losses)
    
    def train(self, train_instances, val_instances, num_epochs=20):
        """完整训练流程"""
        print(f"🚀 Starting comprehensive training...")
        print(f"   Train instances: {len(train_instances)}")
        print(f"   Val instances: {len(val_instances)}")
        print(f"   Epochs: {num_epochs}")
        print()
        
        for epoch in range(num_epochs):
            epoch_start = time.time()
            
            # 训练
            train_loss = self.train_epoch(train_instances, batch_size=4)
            
            # 验证
            val_loss = self.validate_epoch(val_instances)
            
            # 更新学习率
            self.scheduler.step()
            
            # 记录损失
            self.train_losses.append(train_loss)
            self.val_losses.append(val_loss)
            
            # 保存最佳模型
            if val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                self.save_model('best_model.pth')
            
            epoch_time = time.time() - epoch_start
            
            print(f"Epoch {epoch+1:2d}/{num_epochs} ({epoch_time:5.1f}s) | "
                  f"Train: {train_loss:.4f} | Val: {val_loss:.4f} | "
                  f"LR: {self.scheduler.get_last_lr()[0]:.6f}")
        
        print(f"\n✅ Training completed!")
        print(f"   Best validation loss: {self.best_val_loss:.4f}")
    
    def save_model(self, filename):
        """保存模型"""
        os.makedirs('data/models', exist_ok=True)
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'best_val_loss': self.best_val_loss,
            'config': self.config.to_dict()
        }, f'data/models/{filename}')


def main():
    """主函数"""
    print("🎯 NEURAL COLUMN GENERATION V2.0 - COMPLETE TRAINING SYSTEM")
    print("=" * 80)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    random.seed(42)
    
    # 加载配置
    config = get_config()
    config.training.num_epochs = 15
    config.training.learning_rate = 0.001
    config.training.batch_size = 4
    
    print(f"📋 Training Configuration:")
    print(f"   Epochs: {config.training.num_epochs}")
    print(f"   Learning rate: {config.training.learning_rate}")
    print(f"   Batch size: {config.training.batch_size}")
    print(f"   Device: {config.system.device}")
    print()
    
    # 生成数据集
    data_generator = ComprehensiveDataGenerator(config)
    
    # 生成合成数据
    synthetic_instances = data_generator.generate_training_dataset(num_instances=800)
    
    # 加载真实数据
    real_instances = data_generator.load_real_data()
    
    # 合并数据集
    all_instances = synthetic_instances + real_instances
    random.shuffle(all_instances)
    
    print(f"📊 Total dataset: {len(all_instances)} instances")
    print(f"   Synthetic: {len(synthetic_instances)}")
    print(f"   Real: {len(real_instances)}")
    print()
    
    # 划分数据集
    train_size = int(0.8 * len(all_instances))
    val_size = int(0.1 * len(all_instances))
    
    train_instances = all_instances[:train_size]
    val_instances = all_instances[train_size:train_size + val_size]
    test_instances = all_instances[train_size + val_size:]
    
    print(f"📊 Dataset split:")
    print(f"   Train: {len(train_instances)} instances")
    print(f"   Val: {len(val_instances)} instances")
    print(f"   Test: {len(test_instances)} instances")
    print()
    
    # 创建训练器
    trainer = ImprovedTrainer(config)
    
    # 开始训练
    start_time = time.time()
    trainer.train(train_instances, val_instances, num_epochs=config.training.num_epochs)
    total_time = time.time() - start_time
    
    print(f"\n🎉 Training completed in {total_time/60:.1f} minutes!")
    print(f"📈 Average time per epoch: {total_time/config.training.num_epochs:.1f}s")
    
    # 保存最终模型
    trainer.save_model('final_model.pth')
    
    print(f"\n💾 Models saved to data/models/")
    print(f"✅ Complete training system finished successfully!")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
