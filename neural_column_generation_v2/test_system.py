#!/usr/bin/env python3
"""
Test System
测试系统 - 测试神经列生成v2系统的基本功能
"""

import os
import sys
import torch
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from config import get_config
from core.neural_model import NeuralColumnModel
from core.column_generator import ColumnGenerator
from core.schedule_evaluator import ScheduleEvaluator
from core.feature_extractor import FeatureExtractor
from core.data_parser import DataParser


def test_model():
    """测试模型"""
    print("🧪 Testing model...")
    
    # 加载配置
    config = get_config()
    
    # 创建模型
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = NeuralColumnModel(config).to(device)
    
    # 打印模型信息
    total_params = sum(p.numel() for p in model.parameters())
    trainable_params = sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    print(f"   Total parameters: {total_params:,}")
    print(f"   Trainable parameters: {trainable_params:,}")
    print(f"   Model size: {total_params * 4 / 1e6:.2f} MB")
    
    print("✅ Model test passed")
    return model, config, device


def test_data_generation():
    """测试数据生成"""
    print("🧪 Testing data generation...")
    
    # 加载配置
    config = get_config()
    
    # 创建合成数据
    processing_matrix = torch.tensor([
        [5.0, 3.0, 4.0],  # Job 0, Op 0
        [2.0, 6.0, 3.0],  # Job 0, Op 1
        [4.0, 2.0, 5.0],  # Job 0, Op 2
        [3.0, 4.0, 2.0],  # Job 1, Op 0
        [6.0, 3.0, 4.0],  # Job 1, Op 1
        [2.0, 5.0, 3.0],  # Job 2, Op 0
        [4.0, 2.0, 6.0],  # Job 2, Op 1
        [3.0, 4.0, 2.0],  # Job 2, Op 2
    ], dtype=torch.float32)
    
    test_instance = {
        'job_length': [3, 2, 3],
        'processing_matrix': processing_matrix,
        'processing_times': processing_matrix.numpy(),
        'n_jobs': 3,
        'n_machines': 3,
        'n_operations': 8
    }
    
    # 测试特征提取
    feature_extractor = FeatureExtractor(config)
    features = feature_extractor.extract_features(test_instance)
    
    print(f"   Features extracted:")
    for key, value in features.items():
        if isinstance(value, torch.Tensor):
            print(f"     {key}: {value.shape}")
        else:
            print(f"     {key}: {type(value)}")
    
    print("✅ Data generation test passed")
    return test_instance, features


def test_column_generation(model, config, test_instance, features):
    """测试列生成"""
    print("🧪 Testing column generation...")
    
    # 创建列生成器
    column_generator = ColumnGenerator(model, config)
    
    # 生成列
    columns = column_generator.generate_columns(
        features,
        test_instance['job_length'],
        num_columns=2
    )
    
    print(f"   Generated {len(columns)} columns")
    
    # 评估列
    evaluator = ScheduleEvaluator()
    
    for i, column in enumerate(columns):
        evaluated_column = evaluator.evaluate_column(column, test_instance)
        print(f"   Column {i}: makespan={evaluated_column.makespan:.2f}, feasible={evaluated_column.feasible}")
    
    print("✅ Column generation test passed")
    return columns


def main():
    """主函数"""
    print("🚀 Neural Column Generation v2.0 - System Test")
    print("=" * 70)
    
    try:
        # 测试模型
        model, config, device = test_model()
        
        # 测试数据生成
        test_instance, features = test_data_generation()
        
        # 测试列生成
        columns = test_column_generation(model, config, test_instance, features)
        
        print("\n🎉 All tests passed!")
        print("✅ Neural Column Generation v2.0 system is ready for use!")
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
