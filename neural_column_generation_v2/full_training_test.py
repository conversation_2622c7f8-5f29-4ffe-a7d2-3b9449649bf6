#!/usr/bin/env python3
"""
Full Training Test
完整训练测试 - 测试完整的训练流程
"""

import sys
import os
import torch
import numpy as np
import time
from typing import List, Dict

# 添加路径
sys.path.insert(0, '.')

# 导入模块
from core import NeuralColumnModel, ColumnGenerator, FeatureExtractor, ScheduleEvaluator, FJSPParser
from config import get_debug_config


def create_synthetic_dataset(num_instances=10):
    """创建合成数据集"""
    print(f"🔄 Creating {num_instances} synthetic instances...")
    
    instances = []
    
    for i in range(num_instances):
        # 随机生成实例参数
        n_jobs = np.random.randint(2, 5)
        n_machines = np.random.randint(2, 4)
        
        # 生成作业长度
        job_lengths = []
        total_operations = 0
        
        for job_id in range(n_jobs):
            n_ops = np.random.randint(2, 4)
            job_lengths.append(n_ops)
            total_operations += n_ops
        
        # 生成处理时间矩阵
        processing_times = []
        
        for op_id in range(total_operations):
            proc_times = [0] * n_machines
            
            # 随机选择可用机器
            n_available = np.random.randint(1, n_machines + 1)
            available_machines = np.random.choice(n_machines, n_available, replace=False)
            
            for machine_id in available_machines:
                proc_time = np.random.randint(5, 25)
                proc_times[machine_id] = proc_time
            
            processing_times.append(proc_times)
        
        # 创建实例
        instance = {
            'instance_id': f'synthetic_{i:03d}',
            'n_jobs': n_jobs,
            'n_machines': n_machines,
            'n_operations': total_operations,
            'job_length': job_lengths,
            'processing_times': processing_times,
            'format': 'synthetic'
        }
        
        instances.append(instance)
    
    print(f"✅ Created {len(instances)} synthetic instances")
    return instances


class SimplifiedTrainer:
    """简化的训练器"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.system.device)
        
        # 初始化组件
        self.model = NeuralColumnModel(config).to(self.device)
        self.column_generator = ColumnGenerator(self.model, config)
        self.feature_extractor = FeatureExtractor(config)
        self.evaluator = ScheduleEvaluator(config)
        
        # 优化器
        self.optimizer = torch.optim.Adam(
            self.model.parameters(),
            lr=config.training.learning_rate
        )
        
        print(f"🎯 Simplified trainer initialized")
        print(f"   Device: {self.device}")
        print(f"   Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
    
    def compute_loss(self, columns, instances):
        """计算简化的损失"""
        total_loss = 0.0
        valid_columns = 0

        for batch_columns, instance in zip(columns, instances):
            # 评估列
            evaluated_columns = self.evaluator.evaluate_columns(batch_columns, instance)

            # 计算makespan损失
            makespans = []
            for column in evaluated_columns:
                if column.feasible and column.makespan < float('inf'):
                    makespans.append(column.makespan)

            if makespans:
                # 使用最佳makespan作为损失
                best_makespan = min(makespans)
                normalized_loss = best_makespan / 100.0
                total_loss += normalized_loss
                valid_columns += 1
            else:
                # 如果没有可行解，给予高损失
                total_loss += 10.0
                valid_columns += 1

        # 创建可微分的损失
        loss_tensor = torch.tensor(total_loss / max(valid_columns, 1),
                                 device=self.device, requires_grad=True)
        return loss_tensor
    
    def train_step(self, instances):
        """训练一步"""
        self.model.train()
        
        # 提取特征并生成列
        all_columns = []
        
        for instance in instances:
            features = self.feature_extractor.extract_features(instance)
            
            # 移动特征到设备
            device_features = {}
            for key, value in features.items():
                if isinstance(value, torch.Tensor):
                    device_features[key] = value.to(self.device)
                else:
                    device_features[key] = value
            
            # 生成列
            columns = self.column_generator.generate_columns(
                device_features,
                instance['job_length'],
                num_columns=2
            )
            
            all_columns.append(columns)
        
        # 计算损失
        loss_tensor = self.compute_loss(all_columns, instances)

        # 反向传播
        self.optimizer.zero_grad()
        loss_tensor.backward()
        self.optimizer.step()

        return loss_tensor.item(), all_columns
    
    def evaluate_step(self, instances):
        """评估一步"""
        self.model.eval()
        
        with torch.no_grad():
            # 提取特征并生成列
            all_columns = []
            
            for instance in instances:
                features = self.feature_extractor.extract_features(instance)
                
                # 移动特征到设备
                device_features = {}
                for key, value in features.items():
                    if isinstance(value, torch.Tensor):
                        device_features[key] = value.to(self.device)
                    else:
                        device_features[key] = value
                
                # 生成列
                columns = self.column_generator.generate_columns(
                    device_features,
                    instance['job_length'],
                    num_columns=2
                )
                
                all_columns.append(columns)
            
            # 计算损失
            loss_tensor = self.compute_loss(all_columns, instances)

            return loss_tensor.item(), all_columns
    
    def train(self, train_instances, val_instances, num_epochs=5):
        """训练模型"""
        print(f"🚀 Starting training for {num_epochs} epochs...")
        
        for epoch in range(num_epochs):
            epoch_start = time.time()
            
            # 训练
            train_loss, train_columns = self.train_step(train_instances)
            
            # 验证
            val_loss, val_columns = self.evaluate_step(val_instances)
            
            epoch_time = time.time() - epoch_start
            
            # 计算统计信息
            train_stats = self.compute_statistics(train_columns, train_instances)
            val_stats = self.compute_statistics(val_columns, val_instances)
            
            print(f"Epoch {epoch+1}/{num_epochs} ({epoch_time:.2f}s)")
            print(f"  Train Loss: {train_loss:.4f}, Feasible: {train_stats['feasible_rate']:.1%}")
            print(f"  Val Loss: {val_loss:.4f}, Feasible: {val_stats['feasible_rate']:.1%}")
            print(f"  Train Avg Makespan: {train_stats['avg_makespan']:.1f}")
            print(f"  Val Avg Makespan: {val_stats['avg_makespan']:.1f}")
            print("-" * 50)
        
        print("✅ Training completed!")
    
    def compute_statistics(self, all_columns, instances):
        """计算统计信息"""
        total_columns = 0
        feasible_columns = 0
        makespans = []
        
        for batch_columns, instance in zip(all_columns, instances):
            evaluated_columns = self.evaluator.evaluate_columns(batch_columns, instance)
            
            for column in evaluated_columns:
                total_columns += 1
                if column.feasible and column.makespan < float('inf'):
                    feasible_columns += 1
                    makespans.append(column.makespan)
        
        return {
            'total_columns': total_columns,
            'feasible_columns': feasible_columns,
            'feasible_rate': feasible_columns / max(total_columns, 1),
            'avg_makespan': np.mean(makespans) if makespans else 0.0
        }


def main():
    """主函数"""
    print("🎯 NEURAL COLUMN GENERATION V2.0 - FULL TRAINING TEST")
    print("=" * 70)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 加载配置
    config = get_debug_config()
    config.training.num_epochs = 3  # 快速测试
    config.training.batch_size = 2
    
    print(f"📋 Configuration:")
    print(f"   Epochs: {config.training.num_epochs}")
    print(f"   Batch size: {config.training.batch_size}")
    print(f"   Learning rate: {config.training.learning_rate}")
    print(f"   Device: {config.system.device}")
    print()
    
    # 创建数据集
    all_instances = create_synthetic_dataset(num_instances=8)
    
    # 划分数据集
    train_instances = all_instances[:6]
    val_instances = all_instances[6:]
    
    print(f"📊 Dataset split:")
    print(f"   Train: {len(train_instances)} instances")
    print(f"   Val: {len(val_instances)} instances")
    print()
    
    # 创建训练器
    trainer = SimplifiedTrainer(config)
    
    # 开始训练
    start_time = time.time()
    trainer.train(train_instances, val_instances, num_epochs=config.training.num_epochs)
    total_time = time.time() - start_time
    
    print(f"\n🎉 Training completed in {total_time:.2f} seconds!")
    print(f"📈 Average time per epoch: {total_time/config.training.num_epochs:.2f}s")
    
    # 最终评估
    print(f"\n🔬 Final evaluation...")
    final_loss, final_columns = trainer.evaluate_step(val_instances)
    final_stats = trainer.compute_statistics(final_columns, val_instances)
    
    print(f"📊 Final Results:")
    print(f"   Validation Loss: {final_loss:.4f}")
    print(f"   Feasible Rate: {final_stats['feasible_rate']:.1%}")
    print(f"   Average Makespan: {final_stats['avg_makespan']:.1f}")
    print(f"   Total Columns: {final_stats['total_columns']}")
    
    print(f"\n✅ Full training test completed successfully!")
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
