#!/usr/bin/env python3
"""
Training Visualizer
训练过程可视化器 - 实时监控和可视化训练过程
"""

import os
import time
import json
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Optional, Any
from pathlib import Path
import threading
import queue


class TrainingVisualizer:
    """训练过程可视化器"""
    
    def __init__(self, output_dir: str, update_interval: int = 10):
        self.output_dir = output_dir
        self.update_interval = update_interval
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'plots'), exist_ok=True)
        
        # 训练历史数据
        self.training_history = []
        self.current_epoch = 0
        
        # 实时更新队列
        self.update_queue = queue.Queue()
        self.is_running = False
        self.update_thread = None
        
        # 设置绘图样式
        plt.style.use('seaborn-v0_8')
        sns.set_palette("husl")
        
        # 初始化图表
        self.fig = None
        self.axes = None
        self._setup_plots()
    
    def _setup_plots(self):
        """设置绘图布局"""
        self.fig, self.axes = plt.subplots(2, 3, figsize=(18, 12))
        self.fig.suptitle('Neural Column Generation Training Progress', fontsize=16, fontweight='bold')
        
        # 设置子图标题
        titles = [
            'Training & Validation Loss',
            'Makespan Loss Components',
            'Learning Rate Schedule',
            'Column Generation Quality',
            'Training Metrics',
            'Performance Summary'
        ]
        
        for i, ax in enumerate(self.axes.flat):
            ax.set_title(titles[i])
            ax.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.ion()  # 开启交互模式
    
    def start_monitoring(self):
        """开始监控训练过程"""
        if not self.is_running:
            self.is_running = True
            self.update_thread = threading.Thread(target=self._update_loop)
            self.update_thread.daemon = True
            self.update_thread.start()
            print("📊 Training visualization started")
    
    def stop_monitoring(self):
        """停止监控"""
        self.is_running = False
        if self.update_thread:
            self.update_thread.join()
        print("📊 Training visualization stopped")
    
    def log_epoch(self, epoch_info: Dict):
        """记录epoch信息"""
        self.training_history.append(epoch_info)
        self.current_epoch = epoch_info.get('epoch', self.current_epoch)
        
        # 添加到更新队列
        self.update_queue.put(epoch_info)
        
        # 保存历史数据
        self._save_history()
    
    def _update_loop(self):
        """更新循环"""
        while self.is_running:
            try:
                # 检查是否有新数据
                if not self.update_queue.empty():
                    # 处理所有待更新的数据
                    while not self.update_queue.empty():
                        epoch_info = self.update_queue.get_nowait()
                    
                    # 更新图表
                    self._update_plots()
                
                time.sleep(self.update_interval)
                
            except Exception as e:
                print(f"⚠️ Visualization update error: {e}")
    
    def _update_plots(self):
        """更新所有图表"""
        if not self.training_history:
            return
        
        try:
            # 清除所有子图
            for ax in self.axes.flat:
                ax.clear()
            
            # 更新各个图表
            self._plot_loss_curves()
            self._plot_loss_components()
            self._plot_learning_rate()
            self._plot_column_quality()
            self._plot_training_metrics()
            self._plot_performance_summary()
            
            # 重新设置标题和网格
            titles = [
                'Training & Validation Loss',
                'Makespan Loss Components',
                'Learning Rate Schedule',
                'Column Generation Quality',
                'Training Metrics',
                'Performance Summary'
            ]
            
            for i, ax in enumerate(self.axes.flat):
                ax.set_title(titles[i])
                ax.grid(True, alpha=0.3)
            
            plt.tight_layout()
            plt.draw()
            plt.pause(0.01)
            
            # 保存当前图表
            self._save_current_plot()
            
        except Exception as e:
            print(f"⚠️ Plot update error: {e}")
    
    def _plot_loss_curves(self):
        """绘制损失曲线"""
        ax = self.axes[0, 0]
        
        epochs = [h['epoch'] for h in self.training_history]
        train_losses = [h.get('train_loss', 0) for h in self.training_history]
        val_losses = [h.get('val_loss', 0) for h in self.training_history if h.get('val_loss') is not None]
        val_epochs = [h['epoch'] for h in self.training_history if h.get('val_loss') is not None]
        
        ax.plot(epochs, train_losses, 'b-', label='Training Loss', linewidth=2)
        if val_losses:
            ax.plot(val_epochs, val_losses, 'r-', label='Validation Loss', linewidth=2)
        
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Loss')
        ax.legend()
        
        # 添加最新值标注
        if train_losses:
            ax.annotate(f'{train_losses[-1]:.4f}', 
                       xy=(epochs[-1], train_losses[-1]), 
                       xytext=(10, 10), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='blue', alpha=0.3))
    
    def _plot_loss_components(self):
        """绘制损失组件"""
        ax = self.axes[0, 1]
        
        epochs = [h['epoch'] for h in self.training_history]
        
        # 提取损失组件
        components = ['makespan_loss', 'feasibility_loss', 'diversity_loss', 'decision_quality_loss']
        colors = ['red', 'green', 'blue', 'orange']
        
        for component, color in zip(components, colors):
            values = []
            for h in self.training_history:
                metrics = h.get('train_metrics', {})
                values.append(metrics.get(component, 0))
            
            if any(v > 0 for v in values):
                ax.plot(epochs, values, color=color, label=component.replace('_', ' ').title(), linewidth=2)
        
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Loss Component')
        ax.legend()
    
    def _plot_learning_rate(self):
        """绘制学习率变化"""
        ax = self.axes[0, 2]
        
        epochs = [h['epoch'] for h in self.training_history]
        lrs = [h.get('lr', 0) for h in self.training_history]
        
        ax.plot(epochs, lrs, 'g-', linewidth=2)
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Learning Rate')
        ax.set_yscale('log')
        
        # 添加当前学习率标注
        if lrs:
            ax.annotate(f'{lrs[-1]:.6f}', 
                       xy=(epochs[-1], lrs[-1]), 
                       xytext=(10, 10), textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.3', facecolor='green', alpha=0.3))
    
    def _plot_column_quality(self):
        """绘制列生成质量"""
        ax = self.axes[1, 0]
        
        epochs = [h['epoch'] for h in self.training_history]
        
        # 提取质量指标
        feasible_ratios = []
        avg_makespans = []
        
        for h in self.training_history:
            val_metrics = h.get('val_metrics', {})
            
            # 计算可行列比例
            feasible = val_metrics.get('feasible_columns', 0)
            total = val_metrics.get('total_columns', 1)
            feasible_ratios.append(feasible / total if total > 0 else 0)
            
            # 平均makespan（简化处理）
            avg_makespans.append(val_metrics.get('avg_makespan', 0))
        
        # 双y轴绘图
        ax2 = ax.twinx()
        
        line1 = ax.plot(epochs, feasible_ratios, 'b-', label='Feasible Ratio', linewidth=2)
        line2 = ax2.plot(epochs, avg_makespans, 'r-', label='Avg Makespan', linewidth=2)
        
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Feasible Ratio', color='blue')
        ax2.set_ylabel('Average Makespan', color='red')
        
        # 合并图例
        lines = line1 + line2
        labels = [l.get_label() for l in lines]
        ax.legend(lines, labels, loc='upper right')
    
    def _plot_training_metrics(self):
        """绘制训练指标"""
        ax = self.axes[1, 1]
        
        epochs = [h['epoch'] for h in self.training_history]
        
        # 提取训练时间
        train_times = []
        for h in self.training_history:
            metrics = h.get('train_metrics', {})
            train_times.append(metrics.get('time', 0))
        
        ax.bar(epochs[-10:], train_times[-10:], alpha=0.7, color='skyblue')
        ax.set_xlabel('Epoch')
        ax.set_ylabel('Training Time (s)')
        ax.set_title('Recent Training Times')
    
    def _plot_performance_summary(self):
        """绘制性能摘要"""
        ax = self.axes[1, 2]
        
        if not self.training_history:
            return
        
        # 最新的性能指标
        latest = self.training_history[-1]
        
        metrics = {
            'Train Loss': latest.get('train_loss', 0),
            'Val Loss': latest.get('val_loss', 0),
            'Learning Rate': latest.get('lr', 0),
            'Epoch': latest.get('epoch', 0)
        }
        
        # 创建文本摘要
        ax.axis('off')
        
        summary_text = f"""
        Current Epoch: {metrics['Epoch']}
        
        Training Loss: {metrics['Train Loss']:.4f}
        Validation Loss: {metrics['Val Loss']:.4f}
        Learning Rate: {metrics['Learning Rate']:.6f}
        
        Total Epochs: {len(self.training_history)}
        Best Val Loss: {min(h.get('val_loss', float('inf')) for h in self.training_history if h.get('val_loss') is not None):.4f}
        """
        
        ax.text(0.1, 0.9, summary_text, transform=ax.transAxes, 
               fontsize=12, verticalalignment='top',
               bbox=dict(boxstyle='round,pad=0.5', facecolor='lightgray', alpha=0.8))
    
    def _save_current_plot(self):
        """保存当前图表"""
        try:
            plot_path = os.path.join(self.output_dir, 'plots', f'training_progress_epoch_{self.current_epoch}.png')
            self.fig.savefig(plot_path, dpi=150, bbox_inches='tight')
            
            # 同时保存最新版本
            latest_path = os.path.join(self.output_dir, 'plots', 'latest_training_progress.png')
            self.fig.savefig(latest_path, dpi=150, bbox_inches='tight')
            
        except Exception as e:
            print(f"⚠️ Error saving plot: {e}")
    
    def _save_history(self):
        """保存训练历史"""
        try:
            history_path = os.path.join(self.output_dir, 'training_history.json')
            with open(history_path, 'w') as f:
                json.dump(self.training_history, f, indent=2, default=str)
        except Exception as e:
            print(f"⚠️ Error saving history: {e}")
    
    def generate_final_report(self):
        """生成最终训练报告"""
        if not self.training_history:
            return
        
        print("📊 Generating final training report...")
        
        # 创建最终报告图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Neural Column Generation Training Report', fontsize=16, fontweight='bold')
        
        epochs = [h['epoch'] for h in self.training_history]
        train_losses = [h.get('train_loss', 0) for h in self.training_history]
        val_losses = [h.get('val_loss', 0) for h in self.training_history if h.get('val_loss') is not None]
        val_epochs = [h['epoch'] for h in self.training_history if h.get('val_loss') is not None]
        
        # 1. 损失曲线
        axes[0, 0].plot(epochs, train_losses, 'b-', label='Training Loss', linewidth=2)
        if val_losses:
            axes[0, 0].plot(val_epochs, val_losses, 'r-', label='Validation Loss', linewidth=2)
        axes[0, 0].set_title('Training Progress')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 学习率变化
        lrs = [h.get('lr', 0) for h in self.training_history]
        axes[0, 1].plot(epochs, lrs, 'g-', linewidth=2)
        axes[0, 1].set_title('Learning Rate Schedule')
        axes[0, 1].set_xlabel('Epoch')
        axes[0, 1].set_ylabel('Learning Rate')
        axes[0, 1].set_yscale('log')
        axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 训练时间分布
        train_times = [h.get('train_metrics', {}).get('time', 0) for h in self.training_history]
        axes[1, 0].hist(train_times, bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        axes[1, 0].set_title('Training Time Distribution')
        axes[1, 0].set_xlabel('Time per Epoch (s)')
        axes[1, 0].set_ylabel('Frequency')
        axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 性能摘要
        axes[1, 1].axis('off')
        
        # 计算统计信息
        best_train_loss = min(train_losses) if train_losses else 0
        best_val_loss = min(val_losses) if val_losses else 0
        avg_train_time = np.mean(train_times) if train_times else 0
        total_time = sum(train_times) if train_times else 0
        
        summary_text = f"""
        Training Summary:
        
        Total Epochs: {len(self.training_history)}
        Best Training Loss: {best_train_loss:.4f}
        Best Validation Loss: {best_val_loss:.4f}
        
        Average Time per Epoch: {avg_train_time:.2f}s
        Total Training Time: {total_time:.2f}s
        
        Final Learning Rate: {lrs[-1]:.6f}
        """
        
        axes[1, 1].text(0.1, 0.9, summary_text, transform=axes[1, 1].transAxes,
                        fontsize=12, verticalalignment='top',
                        bbox=dict(boxstyle='round,pad=0.5', facecolor='lightblue', alpha=0.8))
        
        plt.tight_layout()
        
        # 保存最终报告
        report_path = os.path.join(self.output_dir, 'final_training_report.png')
        fig.savefig(report_path, dpi=300, bbox_inches='tight')
        plt.close(fig)
        
        print(f"✅ Final training report saved to {report_path}")
    
    def __del__(self):
        """析构函数"""
        self.stop_monitoring()
        if self.fig:
            plt.close(self.fig)
