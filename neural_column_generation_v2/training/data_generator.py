#!/usr/bin/env python3
"""
Training Data Generator
训练数据生成器 - 生成训练数据
"""

import os
import random
import numpy as np
import torch
from typing import List, Dict, Tuple, Optional, Any
from pathlib import Path

try:
    from ..core.data_parser import DataParser
except ImportError:
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from core.data_parser import DataParser


class TrainingDataGenerator:
    """训练数据生成器"""
    
    def __init__(self, config):
        self.config = config
        self.data_parser = DataParser()
        
        # 数据增强参数
        self.enable_augmentation = getattr(config.data, 'enable_augmentation', True)
        self.augmentation_factor = getattr(config.data, 'augmentation_factor', 2)
    
    def load_from_directory(self, data_dir: str, max_instances: int = None) -> List[Dict]:
        """
        从目录加载数据
        
        Args:
            data_dir: 数据目录
            max_instances: 最大实例数量
            
        Returns:
            实例列表
        """
        print(f"📂 Loading data from {data_dir}")
        
        instances = []
        
        if not os.path.exists(data_dir):
            print(f"⚠️ Directory not found: {data_dir}")
            return instances
        
        # 支持多种文件格式
        file_extensions = ['.fjs', '.txt', '.dat']
        
        for file_ext in file_extensions:
            pattern = f"*{file_ext}"
            files = list(Path(data_dir).glob(pattern))
            
            for file_path in files:
                try:
                    instance_data = self.data_parser.parse_file(str(file_path))
                    if instance_data:
                        instance_data['filename'] = file_path.name
                        instance_data['filepath'] = str(file_path)
                        instances.append(instance_data)
                        
                        if max_instances and len(instances) >= max_instances:
                            break
                except Exception as e:
                    print(f"⚠️ Error loading {file_path}: {e}")
            
            if max_instances and len(instances) >= max_instances:
                break
        
        # 数据增强
        if self.enable_augmentation and instances:
            augmented_instances = self._augment_data(instances)
            instances.extend(augmented_instances)
            
            # 再次截断
            if max_instances and len(instances) > max_instances:
                instances = instances[:max_instances]
        
        print(f"✅ Loaded {len(instances)} instances")
        return instances
    
    def generate_synthetic_data(self, num_instances: int = 100) -> List[Dict]:
        """
        生成合成数据 - 增强版本，包含更多样化的实例

        Args:
            num_instances: 实例数量

        Returns:
            实例列表
        """
        print(f"🔧 Generating {num_instances} diverse synthetic instances")

        instances = []

        # 定义指定的问题规模 - 按用户要求
        job_options = [10, 12, 20, 25, 30]
        machine_options = [5, 10]

        # 创建所有可能的job-machine组合
        problem_configs = []
        for n_jobs in job_options:
            for n_machines in machine_options:
                problem_configs.append({
                    'n_jobs': n_jobs,
                    'n_machines': n_machines,
                    'op_length_range': (2, 6),  # 每个作业2-6个操作
                    'proc_time_range': (1, 20)  # 处理时间1-20
                })

        # 均匀分配实例到各个配置
        instances_per_config = num_instances // len(problem_configs)
        remaining_instances = num_instances % len(problem_configs)

        for config_idx, config in enumerate(problem_configs):
            # 计算当前配置的实例数量
            current_instances = instances_per_config
            if config_idx < remaining_instances:
                current_instances += 1

            for i in range(current_instances):
                # 使用指定的job和machine数量
                n_jobs = config['n_jobs']
                n_machines = config['n_machines']

                # 生成作业长度 - 增加变化性
                job_length = []
                for _ in range(n_jobs):
                    op_count = random.randint(*config['op_length_range'])
                    job_length.append(op_count)

                n_operations = sum(job_length)

                # 生成处理时间矩阵 - 增加多样性
                processing_matrix = np.zeros((n_operations, n_machines), dtype=np.float32)

                # 添加不同的处理时间模式
                pattern = random.choice(['uniform', 'bimodal', 'exponential', 'normal'])

                for op_id in range(n_operations):
                    for m_id in range(n_machines):
                        if pattern == 'uniform':
                            proc_time = random.randint(*config['proc_time_range'])
                        elif pattern == 'bimodal':
                            # 双峰分布
                            if random.random() < 0.5:
                                proc_time = random.randint(config['proc_time_range'][0], config['proc_time_range'][0] + 5)
                            else:
                                proc_time = random.randint(config['proc_time_range'][1] - 5, config['proc_time_range'][1])
                        elif pattern == 'exponential':
                            # 指数分布近似
                            proc_time = min(config['proc_time_range'][1],
                                          max(config['proc_time_range'][0],
                                              int(np.random.exponential(4) + config['proc_time_range'][0])))
                        else:  # normal
                            # 正态分布
                            mean = (config['proc_time_range'][0] + config['proc_time_range'][1]) / 2
                            std = (config['proc_time_range'][1] - config['proc_time_range'][0]) / 4
                            proc_time = max(config['proc_time_range'][0],
                                          min(config['proc_time_range'][1],
                                              int(np.random.normal(mean, std))))

                        processing_matrix[op_id, m_id] = proc_time

                # 创建实例
                instance = {
                    'job_length': job_length,
                    'processing_matrix': torch.tensor(processing_matrix, dtype=torch.float32),
                    'processing_times': processing_matrix,
                    'n_jobs': n_jobs,
                    'n_machines': n_machines,
                    'n_operations': n_operations,
                    'filename': f'synthetic_{n_jobs}j_{n_machines}m_{i}.fjs',
                    'synthetic': True,
                    'config_idx': config_idx,
                    'pattern': pattern
                }

                instances.append(instance)

        # 确保达到目标数量（通常已经达到，但以防万一）
        while len(instances) < num_instances:
            config = random.choice(problem_configs)
            n_jobs = config['n_jobs']
            n_machines = config['n_machines']

            job_length = [random.randint(*config['op_length_range']) for _ in range(n_jobs)]
            n_operations = sum(job_length)

            processing_matrix = np.zeros((n_operations, n_machines), dtype=np.float32)
            for op_id in range(n_operations):
                for m_id in range(n_machines):
                    processing_matrix[op_id, m_id] = random.randint(*config['proc_time_range'])

            instance = {
                'job_length': job_length,
                'processing_matrix': torch.tensor(processing_matrix, dtype=torch.float32),
                'processing_times': processing_matrix,
                'n_jobs': n_jobs,
                'n_machines': n_machines,
                'n_operations': n_operations,
                'filename': f'synthetic_extra_{n_jobs}j_{n_machines}m_{len(instances)}.fjs',
                'synthetic': True
            }

            instances.append(instance)

        # 打乱顺序
        random.shuffle(instances)

        print(f"✅ Generated {len(instances)} diverse synthetic instances")

        # 打印配置分布
        config_counts = {}
        for instance in instances:
            key = f"{instance['n_jobs']}J-{instance['n_machines']}M"
            config_counts[key] = config_counts.get(key, 0) + 1

        print("   Configuration distribution:")
        for config, count in sorted(config_counts.items()):
            print(f"     {config}: {count} instances")

        return instances
    
    def _augment_data(self, instances: List[Dict]) -> List[Dict]:
        """
        数据增强
        
        Args:
            instances: 原始实例列表
            
        Returns:
            增强后的实例列表
        """
        augmented_instances = []
        
        for instance in instances:
            for _ in range(self.augmentation_factor):
                # 复制实例
                augmented = self._augment_instance(instance)
                if augmented:
                    augmented_instances.append(augmented)
        
        return augmented_instances
    
    def _augment_instance(self, instance: Dict) -> Optional[Dict]:
        """
        增强单个实例
        
        Args:
            instance: 原始实例
            
        Returns:
            增强后的实例
        """
        try:
            # 复制实例
            augmented = instance.copy()
            
            # 获取处理时间矩阵
            if isinstance(instance['processing_matrix'], torch.Tensor):
                processing_matrix = instance['processing_matrix'].clone().numpy()
            else:
                processing_matrix = np.array(instance['processing_matrix'])
            
            # 随机选择增强方法
            augmentation_method = random.choice([
                'scale', 'permute_machines', 'add_noise'
            ])
            
            if augmentation_method == 'scale':
                # 缩放处理时间
                scale_factor = random.uniform(0.8, 1.2)
                processing_matrix = processing_matrix * scale_factor
                
            elif augmentation_method == 'permute_machines':
                # 置换机器顺序
                n_machines = processing_matrix.shape[1]
                permutation = np.random.permutation(n_machines)
                processing_matrix = processing_matrix[:, permutation]
                
            elif augmentation_method == 'add_noise':
                # 添加噪声
                noise_level = random.uniform(0.05, 0.15)
                noise = np.random.normal(0, noise_level, processing_matrix.shape)
                processing_matrix = processing_matrix * (1 + noise)
                processing_matrix = np.maximum(processing_matrix, 1.0)  # 确保处理时间为正
            
            # 更新实例
            augmented['processing_matrix'] = torch.tensor(processing_matrix, dtype=torch.float32)
            augmented['processing_times'] = processing_matrix
            augmented['filename'] = f"augmented_{augmentation_method}_{instance.get('filename', 'unknown')}"
            augmented['augmented'] = True
            augmented['augmentation_method'] = augmentation_method
            
            return augmented
            
        except Exception as e:
            print(f"⚠️ Error augmenting instance: {e}")
            return None
    
    def create_batch(self, instances: List[Dict], batch_size: int) -> List[Dict]:
        """
        创建批次
        
        Args:
            instances: 实例列表
            batch_size: 批次大小
            
        Returns:
            批次
        """
        if len(instances) <= batch_size:
            return instances
        
        # 随机选择实例
        return random.sample(instances, batch_size)
    
    def save_to_file(self, instances: List[Dict], output_dir: str):
        """
        保存实例到文件
        
        Args:
            instances: 实例列表
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        
        for i, instance in enumerate(instances):
            filename = instance.get('filename', f'instance_{i}.fjs')
            output_path = os.path.join(output_dir, filename)
            
            try:
                # 将实例转换为FJS格式
                fjs_content = self._convert_to_fjs_format(instance)
                
                # 保存到文件
                with open(output_path, 'w') as f:
                    f.write(fjs_content)
                    
            except Exception as e:
                print(f"⚠️ Error saving instance to {output_path}: {e}")
    
    def _convert_to_fjs_format(self, instance: Dict) -> str:
        """
        将实例转换为FJS格式
        
        Args:
            instance: 实例
            
        Returns:
            FJS格式的字符串
        """
        n_jobs = instance['n_jobs']
        n_machines = instance['n_machines']
        job_length = instance['job_length']
        
        if isinstance(instance['processing_matrix'], torch.Tensor):
            processing_matrix = instance['processing_matrix'].numpy()
        else:
            processing_matrix = instance['processing_matrix']
        
        # 构建FJS格式
        lines = []
        
        # 头部
        lines.append(f"{n_jobs} {n_machines}")
        
        # 作业
        op_idx = 0
        for job_id, n_ops in enumerate(job_length):
            job_line = f"{n_ops}"
            
            for _ in range(n_ops):
                job_line += f" {n_machines}"
                
                for m_id in range(n_machines):
                    proc_time = int(processing_matrix[op_idx, m_id])
                    job_line += f" {m_id+1} {proc_time}"
                
                op_idx += 1
            
            lines.append(job_line)
        
        return "\n".join(lines)
