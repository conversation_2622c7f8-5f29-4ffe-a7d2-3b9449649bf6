#!/usr/bin/env python3
"""
Training Data Generator
训练数据生成器 - 生成训练数据
"""

import os
import random
import numpy as np
import torch
from typing import List, Dict, Tuple, Optional, Any
from pathlib import Path

try:
    from ..core.data_parser import DataParser
except ImportError:
    import sys
    from pathlib import Path
    sys.path.append(str(Path(__file__).parent.parent))
    from core.data_parser import DataParser


class TrainingDataGenerator:
    """训练数据生成器"""
    
    def __init__(self, config):
        self.config = config
        self.data_parser = DataParser()
        
        # 数据增强参数
        self.enable_augmentation = getattr(config.data, 'enable_augmentation', True)
        self.augmentation_factor = getattr(config.data, 'augmentation_factor', 2)
    
    def load_from_directory(self, data_dir: str, max_instances: int = None) -> List[Dict]:
        """
        从目录加载数据
        
        Args:
            data_dir: 数据目录
            max_instances: 最大实例数量
            
        Returns:
            实例列表
        """
        print(f"📂 Loading data from {data_dir}")
        
        instances = []
        
        if not os.path.exists(data_dir):
            print(f"⚠️ Directory not found: {data_dir}")
            return instances
        
        # 支持多种文件格式
        file_extensions = ['.fjs', '.txt', '.dat']
        
        for file_ext in file_extensions:
            pattern = f"*{file_ext}"
            files = list(Path(data_dir).glob(pattern))
            
            for file_path in files:
                try:
                    instance_data = self.data_parser.parse_file(str(file_path))
                    if instance_data:
                        instance_data['filename'] = file_path.name
                        instance_data['filepath'] = str(file_path)
                        instances.append(instance_data)
                        
                        if max_instances and len(instances) >= max_instances:
                            break
                except Exception as e:
                    print(f"⚠️ Error loading {file_path}: {e}")
            
            if max_instances and len(instances) >= max_instances:
                break
        
        # 数据增强
        if self.enable_augmentation and instances:
            augmented_instances = self._augment_data(instances)
            instances.extend(augmented_instances)
            
            # 再次截断
            if max_instances and len(instances) > max_instances:
                instances = instances[:max_instances]
        
        print(f"✅ Loaded {len(instances)} instances")
        return instances
    
    def generate_synthetic_data(self, num_instances: int = 100) -> List[Dict]:
        """
        生成合成数据 - 增强版本，包含更多样化的实例

        Args:
            num_instances: 实例数量

        Returns:
            实例列表
        """
        print(f"🔧 Generating {num_instances} diverse synthetic instances")

        instances = []

        # 定义不同的问题规模类别
        problem_scales = [
            {'n_jobs_range': (3, 6), 'n_machines_range': (2, 4), 'op_length_range': (2, 4), 'proc_time_range': (1, 8)},   # 小规模
            {'n_jobs_range': (5, 8), 'n_machines_range': (3, 5), 'op_length_range': (2, 5), 'proc_time_range': (1, 12)},  # 中等规模
            {'n_jobs_range': (7, 12), 'n_machines_range': (4, 6), 'op_length_range': (3, 6), 'proc_time_range': (1, 15)}, # 大规模
            {'n_jobs_range': (10, 15), 'n_machines_range': (5, 8), 'op_length_range': (3, 7), 'proc_time_range': (1, 20)} # 超大规模
        ]

        # 按比例分配不同规模的实例
        scale_distribution = [0.3, 0.3, 0.25, 0.15]  # 小、中、大、超大的比例

        for scale_idx, (scale, ratio) in enumerate(zip(problem_scales, scale_distribution)):
            scale_instances = int(num_instances * ratio)

            for i in range(scale_instances):
                # 根据规模生成参数
                n_jobs = random.randint(*scale['n_jobs_range'])
                n_machines = random.randint(*scale['n_machines_range'])

                # 生成作业长度 - 增加变化性
                job_length = []
                for _ in range(n_jobs):
                    op_count = random.randint(*scale['op_length_range'])
                    job_length.append(op_count)

                n_operations = sum(job_length)

                # 生成处理时间矩阵 - 增加多样性
                processing_matrix = np.zeros((n_operations, n_machines), dtype=np.float32)

                # 添加不同的处理时间模式
                pattern = random.choice(['uniform', 'bimodal', 'exponential', 'normal'])

                for op_id in range(n_operations):
                    for m_id in range(n_machines):
                        if pattern == 'uniform':
                            proc_time = random.randint(*scale['proc_time_range'])
                        elif pattern == 'bimodal':
                            # 双峰分布
                            if random.random() < 0.5:
                                proc_time = random.randint(scale['proc_time_range'][0], scale['proc_time_range'][0] + 3)
                            else:
                                proc_time = random.randint(scale['proc_time_range'][1] - 3, scale['proc_time_range'][1])
                        elif pattern == 'exponential':
                            # 指数分布近似
                            proc_time = min(scale['proc_time_range'][1],
                                          max(scale['proc_time_range'][0],
                                              int(np.random.exponential(3) + scale['proc_time_range'][0])))
                        else:  # normal
                            # 正态分布
                            mean = (scale['proc_time_range'][0] + scale['proc_time_range'][1]) / 2
                            std = (scale['proc_time_range'][1] - scale['proc_time_range'][0]) / 4
                            proc_time = max(scale['proc_time_range'][0],
                                          min(scale['proc_time_range'][1],
                                              int(np.random.normal(mean, std))))

                        processing_matrix[op_id, m_id] = proc_time

                # 创建实例
                instance = {
                    'job_length': job_length,
                    'processing_matrix': torch.tensor(processing_matrix, dtype=torch.float32),
                    'processing_times': processing_matrix,
                    'n_jobs': n_jobs,
                    'n_machines': n_machines,
                    'n_operations': n_operations,
                    'filename': f'synthetic_scale{scale_idx}_{i}.fjs',
                    'synthetic': True,
                    'scale': scale_idx,
                    'pattern': pattern
                }

                instances.append(instance)

        # 如果还没达到目标数量，补充随机实例
        while len(instances) < num_instances:
            scale = random.choice(problem_scales)
            n_jobs = random.randint(*scale['n_jobs_range'])
            n_machines = random.randint(*scale['n_machines_range'])

            job_length = [random.randint(*scale['op_length_range']) for _ in range(n_jobs)]
            n_operations = sum(job_length)

            processing_matrix = np.zeros((n_operations, n_machines), dtype=np.float32)
            for op_id in range(n_operations):
                for m_id in range(n_machines):
                    processing_matrix[op_id, m_id] = random.randint(*scale['proc_time_range'])

            instance = {
                'job_length': job_length,
                'processing_matrix': torch.tensor(processing_matrix, dtype=torch.float32),
                'processing_times': processing_matrix,
                'n_jobs': n_jobs,
                'n_machines': n_machines,
                'n_operations': n_operations,
                'filename': f'synthetic_extra_{len(instances)}.fjs',
                'synthetic': True
            }

            instances.append(instance)

        # 打乱顺序
        random.shuffle(instances)

        print(f"✅ Generated {len(instances)} diverse synthetic instances")
        print(f"   Scale distribution: Small={int(num_instances*0.3)}, Medium={int(num_instances*0.3)}, Large={int(num_instances*0.25)}, XLarge={int(num_instances*0.15)}")
        return instances
    
    def _augment_data(self, instances: List[Dict]) -> List[Dict]:
        """
        数据增强
        
        Args:
            instances: 原始实例列表
            
        Returns:
            增强后的实例列表
        """
        augmented_instances = []
        
        for instance in instances:
            for _ in range(self.augmentation_factor):
                # 复制实例
                augmented = self._augment_instance(instance)
                if augmented:
                    augmented_instances.append(augmented)
        
        return augmented_instances
    
    def _augment_instance(self, instance: Dict) -> Optional[Dict]:
        """
        增强单个实例
        
        Args:
            instance: 原始实例
            
        Returns:
            增强后的实例
        """
        try:
            # 复制实例
            augmented = instance.copy()
            
            # 获取处理时间矩阵
            if isinstance(instance['processing_matrix'], torch.Tensor):
                processing_matrix = instance['processing_matrix'].clone().numpy()
            else:
                processing_matrix = np.array(instance['processing_matrix'])
            
            # 随机选择增强方法
            augmentation_method = random.choice([
                'scale', 'permute_machines', 'add_noise'
            ])
            
            if augmentation_method == 'scale':
                # 缩放处理时间
                scale_factor = random.uniform(0.8, 1.2)
                processing_matrix = processing_matrix * scale_factor
                
            elif augmentation_method == 'permute_machines':
                # 置换机器顺序
                n_machines = processing_matrix.shape[1]
                permutation = np.random.permutation(n_machines)
                processing_matrix = processing_matrix[:, permutation]
                
            elif augmentation_method == 'add_noise':
                # 添加噪声
                noise_level = random.uniform(0.05, 0.15)
                noise = np.random.normal(0, noise_level, processing_matrix.shape)
                processing_matrix = processing_matrix * (1 + noise)
                processing_matrix = np.maximum(processing_matrix, 1.0)  # 确保处理时间为正
            
            # 更新实例
            augmented['processing_matrix'] = torch.tensor(processing_matrix, dtype=torch.float32)
            augmented['processing_times'] = processing_matrix
            augmented['filename'] = f"augmented_{augmentation_method}_{instance.get('filename', 'unknown')}"
            augmented['augmented'] = True
            augmented['augmentation_method'] = augmentation_method
            
            return augmented
            
        except Exception as e:
            print(f"⚠️ Error augmenting instance: {e}")
            return None
    
    def create_batch(self, instances: List[Dict], batch_size: int) -> List[Dict]:
        """
        创建批次
        
        Args:
            instances: 实例列表
            batch_size: 批次大小
            
        Returns:
            批次
        """
        if len(instances) <= batch_size:
            return instances
        
        # 随机选择实例
        return random.sample(instances, batch_size)
    
    def save_to_file(self, instances: List[Dict], output_dir: str):
        """
        保存实例到文件
        
        Args:
            instances: 实例列表
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)
        
        for i, instance in enumerate(instances):
            filename = instance.get('filename', f'instance_{i}.fjs')
            output_path = os.path.join(output_dir, filename)
            
            try:
                # 将实例转换为FJS格式
                fjs_content = self._convert_to_fjs_format(instance)
                
                # 保存到文件
                with open(output_path, 'w') as f:
                    f.write(fjs_content)
                    
            except Exception as e:
                print(f"⚠️ Error saving instance to {output_path}: {e}")
    
    def _convert_to_fjs_format(self, instance: Dict) -> str:
        """
        将实例转换为FJS格式
        
        Args:
            instance: 实例
            
        Returns:
            FJS格式的字符串
        """
        n_jobs = instance['n_jobs']
        n_machines = instance['n_machines']
        job_length = instance['job_length']
        
        if isinstance(instance['processing_matrix'], torch.Tensor):
            processing_matrix = instance['processing_matrix'].numpy()
        else:
            processing_matrix = instance['processing_matrix']
        
        # 构建FJS格式
        lines = []
        
        # 头部
        lines.append(f"{n_jobs} {n_machines}")
        
        # 作业
        op_idx = 0
        for job_id, n_ops in enumerate(job_length):
            job_line = f"{n_ops}"
            
            for _ in range(n_ops):
                job_line += f" {n_machines}"
                
                for m_id in range(n_machines):
                    proc_time = int(processing_matrix[op_idx, m_id])
                    job_line += f" {m_id+1} {proc_time}"
                
                op_idx += 1
            
            lines.append(job_line)
        
        return "\n".join(lines)
