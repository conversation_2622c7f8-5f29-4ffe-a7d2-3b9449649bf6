#!/usr/bin/env python3
"""
Training Utils
训练工具 - 包含早停、学习率调度等训练辅助功能
"""

import numpy as np
from typing import Optional, List, Dict, Any


class EarlyStopping:
    """早停机制"""
    
    def __init__(self, patience: int = 10, min_delta: float = 0.0, restore_best_weights: bool = True):
        """
        初始化早停机制
        
        Args:
            patience: 容忍的无改进epoch数量
            min_delta: 最小改进阈值
            restore_best_weights: 是否恢复最佳权重
        """
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        
        self.best_loss = float('inf')
        self.counter = 0
        self.best_weights = None
        self.early_stop = False
    
    def __call__(self, val_loss: float, model=None) -> bool:
        """
        检查是否应该早停
        
        Args:
            val_loss: 验证损失
            model: 模型（用于保存最佳权重）
            
        Returns:
            是否应该早停
        """
        if val_loss < self.best_loss - self.min_delta:
            # 有改进
            self.best_loss = val_loss
            self.counter = 0
            
            # 保存最佳权重
            if self.restore_best_weights and model is not None:
                self.best_weights = model.state_dict().copy()
                
        else:
            # 无改进
            self.counter += 1
            
            if self.counter >= self.patience:
                self.early_stop = True
                
                # 恢复最佳权重
                if self.restore_best_weights and self.best_weights is not None and model is not None:
                    model.load_state_dict(self.best_weights)
        
        return self.early_stop
    
    def reset(self):
        """重置早停状态"""
        self.best_loss = float('inf')
        self.counter = 0
        self.best_weights = None
        self.early_stop = False


class LearningRateScheduler:
    """学习率调度器"""
    
    def __init__(self, optimizer, scheduler_type: str = 'cosine', **kwargs):
        """
        初始化学习率调度器
        
        Args:
            optimizer: 优化器
            scheduler_type: 调度器类型 ('cosine', 'step', 'exponential', 'plateau')
            **kwargs: 调度器参数
        """
        self.optimizer = optimizer
        self.scheduler_type = scheduler_type
        
        if scheduler_type == 'cosine':
            from torch.optim.lr_scheduler import CosineAnnealingLR
            self.scheduler = CosineAnnealingLR(
                optimizer,
                T_max=kwargs.get('T_max', 100),
                eta_min=kwargs.get('eta_min', 0)
            )
        elif scheduler_type == 'step':
            from torch.optim.lr_scheduler import StepLR
            self.scheduler = StepLR(
                optimizer,
                step_size=kwargs.get('step_size', 30),
                gamma=kwargs.get('gamma', 0.1)
            )
        elif scheduler_type == 'exponential':
            from torch.optim.lr_scheduler import ExponentialLR
            self.scheduler = ExponentialLR(
                optimizer,
                gamma=kwargs.get('gamma', 0.95)
            )
        elif scheduler_type == 'plateau':
            from torch.optim.lr_scheduler import ReduceLROnPlateau
            self.scheduler = ReduceLROnPlateau(
                optimizer,
                mode=kwargs.get('mode', 'min'),
                factor=kwargs.get('factor', 0.5),
                patience=kwargs.get('patience', 10),
                min_lr=kwargs.get('min_lr', 0)
            )
        else:
            raise ValueError(f"Unknown scheduler type: {scheduler_type}")
    
    def step(self, val_loss: Optional[float] = None):
        """执行学习率调度步骤"""
        if self.scheduler_type == 'plateau':
            if val_loss is not None:
                self.scheduler.step(val_loss)
        else:
            self.scheduler.step()
    
    def get_last_lr(self) -> List[float]:
        """获取最后的学习率"""
        return self.scheduler.get_last_lr()
    
    def state_dict(self) -> Dict:
        """获取状态字典"""
        return self.scheduler.state_dict()
    
    def load_state_dict(self, state_dict: Dict):
        """加载状态字典"""
        self.scheduler.load_state_dict(state_dict)


class MetricsTracker:
    """指标跟踪器"""
    
    def __init__(self):
        self.metrics = {}
        self.history = []
    
    def update(self, **kwargs):
        """更新指标"""
        for key, value in kwargs.items():
            if key not in self.metrics:
                self.metrics[key] = []
            self.metrics[key].append(value)
    
    def get_average(self, key: str, last_n: Optional[int] = None) -> float:
        """获取指标的平均值"""
        if key not in self.metrics:
            return 0.0
        
        values = self.metrics[key]
        if last_n is not None:
            values = values[-last_n:]
        
        return np.mean(values) if values else 0.0
    
    def get_latest(self, key: str) -> float:
        """获取指标的最新值"""
        if key not in self.metrics or not self.metrics[key]:
            return 0.0
        return self.metrics[key][-1]
    
    def reset(self):
        """重置所有指标"""
        self.metrics = {}
        self.history = []
    
    def save_epoch(self, epoch: int):
        """保存当前epoch的指标"""
        epoch_metrics = {'epoch': epoch}
        for key, values in self.metrics.items():
            if values:
                epoch_metrics[key] = values[-1]
        
        self.history.append(epoch_metrics)
        
        # 清空当前epoch的指标
        self.metrics = {}
    
    def get_history(self) -> List[Dict]:
        """获取历史记录"""
        return self.history


class GradientClipper:
    """梯度裁剪器"""
    
    def __init__(self, max_norm: float = 1.0, norm_type: float = 2.0):
        """
        初始化梯度裁剪器
        
        Args:
            max_norm: 最大梯度范数
            norm_type: 范数类型
        """
        self.max_norm = max_norm
        self.norm_type = norm_type
    
    def clip_gradients(self, model) -> float:
        """
        裁剪梯度
        
        Args:
            model: 模型
            
        Returns:
            梯度范数
        """
        import torch.nn as nn
        
        total_norm = nn.utils.clip_grad_norm_(
            model.parameters(),
            max_norm=self.max_norm,
            norm_type=self.norm_type
        )
        
        return total_norm.item()


class ModelCheckpointer:
    """模型检查点管理器"""
    
    def __init__(self, checkpoint_dir: str, save_best: bool = True, save_last: bool = True):
        """
        初始化检查点管理器
        
        Args:
            checkpoint_dir: 检查点目录
            save_best: 是否保存最佳模型
            save_last: 是否保存最新模型
        """
        import os
        
        self.checkpoint_dir = checkpoint_dir
        self.save_best = save_best
        self.save_last = save_last
        
        os.makedirs(checkpoint_dir, exist_ok=True)
        
        self.best_metric = float('inf')
        self.best_epoch = 0
    
    def save_checkpoint(self, model, optimizer, scheduler, epoch: int, 
                       metric: float, is_best: bool = False):
        """
        保存检查点
        
        Args:
            model: 模型
            optimizer: 优化器
            scheduler: 学习率调度器
            epoch: 当前epoch
            metric: 当前指标值
            is_best: 是否为最佳模型
        """
        import torch
        import os
        
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
            'metric': metric,
            'best_metric': self.best_metric,
            'best_epoch': self.best_epoch
        }
        
        # 保存最新模型
        if self.save_last:
            last_path = os.path.join(self.checkpoint_dir, 'last_checkpoint.pth')
            torch.save(checkpoint, last_path)
        
        # 保存最佳模型
        if is_best and self.save_best:
            best_path = os.path.join(self.checkpoint_dir, 'best_checkpoint.pth')
            torch.save(checkpoint, best_path)
            
            self.best_metric = metric
            self.best_epoch = epoch
        
        # 保存定期检查点
        if epoch % 10 == 0:
            epoch_path = os.path.join(self.checkpoint_dir, f'checkpoint_epoch_{epoch}.pth')
            torch.save(checkpoint, epoch_path)
    
    def load_checkpoint(self, model, optimizer=None, scheduler=None, 
                       checkpoint_path: str = None) -> Dict:
        """
        加载检查点
        
        Args:
            model: 模型
            optimizer: 优化器
            scheduler: 学习率调度器
            checkpoint_path: 检查点路径，如果为None则加载最佳检查点
            
        Returns:
            检查点信息
        """
        import torch
        import os
        
        if checkpoint_path is None:
            checkpoint_path = os.path.join(self.checkpoint_dir, 'best_checkpoint.pth')
        
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")
        
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        # 加载模型状态
        model.load_state_dict(checkpoint['model_state_dict'])
        
        # 加载优化器状态
        if optimizer is not None and 'optimizer_state_dict' in checkpoint:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        # 加载调度器状态
        if scheduler is not None and 'scheduler_state_dict' in checkpoint:
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        # 更新最佳指标
        self.best_metric = checkpoint.get('best_metric', float('inf'))
        self.best_epoch = checkpoint.get('best_epoch', 0)
        
        return {
            'epoch': checkpoint.get('epoch', 0),
            'metric': checkpoint.get('metric', float('inf')),
            'best_metric': self.best_metric,
            'best_epoch': self.best_epoch
        }
