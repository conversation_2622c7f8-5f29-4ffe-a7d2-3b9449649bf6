#!/usr/bin/env python3
"""
Loss Functions
损失函数 - 神经列生成模型的损失函数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple, Optional, Any


class MakespanLoss(nn.Module):
    """Makespan损失函数"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.weight = getattr(config.training, 'makespan_loss_weight', 1.0)
    
    def forward(self, columns: List, instance_data: Dict) -> torch.Tensor:
        """
        计算makespan损失

        Args:
            columns: 生成的列列表
            instance_data: 实例数据

        Returns:
            makespan损失
        """
        # 提取makespan
        makespans = []
        for column in columns:
            if hasattr(column, 'makespan') and column.makespan < float('inf'):
                makespans.append(column.makespan)

        if not makespans:
            # 创建一个需要梯度的零张量
            return torch.zeros(1, requires_grad=True) * self.weight

        # 转换为张量并确保需要梯度
        makespans_tensor = torch.tensor(makespans, dtype=torch.float32, requires_grad=True)

        # 归一化makespan损失 - 除以问题规模的估计值
        n_jobs = instance_data.get('n_jobs', 1)
        n_machines = instance_data.get('n_machines', 1)
        scale_factor = max(n_jobs * n_machines, 1)  # 简单的规模估计

        # 计算损失（归一化的平均makespan）
        loss = torch.mean(makespans_tensor) / scale_factor

        return loss * self.weight


class FeasibilityLoss(nn.Module):
    """可行性损失函数"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.weight = getattr(config.training, 'feasibility_loss_weight', 1.0)
    
    def forward(self, columns: List, instance_data: Dict) -> torch.Tensor:
        """
        计算可行性损失
        
        Args:
            columns: 生成的列列表
            instance_data: 实例数据
            
        Returns:
            可行性损失
        """
        # 计算不可行列的比例
        feasible_count = sum(1 for col in columns if hasattr(col, 'feasible') and col.feasible)
        total_count = len(columns)
        
        if total_count == 0:
            return torch.zeros(1, requires_grad=True) * self.weight

        infeasible_ratio = 1.0 - (feasible_count / total_count)

        # 转换为张量并确保需要梯度
        loss = torch.tensor(infeasible_ratio, dtype=torch.float32, requires_grad=True)

        return loss * self.weight


class DiversityLoss(nn.Module):
    """多样性损失函数"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.weight = getattr(config.training, 'diversity_loss_weight', 0.5)
    
    def forward(self, columns: List, instance_data: Dict) -> torch.Tensor:
        """
        计算多样性损失
        
        Args:
            columns: 生成的列列表
            instance_data: 实例数据
            
        Returns:
            多样性损失
        """
        if len(columns) <= 1:
            return torch.zeros(1, requires_grad=True) * self.weight

        # 提取调度
        schedules = []
        for column in columns:
            if hasattr(column, 'schedule') and column.schedule:
                schedules.append(column.schedule)

        if len(schedules) <= 1:
            return torch.zeros(1, requires_grad=True) * self.weight
        
        # 计算调度之间的相似度
        similarities = []
        for i in range(len(schedules)):
            for j in range(i + 1, len(schedules)):
                sim = self._calculate_schedule_similarity(schedules[i], schedules[j])
                similarities.append(sim)
        
        # 转换为张量
        if not similarities:
            return torch.zeros(1, requires_grad=True) * self.weight

        similarities_tensor = torch.tensor(similarities, dtype=torch.float32, requires_grad=True)

        # 计算损失（平均相似度）
        loss = torch.mean(similarities_tensor)

        return loss * self.weight
    
    def _calculate_schedule_similarity(self, schedule1: List, schedule2: List) -> float:
        """
        计算两个调度之间的相似度
        
        Args:
            schedule1: 第一个调度
            schedule2: 第二个调度
            
        Returns:
            相似度（0-1之间）
        """
        # 转换为集合
        set1 = set((op, m) for op, m in schedule1)
        set2 = set((op, m) for op, m in schedule2)
        
        # 计算Jaccard相似度
        intersection = len(set1.intersection(set2))
        union = len(set1.union(set2))
        
        if union == 0:
            return 0.0
        
        return intersection / union


class DecisionQualityLoss(nn.Module):
    """决策质量损失函数"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.weight = getattr(config.training, 'decision_quality_loss_weight', 0.5)
    
    def forward(self, columns: List, instance_data: Dict) -> torch.Tensor:
        """
        计算决策质量损失
        
        Args:
            columns: 生成的列列表
            instance_data: 实例数据
            
        Returns:
            决策质量损失
        """
        # 提取决策质量
        qualities = []
        for column in columns:
            if hasattr(column, 'decision_quality'):
                qualities.append(1.0 - column.decision_quality)  # 转换为损失
        
        if not qualities:
            return torch.zeros(1, requires_grad=True) * self.weight

        # 转换为张量并确保需要梯度
        qualities_tensor = torch.tensor(qualities, dtype=torch.float32, requires_grad=True)

        # 计算损失（平均决策质量损失）
        loss = torch.mean(qualities_tensor)

        return loss * self.weight


class CombinedLoss(nn.Module):
    """组合损失函数"""

    def __init__(self, config):
        super().__init__()
        self.config = config

        # 创建各个损失函数
        self.makespan_loss = MakespanLoss(config)
        self.feasibility_loss = FeasibilityLoss(config)
        self.diversity_loss = DiversityLoss(config)
        self.decision_quality_loss = DecisionQualityLoss(config)

        # 损失权重调整
        self.adaptive_weights = getattr(config.training, 'adaptive_loss_weights', False)
        self.loss_history = {
            'makespan': [],
            'feasibility': [],
            'diversity': [],
            'decision_quality': []
        }
    
    def forward(self, columns: List, instance_data: Dict) -> Tuple[torch.Tensor, Dict]:
        """
        计算组合损失

        Args:
            columns: 生成的列列表
            instance_data: 实例数据

        Returns:
            总损失和各个损失组件
        """
        # 计算各个损失
        makespan_loss = self.makespan_loss(columns, instance_data)
        feasibility_loss = self.feasibility_loss(columns, instance_data)
        diversity_loss = self.diversity_loss(columns, instance_data)
        decision_quality_loss = self.decision_quality_loss(columns, instance_data)

        # 更新损失历史
        self.loss_history['makespan'].append(makespan_loss.item())
        self.loss_history['feasibility'].append(feasibility_loss.item())
        self.loss_history['diversity'].append(diversity_loss.item())
        self.loss_history['decision_quality'].append(decision_quality_loss.item())

        # 更稳定的自适应权重调整
        if self.adaptive_weights and len(self.loss_history['makespan']) > 50:  # 需要更多历史数据
            # 计算最近20次的平均损失和标准差
            window_size = 20
            recent_losses = {
                'makespan': np.mean(self.loss_history['makespan'][-window_size:]),
                'feasibility': np.mean(self.loss_history['feasibility'][-window_size:]),
                'diversity': np.mean(self.loss_history['diversity'][-window_size:]),
                'decision_quality': np.mean(self.loss_history['decision_quality'][-window_size:])
            }

            # 计算损失的稳定性（标准差）
            loss_stds = {
                'makespan': np.std(self.loss_history['makespan'][-window_size:]),
                'feasibility': np.std(self.loss_history['feasibility'][-window_size:]),
                'diversity': np.std(self.loss_history['diversity'][-window_size:]),
                'decision_quality': np.std(self.loss_history['decision_quality'][-window_size:])
            }

            # 只有在损失稳定时才调整权重
            total_recent_loss = sum(recent_losses.values())
            total_std = sum(loss_stds.values())

            if total_recent_loss > 0 and total_std < 0.1:  # 损失相对稳定时才调整
                # 更温和的权重调整
                adjustment_factor = 0.2  # 减少调整幅度
                makespan_weight = 1.0 - (recent_losses['makespan'] / total_recent_loss * adjustment_factor)
                feasibility_weight = 1.0 - (recent_losses['feasibility'] / total_recent_loss * adjustment_factor)
                diversity_weight = 1.0 - (recent_losses['diversity'] / total_recent_loss * adjustment_factor)
                decision_quality_weight = 1.0 - (recent_losses['decision_quality'] / total_recent_loss * adjustment_factor)

                # 确保权重在合理范围内
                makespan_weight = max(0.5, min(1.5, makespan_weight))
                feasibility_weight = max(0.5, min(1.5, feasibility_weight))
                diversity_weight = max(0.5, min(1.5, diversity_weight))
                decision_quality_weight = max(0.5, min(1.5, decision_quality_weight))

                # 应用调整后的权重
                makespan_loss = makespan_loss * makespan_weight
                feasibility_loss = feasibility_loss * feasibility_weight
                diversity_loss = diversity_loss * diversity_weight
                decision_quality_loss = decision_quality_loss * decision_quality_weight

        # 计算总损失
        total_loss = makespan_loss + feasibility_loss + diversity_loss + decision_quality_loss

        # 返回总损失和各个损失组件
        loss_components = {
            'makespan_loss': makespan_loss.item(),
            'feasibility_loss': feasibility_loss.item(),
            'diversity_loss': diversity_loss.item(),
            'decision_quality_loss': decision_quality_loss.item()
        }

        return total_loss, loss_components
