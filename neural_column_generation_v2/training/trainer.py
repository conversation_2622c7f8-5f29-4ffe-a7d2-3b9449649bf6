#!/usr/bin/env python3
"""
Neural Column Trainer
神经列生成训练器 - 负责模型训练
"""

import os
import time
import json
import numpy as np
from typing import List, Dict, Tuple, Optional, Any
from pathlib import Path
import torch
import torch.nn as nn
from torch.utils.data import DataLoader, Dataset

from .training_utils import EarlyStopping, LearningRateScheduler
from .loss_functions import CombinedLoss
from .training_visualizer import TrainingVisualizer


class FJSPDataset(Dataset):
    """FJSP数据集"""
    
    def __init__(self, data_list: List[Dict]):
        self.data = data_list
    
    def __len__(self):
        return len(self.data)
    
    def __getitem__(self, idx):
        return self.data[idx]


class NeuralColumnTrainer:
    """神经列生成训练器"""
    
    def __init__(self, model, column_generator, evaluator, feature_extractor,
                criterion, optimizer, scheduler, config, device, output_dir):
        self.model = model
        self.column_generator = column_generator
        self.evaluator = evaluator
        self.feature_extractor = feature_extractor
        self.criterion = criterion
        self.optimizer = optimizer
        self.scheduler = scheduler
        self.config = config
        self.device = device
        self.output_dir = output_dir

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(os.path.join(output_dir, 'checkpoints'), exist_ok=True)

        # 训练状态
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        self.training_history = []

        # 早停
        self.early_stopping = EarlyStopping(
            patience=config.training.early_stopping_patience,
            min_delta=config.training.early_stopping_min_delta
        )

        # 可视化器
        self.visualizer = None
        if hasattr(config.training, 'enable_visualization') and config.training.enable_visualization:
            self.visualizer = TrainingVisualizer(
                output_dir=os.path.join(output_dir, 'visualization'),
                update_interval=config.training.visualization_update_interval if hasattr(config.training, 'visualization_update_interval') else 10
            )
    
    def train(self, train_data: List[Dict], val_data: List[Dict] = None) -> List[Dict]:
        """训练模型"""
        print(f"🎯 Starting training with {len(train_data)} instances")

        # 启动可视化监控
        if self.visualizer:
            self.visualizer.start_monitoring()
            print("📊 Training visualization enabled")

        # 创建数据加载器
        train_dataset = FJSPDataset(train_data)
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config.training.batch_size,
            shuffle=True,
            num_workers=self.config.training.num_workers,
            collate_fn=lambda x: x  # 不进行批处理，直接返回列表
        )

        if val_data:
            val_dataset = FJSPDataset(val_data)
            val_loader = DataLoader(
                val_dataset,
                batch_size=self.config.training.batch_size,
                shuffle=False,
                num_workers=self.config.training.num_workers,
                collate_fn=lambda x: x
            )
        else:
            val_loader = None
        
        # 训练循环
        for epoch in range(self.current_epoch, self.config.training.num_epochs):
            self.current_epoch = epoch
            
            # 训练一个epoch
            train_loss, train_metrics = self._train_epoch(train_loader)
            
            # 验证
            val_loss, val_metrics = None, None
            if val_loader:
                val_loss, val_metrics = self._validate(val_loader)
            
            # 学习率调度
            if self.scheduler:
                self.scheduler.step()
            
            # 记录历史
            epoch_info = {
                'epoch': epoch,
                'train_loss': train_loss,
                'train_metrics': train_metrics,
                'val_loss': val_loss,
                'val_metrics': val_metrics,
                'lr': self.optimizer.param_groups[0]['lr']
            }
            self.training_history.append(epoch_info)
            
            # 打印进度
            self._print_progress(epoch_info)

            # 更新可视化
            if self.visualizer:
                self.visualizer.log_epoch(epoch_info)

            # 保存检查点
            if val_loss is not None and val_loss < self.best_val_loss:
                self.best_val_loss = val_loss
                self._save_checkpoint(is_best=True)
                print(f"   📌 New best model saved! (val_loss: {val_loss:.4f})")

            # 定期保存检查点
            if (epoch + 1) % self.config.training.checkpoint_interval == 0:
                self._save_checkpoint()

            # 早停
            if val_loss is not None and self.early_stopping(val_loss):
                print(f"⏹️ Early stopping triggered after {epoch + 1} epochs")
                break

        # 保存最终模型
        self._save_checkpoint(is_final=True)

        # 生成最终可视化报告
        if self.visualizer:
            self.visualizer.generate_final_report()
            self.visualizer.stop_monitoring()

        print(f"✅ Training completed after {self.current_epoch + 1} epochs")
        return self.training_history
    
    def _train_epoch(self, data_loader) -> Tuple[float, Dict]:
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        metrics = {
            'makespan_loss': 0.0,
            'feasibility_loss': 0.0,
            'diversity_loss': 0.0,
            'decision_quality_loss': 0.0
        }
        
        start_time = time.time()
        batch_count = 0
        
        for batch_idx, batch_data in enumerate(data_loader):
            batch_count += 1
            batch_loss, batch_metrics = self._train_batch(batch_data)
            
            total_loss += batch_loss
            
            # 累积指标
            for key, value in batch_metrics.items():
                if key in metrics:
                    metrics[key] += value
            
            # 打印批次进度
            if (batch_idx + 1) % self.config.training.log_interval == 0:
                print(f"   Batch {batch_idx + 1}/{len(data_loader)}: loss={batch_loss:.4f}")
        
        # 计算平均值
        avg_loss = total_loss / batch_count if batch_count > 0 else 0.0
        for key in metrics:
            metrics[key] = metrics[key] / batch_count if batch_count > 0 else 0.0
        
        metrics['time'] = time.time() - start_time
        
        return avg_loss, metrics
    
    def _train_batch(self, batch_data: List[Dict]) -> Tuple[float, Dict]:
        """训练单个批次"""
        self.optimizer.zero_grad()
        
        batch_loss = 0.0
        batch_metrics = {
            'makespan_loss': 0.0,
            'feasibility_loss': 0.0,
            'diversity_loss': 0.0,
            'decision_quality_loss': 0.0
        }
        
        for instance in batch_data:
            try:
                # 提取特征
                features = self.feature_extractor.extract_features(instance)
                
                # 生成列
                columns = self.column_generator.generate_columns(
                    features,
                    instance['job_length'],
                    num_columns=self.config.model.num_columns
                )
                
                # 评估列
                evaluated_columns = []
                for column in columns:
                    evaluated_column = self.evaluator.evaluate_column(column, instance)
                    evaluated_columns.append(evaluated_column)
                
                # 计算损失
                loss, loss_components = self.criterion(evaluated_columns, instance)
                
                # 反向传播
                loss.backward()
                
                # 累积损失和指标
                batch_loss += loss.item()
                for key, value in loss_components.items():
                    if key in batch_metrics:
                        batch_metrics[key] += value
                
            except Exception as e:
                print(f"⚠️ Error in training batch: {e}")
                continue
        
        # 梯度裁剪
        if self.config.training.gradient_clip > 0:
            nn.utils.clip_grad_norm_(
                self.model.parameters(), 
                self.config.training.gradient_clip
            )
        
        # 更新参数
        self.optimizer.step()
        
        # 计算平均值
        batch_size = len(batch_data)
        batch_loss = batch_loss / batch_size if batch_size > 0 else 0.0
        for key in batch_metrics:
            batch_metrics[key] = batch_metrics[key] / batch_size if batch_size > 0 else 0.0
        
        return batch_loss, batch_metrics
    
    def _validate(self, data_loader) -> Tuple[float, Dict]:
        """验证模型"""
        self.model.eval()
        total_loss = 0.0
        metrics = {
            'makespan_loss': 0.0,
            'feasibility_loss': 0.0,
            'diversity_loss': 0.0,
            'decision_quality_loss': 0.0,
            'feasible_columns': 0,
            'total_columns': 0
        }
        
        with torch.no_grad():
            batch_count = 0
            
            for batch_data in data_loader:
                batch_count += 1
                batch_loss, batch_metrics = self._validate_batch(batch_data)
                
                total_loss += batch_loss
                
                # 累积指标
                for key, value in batch_metrics.items():
                    if key in metrics:
                        metrics[key] += value
        
        # 计算平均值
        avg_loss = total_loss / batch_count if batch_count > 0 else 0.0
        for key in metrics:
            metrics[key] = metrics[key] / batch_count if batch_count > 0 else 0.0
        
        return avg_loss, metrics
    
    def _validate_batch(self, batch_data: List[Dict]) -> Tuple[float, Dict]:
        """验证单个批次"""
        batch_loss = 0.0
        batch_metrics = {
            'makespan_loss': 0.0,
            'feasibility_loss': 0.0,
            'diversity_loss': 0.0,
            'decision_quality_loss': 0.0,
            'feasible_columns': 0,
            'total_columns': 0
        }
        
        for instance in batch_data:
            try:
                # 提取特征
                features = self.feature_extractor.extract_features(instance)
                
                # 生成列
                columns = self.column_generator.generate_columns(
                    features,
                    instance['job_length'],
                    num_columns=self.config.model.num_columns
                )
                
                # 评估列
                evaluated_columns = []
                for column in columns:
                    evaluated_column = self.evaluator.evaluate_column(column, instance)
                    evaluated_columns.append(evaluated_column)
                
                # 计算损失
                loss, loss_components = self.criterion(evaluated_columns, instance)
                
                # 累积损失和指标
                batch_loss += loss.item()
                for key, value in loss_components.items():
                    if key in batch_metrics:
                        batch_metrics[key] += value
                
                # 统计可行列数量
                feasible_columns = sum(1 for col in evaluated_columns if col.feasible)
                batch_metrics['feasible_columns'] += feasible_columns
                batch_metrics['total_columns'] += len(evaluated_columns)
                
            except Exception as e:
                print(f"⚠️ Error in validation batch: {e}")
                continue
        
        # 计算平均值
        batch_size = len(batch_data)
        batch_loss = batch_loss / batch_size if batch_size > 0 else 0.0
        for key in batch_metrics:
            batch_metrics[key] = batch_metrics[key] / batch_size if batch_size > 0 else 0.0
        
        return batch_loss, batch_metrics

    def _print_progress(self, epoch_info: Dict):
        """打印训练进度"""
        epoch = epoch_info['epoch']
        train_loss = epoch_info['train_loss']
        val_loss = epoch_info.get('val_loss')
        lr = epoch_info['lr']

        print(f"Epoch {epoch + 1:3d}: train_loss={train_loss:.4f}", end="")

        if val_loss is not None:
            print(f", val_loss={val_loss:.4f}", end="")

        print(f", lr={lr:.6f}")

        # 打印详细指标
        train_metrics = epoch_info.get('train_metrics', {})
        if train_metrics:
            print(f"         train_metrics: ", end="")
            for key, value in train_metrics.items():
                if key != 'time':
                    print(f"{key}={value:.4f} ", end="")
            print()

    def _save_checkpoint(self, is_best: bool = False, is_final: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': self.current_epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict() if self.scheduler else None,
            'best_val_loss': self.best_val_loss,
            'training_history': self.training_history,
            'config': self.config.__dict__ if hasattr(self.config, '__dict__') else str(self.config)
        }

        # 保存常规检查点
        checkpoint_path = os.path.join(
            self.output_dir, 'checkpoints', f'checkpoint_epoch_{self.current_epoch}.pth'
        )
        torch.save(checkpoint, checkpoint_path)

        # 保存最佳模型
        if is_best:
            best_path = os.path.join(self.output_dir, 'best_model.pth')
            torch.save(checkpoint, best_path)

        # 保存最终模型
        if is_final:
            final_path = os.path.join(self.output_dir, 'final_model.pth')
            torch.save(checkpoint, final_path)

    def load_checkpoint(self, checkpoint_path: str):
        """加载检查点"""
        print(f"📂 Loading checkpoint from {checkpoint_path}")

        try:
            checkpoint = torch.load(checkpoint_path, map_location=self.device)

            # 加载模型状态
            self.model.load_state_dict(checkpoint['model_state_dict'])

            # 加载优化器状态
            self.optimizer.load_state_dict(checkpoint['optimizer_state_dict'])

            # 加载调度器状态
            if self.scheduler and checkpoint.get('scheduler_state_dict'):
                self.scheduler.load_state_dict(checkpoint['scheduler_state_dict'])

            # 恢复训练状态
            self.current_epoch = checkpoint.get('epoch', 0) + 1
            self.best_val_loss = checkpoint.get('best_val_loss', float('inf'))
            self.training_history = checkpoint.get('training_history', [])

            print(f"✅ Checkpoint loaded. Resuming from epoch {self.current_epoch}")

        except Exception as e:
            print(f"❌ Failed to load checkpoint: {e}")
            raise

    def validate(self, val_data: List[Dict]) -> Dict:
        """单独运行验证"""
        print(f"🔍 Running validation on {len(val_data)} instances")

        val_dataset = FJSPDataset(val_data)
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config.training.batch_size,
            shuffle=False,
            num_workers=self.config.training.num_workers,
            collate_fn=lambda x: x
        )

        val_loss, val_metrics = self._validate(val_loader)

        print(f"✅ Validation completed: loss={val_loss:.4f}")

        return {
            'val_loss': val_loss,
            'val_metrics': val_metrics
        }
