{"model": {"d_model": 128, "n_heads": 8, "n_layers": 4, "dropout": 0.1, "job_feature_dim": 10, "operation_feature_dim": 15, "machine_feature_dim": 8, "num_columns": 5}, "training": {"batch_size": 4, "num_epochs": 50, "learning_rate": 0.001, "weight_decay": 1e-05, "gradient_clip": 1.0, "early_stopping_patience": 15, "early_stopping_min_delta": 0.001, "checkpoint_interval": 5, "num_workers": 2, "log_interval": 1, "makespan_loss_weight": 1.0, "feasibility_loss_weight": 1.0, "diversity_loss_weight": 0.5, "decision_quality_loss_weight": 0.5, "enable_visualization": true, "visualization_update_interval": 5, "adaptive_loss_weights": true, "normalize_losses": true, "scheduler_type": "plateau", "scheduler_patience": 5, "scheduler_factor": 0.5, "scheduler_min_lr": 1e-06}, "data": {"train_split": 0.8, "max_train_instances": 100, "enable_augmentation": true, "augmentation_factor": 2}, "evaluation": {"time_limit": 300.0, "max_iterations": 50, "max_columns_per_iteration": 10, "convergence_tolerance": 1e-06}, "branch_and_price": {"max_iterations": 50, "max_columns_per_iteration": 10, "convergence_tolerance": 1e-06, "time_limit": 300.0, "min_improvement": 0.01, "max_no_improvement_iterations": 5}}