{"model": {"d_model": 256, "n_heads": 8, "n_layers": 4, "dropout": 0.1, "job_feature_dim": 10, "operation_feature_dim": 15, "machine_feature_dim": 8, "num_columns": 5, "max_iterations": 100, "diversity_weight": 0.1}, "training": {"batch_size": 4, "num_epochs": 5, "learning_rate": 0.001, "weight_decay": 0.01, "optimizer": "AdamW", "scheduler": "CosineAnnealingLR", "warmup_epochs": 5, "gradient_clip": 1.0, "patience": 10, "min_delta": 0.0001, "makespan_weight": 2.0, "feasibility_weight": 1.0, "decision_quality_weight": 1.0, "diversity_weight": 0.5}, "data": {"raw_data_dir": "../data", "processed_data_dir": "data/processed", "model_save_dir": "data/models", "train_ratio": 0.8, "val_ratio": 0.1, "test_ratio": 0.1, "augmentation_factor": 1, "noise_level": 0.05, "min_jobs": 3, "max_jobs": 20, "min_machines": 3, "max_machines": 10}, "evaluation": {"baseline_algorithms": ["SPT", "FIFO", "LPT", "Random"], "metrics": ["makespan", "success_rate", "generation_time", "decision_quality", "diversity_score"], "num_test_instances": 5, "num_runs_per_instance": 1, "timeout_seconds": 300, "generate_plots": true, "save_detailed_results": true}, "system": {"device": "cuda", "num_workers": 4, "pin_memory": true, "log_level": "INFO", "log_file": null, "tensorboard_dir": "runs", "save_checkpoints": true, "checkpoint_interval": 10, "keep_last_n_checkpoints": 3, "random_seed": 42, "deterministic": true}}