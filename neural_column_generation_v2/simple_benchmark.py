#!/usr/bin/env python3
"""
Simple Benchmark
简单基准测试 - 测试v2系统与基线方法的对比
"""

import os
import sys
import time
import torch
import numpy as np
import pandas as pd
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from config import get_config
from core.neural_model import NeuralColumnModel
from core.column_generator import ColumnGenerator
from core.schedule_evaluator import ScheduleEvaluator
from core.feature_extractor import FeatureExtractor
from training.data_generator import TrainingDataGenerator
from integration.solver_interface import NeuralColumnSolver, HeuristicSolver


def create_test_instances(num_instances=10):
    """创建测试实例"""
    print(f"🔧 Creating {num_instances} test instances...")
    
    config = get_config()
    data_generator = TrainingDataGenerator(config)
    
    # 生成不同规模的实例
    instances = []
    
    for i in range(num_instances):
        # 随机生成实例参数
        n_jobs = np.random.randint(3, 8)
        n_machines = np.random.randint(2, 5)
        
        # 生成作业长度
        job_length = [np.random.randint(2, 5) for _ in range(n_jobs)]
        n_operations = sum(job_length)
        
        # 生成处理时间矩阵
        processing_matrix = np.zeros((n_operations, n_machines), dtype=np.float32)
        
        for op_id in range(n_operations):
            for m_id in range(n_machines):
                # 随机生成处理时间（1-20）
                processing_matrix[op_id, m_id] = np.random.randint(1, 21)
        
        # 创建实例
        instance = {
            'job_length': job_length,
            'processing_matrix': torch.tensor(processing_matrix, dtype=torch.float32),
            'processing_times': processing_matrix,
            'n_jobs': n_jobs,
            'n_machines': n_machines,
            'n_operations': n_operations,
            'filename': f'test_instance_{i}.fjs',
            'instance_id': i
        }
        
        instances.append(instance)
    
    print(f"✅ Created {len(instances)} test instances")
    return instances


def run_solver_benchmark(solver, instances, solver_name):
    """运行求解器基准测试"""
    print(f"🔄 Testing {solver_name}...")
    
    results = []
    
    for i, instance in enumerate(instances):
        print(f"  Instance {i+1}/{len(instances)}: {instance['n_jobs']}J x {instance['n_machines']}M")
        
        try:
            start_time = time.time()
            result = solver.solve(instance)
            
            results.append({
                'instance_id': instance['instance_id'],
                'n_jobs': instance['n_jobs'],
                'n_machines': instance['n_machines'],
                'n_operations': instance['n_operations'],
                'makespan': result.optimal_makespan,
                'solution_time': result.solution_time,
                'status': result.status,
                'feasible': result.status in ['optimal', 'feasible'],
                'success': result.status == 'optimal'
            })
            
            print(f"    Result: makespan={result.optimal_makespan:.2f}, "
                  f"time={result.solution_time:.3f}s, status={result.status}")
            
        except Exception as e:
            print(f"    ❌ Error: {e}")
            results.append({
                'instance_id': instance['instance_id'],
                'n_jobs': instance['n_jobs'],
                'n_machines': instance['n_machines'],
                'n_operations': instance['n_operations'],
                'makespan': float('inf'),
                'solution_time': 0.0,
                'status': 'error',
                'feasible': False,
                'success': False
            })
    
    return results


def analyze_results(all_results):
    """分析结果"""
    print("\n📊 Analyzing results...")
    
    # 创建DataFrame
    comparison_data = []
    
    for solver_name, solver_results in all_results.items():
        for result in solver_results:
            result['solver'] = solver_name
            comparison_data.append(result)
    
    df = pd.DataFrame(comparison_data)
    
    # 计算统计信息
    summary_stats = []
    
    for solver in df['solver'].unique():
        subset = df[df['solver'] == solver]
        successful = subset[subset['success'] == True]
        
        stats = {
            'Solver': solver,
            'Total_Instances': len(subset),
            'Successful_Instances': len(successful),
            'Success_Rate': len(successful) / len(subset) if len(subset) > 0 else 0,
            'Avg_Makespan': successful['makespan'].mean() if len(successful) > 0 else float('inf'),
            'Std_Makespan': successful['makespan'].std() if len(successful) > 0 else 0,
            'Min_Makespan': successful['makespan'].min() if len(successful) > 0 else float('inf'),
            'Max_Makespan': successful['makespan'].max() if len(successful) > 0 else float('inf'),
            'Avg_Solution_Time': successful['solution_time'].mean() if len(successful) > 0 else 0,
            'Total_Solution_Time': subset['solution_time'].sum()
        }
        
        summary_stats.append(stats)
    
    summary_df = pd.DataFrame(summary_stats)
    
    # 打印结果
    print("\n📋 Benchmark Results:")
    print("=" * 80)
    
    for _, row in summary_df.iterrows():
        solver = row['Solver']
        print(f"\n{solver}:")
        print(f"  Success Rate: {row['Success_Rate']:.1%}")
        print(f"  Average Makespan: {row['Avg_Makespan']:.2f}")
        print(f"  Average Solution Time: {row['Avg_Solution_Time']:.3f}s")
        print(f"  Total Solution Time: {row['Total_Solution_Time']:.3f}s")
    
    # 计算改进
    if len(summary_df) > 1:
        print("\n🔄 Comparison:")
        print("-" * 40)
        
        neural_stats = summary_df[summary_df['Solver'] == 'Neural_v2']
        spt_stats = summary_df[summary_df['Solver'] == 'SPT']
        
        if len(neural_stats) > 0 and len(spt_stats) > 0:
            neural_row = neural_stats.iloc[0]
            spt_row = spt_stats.iloc[0]
            
            if neural_row['Avg_Makespan'] < float('inf') and spt_row['Avg_Makespan'] < float('inf'):
                makespan_improvement = (spt_row['Avg_Makespan'] - neural_row['Avg_Makespan']) / spt_row['Avg_Makespan'] * 100
                time_ratio = neural_row['Avg_Solution_Time'] / spt_row['Avg_Solution_Time']
                
                print(f"  Neural v2 vs SPT:")
                print(f"    Makespan improvement: {makespan_improvement:+.1f}%")
                print(f"    Time ratio: {time_ratio:.1f}x")
                print(f"    Success rate difference: {neural_row['Success_Rate'] - spt_row['Success_Rate']:+.1%}")
    
    return summary_df


def main():
    """主函数"""
    print("🚀 Simple Benchmark Test")
    print("=" * 50)
    
    try:
        # 加载配置
        config = get_config()
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建模型
        model = NeuralColumnModel(config).to(device)
        print(f"✅ Model created")
        
        # 创建测试实例
        test_instances = create_test_instances(num_instances=5)
        
        # 创建求解器
        print("\n🔧 Creating solvers...")
        
        # 神经求解器
        column_generator = ColumnGenerator(model, config)
        evaluator = ScheduleEvaluator()
        neural_solver = NeuralColumnSolver(model, column_generator, evaluator, config)
        
        # 基线求解器
        spt_solver = HeuristicSolver('SPT', config)
        lpt_solver = HeuristicSolver('LPT', config)
        
        solvers = {
            'Neural_v2': neural_solver,
            'SPT': spt_solver,
            'LPT': lpt_solver
        }
        
        # 运行基准测试
        all_results = {}
        
        for solver_name, solver in solvers.items():
            results = run_solver_benchmark(solver, test_instances, solver_name)
            all_results[solver_name] = results
        
        # 分析结果
        summary_df = analyze_results(all_results)
        
        print("\n🎉 Simple benchmark test completed!")
        
    except Exception as e:
        print(f"\n❌ Benchmark test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
