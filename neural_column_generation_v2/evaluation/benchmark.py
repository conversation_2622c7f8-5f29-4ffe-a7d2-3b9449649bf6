#!/usr/bin/env python3
"""
Benchmark Runner
基准测试运行器 - 负责运行各种基准测试
"""

import os
import time
import json
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
from pathlib import Path
import torch

from ..core.column_generator import ColumnGenerator
from ..core.schedule_evaluator import ScheduleEvaluator
from ..core.feature_extractor import FeatureExtractor
from ..core.data_parser import DataParser
from .metrics import PerformanceMetrics, PerformanceResult


class BenchmarkRunner:
    """基准测试运行器"""
    
    def __init__(self, model, config):
        self.model = model
        self.config = config
        self.device = next(model.parameters()).device
        
        # 初始化组件
        self.column_generator = ColumnGenerator(model, config)
        self.evaluator = ScheduleEvaluator()
        self.feature_extractor = FeatureExtractor()
        self.data_parser = DataParser()
        self.metrics = PerformanceMetrics()
        
        # 基线方法
        self.baseline_methods = {
            'SPT': self._run_spt_heuristic,
            'LPT': self._run_lpt_heuristic,
            'FIFO': self._run_fifo_heuristic,
            'Random': self._run_random_heuristic
        }
        
    def run_comprehensive_benchmark(self, data_dirs: List[str], 
                                   output_dir: str = "results/benchmark") -> Dict:
        """运行全面的基准测试"""
        print("🚀 Starting Comprehensive Benchmark...")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        all_results = {}
        
        for data_dir in data_dirs:
            print(f"\n📁 Processing dataset: {data_dir}")
            
            # 加载数据集
            instances = self._load_dataset(data_dir)
            if not instances:
                print(f"❌ No instances found in {data_dir}")
                continue
            
            dataset_name = os.path.basename(data_dir)
            dataset_results = self._run_dataset_benchmark(instances, dataset_name)
            all_results[dataset_name] = dataset_results
            
            # 保存中间结果
            self._save_results(dataset_results, 
                             os.path.join(output_dir, f"{dataset_name}_results.json"))
        
        # 生成综合报告
        comprehensive_report = self._generate_comprehensive_report(all_results)
        
        # 保存最终结果
        final_results = {
            'dataset_results': all_results,
            'comprehensive_report': comprehensive_report,
            'config': self.config.__dict__ if hasattr(self.config, '__dict__') else str(self.config),
            'timestamp': time.strftime('%Y-%m-%d %H:%M:%S')
        }
        
        self._save_results(final_results, os.path.join(output_dir, "comprehensive_results.json"))
        
        print(f"\n✅ Benchmark completed! Results saved to {output_dir}")
        return final_results
    
    def _load_dataset(self, data_dir: str) -> List[Dict]:
        """加载数据集"""
        instances = []
        
        if not os.path.exists(data_dir):
            return instances
        
        # 支持多种文件格式
        file_extensions = ['.fjs', '.txt', '.dat']
        
        for file_ext in file_extensions:
            pattern = f"*{file_ext}"
            files = list(Path(data_dir).glob(pattern))
            
            for file_path in files:
                try:
                    instance_data = self.data_parser.parse_file(str(file_path))
                    if instance_data:
                        instance_data['filename'] = file_path.name
                        instance_data['filepath'] = str(file_path)
                        instances.append(instance_data)
                except Exception as e:
                    print(f"⚠️ Error loading {file_path}: {e}")
        
        return instances
    
    def _run_dataset_benchmark(self, instances: List[Dict], dataset_name: str) -> Dict:
        """运行单个数据集的基准测试"""
        print(f"  Testing {len(instances)} instances...")
        
        results = {
            'dataset_name': dataset_name,
            'num_instances': len(instances),
            'neural_results': [],
            'baseline_results': {},
            'comparison_results': {},
            'statistics': {}
        }
        
        # 初始化基线结果
        for method_name in self.baseline_methods:
            results['baseline_results'][method_name] = []
        
        # 测试每个实例
        for i, instance in enumerate(instances):
            print(f"    Instance {i+1}/{len(instances)}: {instance.get('filename', 'unknown')}")
            
            # 测试神经方法
            neural_result = self._test_neural_method(instance)
            results['neural_results'].append(neural_result)
            
            # 测试基线方法
            for method_name, method_func in self.baseline_methods.items():
                baseline_result = method_func(instance)
                results['baseline_results'][method_name].append(baseline_result)
        
        # 计算统计信息
        results['statistics'] = self._calculate_dataset_statistics(results)
        
        # 生成比较结果
        results['comparison_results'] = self._generate_comparison_results(results)
        
        return results
    
    def _test_neural_method(self, instance: Dict) -> PerformanceResult:
        """测试神经方法"""
        start_time = time.time()
        
        try:
            # 提取特征
            features = self.feature_extractor.extract_features(instance)
            
            # 生成列
            columns = self.column_generator.generate_columns(
                features, 
                instance['job_length'],
                num_columns=self.config.model.num_columns
            )
            
            # 评估列
            evaluated_columns = []
            for column in columns:
                evaluated_column = self.evaluator.evaluate_column(column, instance)
                evaluated_columns.append(evaluated_column)
            
            solution_time = time.time() - start_time
            
            # 计算性能指标
            result = self.metrics.evaluate_single_instance(
                evaluated_columns, instance, solution_time
            )
            
            return result
            
        except Exception as e:
            print(f"    ❌ Neural method failed: {e}")
            return PerformanceResult(
                makespan=float('inf'),
                solution_time=time.time() - start_time,
                feasible=False,
                success=False
            )
    
    def _run_spt_heuristic(self, instance: Dict) -> PerformanceResult:
        """运行SPT启发式"""
        return self._run_priority_heuristic(instance, 'SPT')
    
    def _run_lpt_heuristic(self, instance: Dict) -> PerformanceResult:
        """运行LPT启发式"""
        return self._run_priority_heuristic(instance, 'LPT')
    
    def _run_fifo_heuristic(self, instance: Dict) -> PerformanceResult:
        """运行FIFO启发式"""
        return self._run_priority_heuristic(instance, 'FIFO')
    
    def _run_random_heuristic(self, instance: Dict) -> PerformanceResult:
        """运行随机启发式"""
        return self._run_priority_heuristic(instance, 'Random')
    
    def _run_priority_heuristic(self, instance: Dict, method: str) -> PerformanceResult:
        """运行优先级启发式方法"""
        start_time = time.time()
        
        try:
            job_length = instance['job_length']
            processing_matrix = instance['processing_matrix']
            
            if isinstance(processing_matrix, torch.Tensor):
                processing_matrix = processing_matrix.cpu().numpy()
            
            # 生成启发式调度
            schedule = self._generate_heuristic_schedule(
                processing_matrix, job_length, method
            )
            
            if not schedule:
                return PerformanceResult(
                    makespan=float('inf'),
                    solution_time=time.time() - start_time,
                    feasible=False,
                    success=False
                )
            
            # 评估调度
            makespan = self._calculate_makespan(schedule, processing_matrix, job_length)
            
            return PerformanceResult(
                makespan=makespan,
                solution_time=time.time() - start_time,
                feasible=makespan < float('inf'),
                success=makespan < float('inf')
            )
            
        except Exception as e:
            print(f"    ⚠️ {method} heuristic failed: {e}")
            return PerformanceResult(
                makespan=float('inf'),
                solution_time=time.time() - start_time,
                feasible=False,
                success=False
            )
    
    def _generate_heuristic_schedule(self, processing_matrix: np.ndarray, 
                                   job_length: List[int], method: str) -> List[Tuple[int, int]]:
        """生成启发式调度"""
        n_jobs = len(job_length)
        n_operations = processing_matrix.shape[0]
        
        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_length):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops
        
        schedule = []
        job_progress = [0] * n_jobs
        scheduled_ops = set()
        
        # 创建操作优先级列表
        operation_priorities = self._calculate_operation_priorities(
            processing_matrix, job_length, method
        )
        
        # 按优先级调度
        while len(scheduled_ops) < n_operations:
            # 获取可调度操作
            available_ops = []
            for job_id, operations in enumerate(job_operations):
                if job_progress[job_id] < len(operations):
                    next_op = operations[job_progress[job_id]]
                    if next_op not in scheduled_ops:
                        available_ops.append(next_op)
            
            if not available_ops:
                break
            
            # 根据优先级选择操作
            if method == 'Random':
                selected_op = np.random.choice(available_ops)
            else:
                selected_op = min(available_ops, key=lambda op: operation_priorities.get(op, float('inf')))
            
            # 选择最佳机器
            best_machine = self._select_best_machine(selected_op, processing_matrix)
            
            if best_machine is not None:
                schedule.append((selected_op, best_machine))
                scheduled_ops.add(selected_op)
                
                # 更新作业进度
                for job_id, ops in enumerate(job_operations):
                    if selected_op in ops:
                        job_progress[job_id] += 1
                        break
        
        return schedule if len(scheduled_ops) == n_operations else []

    def _calculate_operation_priorities(self, processing_matrix: np.ndarray,
                                      job_length: List[int], method: str) -> Dict[int, float]:
        """计算操作优先级"""
        priorities = {}

        for op_id in range(processing_matrix.shape[0]):
            valid_times = [processing_matrix[op_id, m] for m in range(processing_matrix.shape[1])
                          if processing_matrix[op_id, m] > 0]

            if not valid_times:
                priorities[op_id] = float('inf')
                continue

            if method == 'SPT':
                priorities[op_id] = min(valid_times)
            elif method == 'LPT':
                priorities[op_id] = -max(valid_times)  # 负值用于最大优先
            elif method == 'FIFO':
                priorities[op_id] = op_id  # 按操作ID顺序
            else:  # Random
                priorities[op_id] = np.random.random()

        return priorities

    def _select_best_machine(self, operation_id: int, processing_matrix: np.ndarray) -> Optional[int]:
        """选择最佳机器"""
        valid_machines = []
        for m in range(processing_matrix.shape[1]):
            if processing_matrix[operation_id, m] > 0:
                valid_machines.append((processing_matrix[operation_id, m], m))

        if not valid_machines:
            return None

        # 选择处理时间最短的机器
        valid_machines.sort()
        return valid_machines[0][1]

    def _calculate_makespan(self, schedule: List[Tuple[int, int]],
                          processing_matrix: np.ndarray, job_length: List[int]) -> float:
        """计算makespan"""
        if not schedule:
            return float('inf')

        try:
            # 构建作业-操作映射
            job_operations = []
            op_id = 0
            for job_id, n_ops in enumerate(job_length):
                job_operations.append(list(range(op_id, op_id + n_ops)))
                op_id += n_ops

            # 初始化时间跟踪
            n_machines = processing_matrix.shape[1]
            n_jobs = len(job_length)

            machine_available_time = [0.0] * n_machines
            job_completion_time = [0.0] * n_jobs

            # 按调度顺序处理操作
            for op_id, machine_id in schedule:
                # 找到操作所属的作业
                job_id = None
                for j, ops in enumerate(job_operations):
                    if op_id in ops:
                        job_id = j
                        break

                if job_id is None:
                    continue

                # 计算开始时间
                proc_time = processing_matrix[op_id, machine_id]
                start_time = max(machine_available_time[machine_id], job_completion_time[job_id])
                completion_time = start_time + proc_time

                # 更新时间
                machine_available_time[machine_id] = completion_time
                job_completion_time[job_id] = completion_time

            return max(job_completion_time)

        except Exception:
            return float('inf')

    def _calculate_dataset_statistics(self, results: Dict) -> Dict:
        """计算数据集统计信息"""
        neural_stats = self.metrics.evaluate_batch_results(results['neural_results'])

        baseline_stats = {}
        for method_name, method_results in results['baseline_results'].items():
            baseline_stats[method_name] = self.metrics.evaluate_batch_results(method_results)

        return {
            'neural': neural_stats,
            'baseline': baseline_stats
        }

    def _generate_comparison_results(self, results: Dict) -> Dict:
        """生成比较结果"""
        comparisons = {}

        for method_name, method_results in results['baseline_results'].items():
            comparison = self.metrics.compare_with_baseline(
                results['neural_results'], method_results, method_name
            )
            comparisons[method_name] = comparison

        return comparisons

    def _generate_comprehensive_report(self, all_results: Dict) -> Dict:
        """生成综合报告"""
        report = {
            'summary': {},
            'dataset_summaries': {},
            'overall_comparison': {}
        }

        # 汇总所有数据集的结果
        all_neural_results = []
        all_baseline_results = {method: [] for method in self.baseline_methods}

        for dataset_name, dataset_results in all_results.items():
            all_neural_results.extend(dataset_results['neural_results'])

            for method_name, method_results in dataset_results['baseline_results'].items():
                all_baseline_results[method_name].extend(method_results)

            # 数据集摘要
            report['dataset_summaries'][dataset_name] = {
                'num_instances': dataset_results['num_instances'],
                'neural_success_rate': dataset_results['statistics']['neural']['success_rate'],
                'best_baseline': self._find_best_baseline(dataset_results['statistics']['baseline'])
            }

        # 总体统计
        report['summary'] = self.metrics.evaluate_batch_results(all_neural_results)

        # 总体比较
        for method_name, method_results in all_baseline_results.items():
            comparison = self.metrics.compare_with_baseline(
                all_neural_results, method_results, method_name
            )
            report['overall_comparison'][method_name] = comparison

        return report

    def _find_best_baseline(self, baseline_stats: Dict) -> str:
        """找到最佳基线方法"""
        best_method = None
        best_makespan = float('inf')

        for method_name, stats in baseline_stats.items():
            if stats.get('success_rate', 0) > 0:
                avg_makespan = stats.get('avg_makespan', float('inf'))
                if avg_makespan < best_makespan:
                    best_makespan = avg_makespan
                    best_method = method_name

        return best_method or 'None'

    def _save_results(self, results: Dict, filepath: str):
        """保存结果到文件"""
        try:
            # 转换不可序列化的对象
            serializable_results = self._make_serializable(results)

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(serializable_results, f, indent=2, ensure_ascii=False)

        except Exception as e:
            print(f"⚠️ Error saving results to {filepath}: {e}")

    def _make_serializable(self, obj):
        """使对象可序列化"""
        if isinstance(obj, dict):
            return {k: self._make_serializable(v) for k, v in obj.items()}
        elif isinstance(obj, list):
            return [self._make_serializable(item) for item in obj]
        elif isinstance(obj, (np.integer, np.floating)):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif hasattr(obj, '__dict__'):
            return self._make_serializable(obj.__dict__)
        else:
            return obj
