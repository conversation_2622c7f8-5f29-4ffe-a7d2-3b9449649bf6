#!/usr/bin/env python3
"""
Performance Metrics
性能指标计算模块
"""

import numpy as np
import torch
from typing import List, Dict, Tuple, Optional
from dataclasses import dataclass
from ..core.neural_model import ScheduleColumn


@dataclass
class PerformanceResult:
    """性能评估结果"""
    makespan: float
    solution_time: float
    feasible: bool
    success: bool
    iterations: int = 0
    columns_generated: int = 0
    decision_quality: float = 0.0
    resource_utilization: float = 0.0
    gap_to_optimal: float = float('inf')


class PerformanceMetrics:
    """性能指标计算器"""
    
    def __init__(self):
        self.baseline_results = {}
        
    def evaluate_single_instance(self, columns: List[ScheduleColumn], 
                                instance_data: Dict, 
                                solution_time: float = 0.0) -> PerformanceResult:
        """评估单个实例的性能"""
        if not columns:
            return PerformanceResult(
                makespan=float('inf'),
                solution_time=solution_time,
                feasible=False,
                success=False
            )
        
        # 找到最佳可行解
        feasible_columns = [col for col in columns if col.feasible and col.makespan > 0]
        
        if not feasible_columns:
            return PerformanceResult(
                makespan=float('inf'),
                solution_time=solution_time,
                feasible=False,
                success=False,
                columns_generated=len(columns)
            )
        
        best_column = min(feasible_columns, key=lambda x: x.makespan)
        
        # 计算资源利用率
        resource_utilization = self._calculate_resource_utilization(
            best_column, instance_data
        )
        
        # 计算平均决策质量
        avg_decision_quality = np.mean([col.decision_quality for col in feasible_columns])
        
        return PerformanceResult(
            makespan=best_column.makespan,
            solution_time=solution_time,
            feasible=True,
            success=True,
            columns_generated=len(columns),
            decision_quality=avg_decision_quality,
            resource_utilization=resource_utilization
        )
    
    def evaluate_batch_results(self, results: List[PerformanceResult]) -> Dict:
        """评估批量结果"""
        if not results:
            return {}
        
        successful_results = [r for r in results if r.success]
        
        if not successful_results:
            return {
                'success_rate': 0.0,
                'avg_makespan': float('inf'),
                'avg_solution_time': 0.0,
                'total_instances': len(results)
            }
        
        return {
            'success_rate': len(successful_results) / len(results),
            'avg_makespan': np.mean([r.makespan for r in successful_results]),
            'std_makespan': np.std([r.makespan for r in successful_results]),
            'min_makespan': min(r.makespan for r in successful_results),
            'max_makespan': max(r.makespan for r in successful_results),
            'avg_solution_time': np.mean([r.solution_time for r in successful_results]),
            'std_solution_time': np.std([r.solution_time for r in successful_results]),
            'avg_decision_quality': np.mean([r.decision_quality for r in successful_results]),
            'avg_resource_utilization': np.mean([r.resource_utilization for r in successful_results]),
            'avg_columns_generated': np.mean([r.columns_generated for r in results]),
            'total_instances': len(results),
            'successful_instances': len(successful_results)
        }
    
    def compare_with_baseline(self, neural_results: List[PerformanceResult],
                             baseline_results: List[PerformanceResult],
                             baseline_name: str = "Baseline") -> Dict:
        """与基线方法比较"""
        neural_stats = self.evaluate_batch_results(neural_results)
        baseline_stats = self.evaluate_batch_results(baseline_results)
        
        if neural_stats.get('success_rate', 0) == 0 or baseline_stats.get('success_rate', 0) == 0:
            return {
                'comparison_valid': False,
                'neural_stats': neural_stats,
                'baseline_stats': baseline_stats
            }
        
        # 计算改进百分比
        makespan_improvement = (
            (baseline_stats['avg_makespan'] - neural_stats['avg_makespan']) / 
            baseline_stats['avg_makespan'] * 100
        )
        
        time_improvement = (
            (baseline_stats['avg_solution_time'] - neural_stats['avg_solution_time']) / 
            baseline_stats['avg_solution_time'] * 100
        )
        
        return {
            'comparison_valid': True,
            'neural_stats': neural_stats,
            'baseline_stats': baseline_stats,
            'makespan_improvement_pct': makespan_improvement,
            'time_improvement_pct': time_improvement,
            'success_rate_diff': neural_stats['success_rate'] - baseline_stats['success_rate'],
            'decision_quality_diff': neural_stats.get('avg_decision_quality', 0) - baseline_stats.get('avg_decision_quality', 0),
            'baseline_name': baseline_name
        }
    
    def _calculate_resource_utilization(self, column: ScheduleColumn, 
                                       instance_data: Dict) -> float:
        """计算资源利用率"""
        if not column.schedule or column.makespan <= 0:
            return 0.0
        
        try:
            processing_matrix = instance_data.get('processing_matrix')
            if processing_matrix is None:
                return 0.0
            
            if isinstance(processing_matrix, torch.Tensor):
                processing_matrix = processing_matrix.cpu().numpy()
            
            n_machines = processing_matrix.shape[1]
            machine_times = [0.0] * n_machines
            
            # 计算每台机器的总工作时间
            for op_id, machine_id in column.schedule:
                if (op_id < processing_matrix.shape[0] and 
                    machine_id < processing_matrix.shape[1]):
                    machine_times[machine_id] += processing_matrix[op_id, machine_id]
            
            # 计算利用率
            total_machine_time = sum(machine_times)
            max_possible_time = column.makespan * n_machines
            
            return total_machine_time / max_possible_time if max_possible_time > 0 else 0.0
            
        except Exception:
            return 0.0
    
    def calculate_statistical_significance(self, results1: List[float], 
                                         results2: List[float]) -> Dict:
        """计算统计显著性"""
        try:
            from scipy import stats
            
            if len(results1) < 2 or len(results2) < 2:
                return {'valid': False, 'reason': 'Insufficient data'}
            
            # t检验
            t_stat, p_value = stats.ttest_ind(results1, results2)
            
            # 效应大小 (Cohen's d)
            pooled_std = np.sqrt(((len(results1) - 1) * np.var(results1, ddof=1) + 
                                 (len(results2) - 1) * np.var(results2, ddof=1)) / 
                                (len(results1) + len(results2) - 2))
            
            cohens_d = (np.mean(results1) - np.mean(results2)) / pooled_std if pooled_std > 0 else 0
            
            return {
                'valid': True,
                't_statistic': t_stat,
                'p_value': p_value,
                'significant': p_value < 0.05,
                'cohens_d': cohens_d,
                'effect_size': self._interpret_effect_size(abs(cohens_d))
            }
            
        except ImportError:
            return {'valid': False, 'reason': 'scipy not available'}
        except Exception as e:
            return {'valid': False, 'reason': str(e)}
    
    def _interpret_effect_size(self, cohens_d: float) -> str:
        """解释效应大小"""
        if cohens_d < 0.2:
            return 'negligible'
        elif cohens_d < 0.5:
            return 'small'
        elif cohens_d < 0.8:
            return 'medium'
        else:
            return 'large'
    
    def generate_performance_report(self, results: Dict, 
                                   comparison_results: Dict = None) -> str:
        """生成性能报告"""
        report = []
        report.append("=" * 60)
        report.append("Neural Column Generation Performance Report")
        report.append("=" * 60)
        
        # 基本统计
        report.append(f"\n📊 Basic Statistics:")
        report.append(f"  Total Instances: {results.get('total_instances', 0)}")
        report.append(f"  Successful Instances: {results.get('successful_instances', 0)}")
        report.append(f"  Success Rate: {results.get('success_rate', 0):.1%}")
        
        # 性能指标
        if results.get('success_rate', 0) > 0:
            report.append(f"\n🎯 Performance Metrics:")
            report.append(f"  Average Makespan: {results.get('avg_makespan', 0):.2f}")
            report.append(f"  Makespan Std Dev: {results.get('std_makespan', 0):.2f}")
            report.append(f"  Best Makespan: {results.get('min_makespan', 0):.2f}")
            report.append(f"  Worst Makespan: {results.get('max_makespan', 0):.2f}")
            report.append(f"  Average Solution Time: {results.get('avg_solution_time', 0):.3f}s")
            report.append(f"  Average Decision Quality: {results.get('avg_decision_quality', 0):.3f}")
            report.append(f"  Average Resource Utilization: {results.get('avg_resource_utilization', 0):.1%}")
        
        # 比较结果
        if comparison_results and comparison_results.get('comparison_valid'):
            report.append(f"\n🔄 Comparison with {comparison_results.get('baseline_name', 'Baseline')}:")
            report.append(f"  Makespan Improvement: {comparison_results.get('makespan_improvement_pct', 0):+.1f}%")
            report.append(f"  Time Improvement: {comparison_results.get('time_improvement_pct', 0):+.1f}%")
            report.append(f"  Success Rate Difference: {comparison_results.get('success_rate_diff', 0):+.1%}")
        
        report.append("\n" + "=" * 60)
        
        return "\n".join(report)
