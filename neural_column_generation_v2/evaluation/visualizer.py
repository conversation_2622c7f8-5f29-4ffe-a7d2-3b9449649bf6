#!/usr/bin/env python3
"""
Result Visualizer
结果可视化模块 - 负责生成各种图表和可视化报告
"""

import os
import numpy as np
import pandas as pd
from typing import List, Dict, Tuple, Optional, Any
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path

from .metrics import PerformanceResult


class ResultVisualizer:
    """结果可视化器"""
    
    def __init__(self, style='seaborn-v0_8', figsize=(12, 8)):
        """初始化可视化器"""
        try:
            plt.style.use(style)
        except:
            plt.style.use('default')
        
        self.figsize = figsize
        self.colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83']
        
        # 设置中文字体
        plt.rcParams['font.sans-serif'] = ['SimHei', 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
    
    def create_comprehensive_report(self, results: Dict, output_dir: str):
        """创建综合可视化报告"""
        print("📊 Generating comprehensive visualization report...")
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成各种图表
        self._plot_performance_comparison(results, output_dir)
        self._plot_success_rates(results, output_dir)
        self._plot_makespan_distribution(results, output_dir)
        self._plot_solution_time_analysis(results, output_dir)
        self._plot_dataset_comparison(results, output_dir)
        self._plot_improvement_analysis(results, output_dir)
        
        # 生成HTML报告
        self._generate_html_report(results, output_dir)
        
        print(f"✅ Visualization report saved to {output_dir}")
    
    def _plot_performance_comparison(self, results: Dict, output_dir: str):
        """绘制性能比较图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Neural Column Generation Performance Comparison', fontsize=16, fontweight='bold')
        
        # 提取数据
        methods = ['Neural'] + list(results.get('overall_comparison', {}).keys())
        makespans = []
        solution_times = []
        success_rates = []
        
        # 神经方法数据
        neural_stats = results.get('summary', {})
        makespans.append(neural_stats.get('avg_makespan', 0))
        solution_times.append(neural_stats.get('avg_solution_time', 0))
        success_rates.append(neural_stats.get('success_rate', 0))
        
        # 基线方法数据
        for method in methods[1:]:
            comparison = results.get('overall_comparison', {}).get(method, {})
            baseline_stats = comparison.get('baseline_stats', {})
            makespans.append(baseline_stats.get('avg_makespan', 0))
            solution_times.append(baseline_stats.get('avg_solution_time', 0))
            success_rates.append(baseline_stats.get('success_rate', 0))
        
        # 1. Makespan比较
        axes[0, 0].bar(methods, makespans, color=self.colors[:len(methods)])
        axes[0, 0].set_title('Average Makespan Comparison')
        axes[0, 0].set_ylabel('Makespan')
        axes[0, 0].tick_params(axis='x', rotation=45)
        
        # 2. 求解时间比较
        axes[0, 1].bar(methods, solution_times, color=self.colors[:len(methods)])
        axes[0, 1].set_title('Average Solution Time Comparison')
        axes[0, 1].set_ylabel('Time (seconds)')
        axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. 成功率比较
        axes[1, 0].bar(methods, [rate * 100 for rate in success_rates], color=self.colors[:len(methods)])
        axes[1, 0].set_title('Success Rate Comparison')
        axes[1, 0].set_ylabel('Success Rate (%)')
        axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 4. 改进百分比
        improvements = []
        for method in methods[1:]:
            comparison = results.get('overall_comparison', {}).get(method, {})
            improvement = comparison.get('makespan_improvement_pct', 0)
            improvements.append(improvement)
        
        if improvements:
            axes[1, 1].bar(methods[1:], improvements, color=self.colors[1:len(methods)])
            axes[1, 1].set_title('Makespan Improvement vs Baselines')
            axes[1, 1].set_ylabel('Improvement (%)')
            axes[1, 1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
            axes[1, 1].tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'performance_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_success_rates(self, results: Dict, output_dir: str):
        """绘制成功率分析图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 按数据集的成功率
        dataset_summaries = results.get('dataset_summaries', {})
        if dataset_summaries:
            datasets = list(dataset_summaries.keys())
            neural_rates = [summary.get('neural_success_rate', 0) * 100 
                           for summary in dataset_summaries.values()]
            
            ax1.bar(datasets, neural_rates, color=self.colors[0])
            ax1.set_title('Neural Method Success Rate by Dataset')
            ax1.set_ylabel('Success Rate (%)')
            ax1.tick_params(axis='x', rotation=45)
            ax1.set_ylim(0, 105)
        
        # 总体成功率比较
        overall_comparison = results.get('overall_comparison', {})
        if overall_comparison:
            methods = ['Neural'] + list(overall_comparison.keys())
            success_rates = []
            
            # 神经方法
            neural_stats = results.get('summary', {})
            success_rates.append(neural_stats.get('success_rate', 0) * 100)
            
            # 基线方法
            for method in methods[1:]:
                baseline_stats = overall_comparison[method].get('baseline_stats', {})
                success_rates.append(baseline_stats.get('success_rate', 0) * 100)
            
            ax2.bar(methods, success_rates, color=self.colors[:len(methods)])
            ax2.set_title('Overall Success Rate Comparison')
            ax2.set_ylabel('Success Rate (%)')
            ax2.tick_params(axis='x', rotation=45)
            ax2.set_ylim(0, 105)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'success_rates.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_makespan_distribution(self, results: Dict, output_dir: str):
        """绘制makespan分布图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Makespan Distribution Analysis', fontsize=16, fontweight='bold')
        
        # 这里需要原始数据，简化处理
        neural_stats = results.get('summary', {})
        
        # 1. 神经方法统计
        if neural_stats:
            stats_data = [
                neural_stats.get('avg_makespan', 0),
                neural_stats.get('min_makespan', 0),
                neural_stats.get('max_makespan', 0)
            ]
            labels = ['Average', 'Best', 'Worst']
            
            axes[0, 0].bar(labels, stats_data, color=self.colors[:3])
            axes[0, 0].set_title('Neural Method Makespan Statistics')
            axes[0, 0].set_ylabel('Makespan')
        
        # 2. 与基线比较的箱线图（简化版）
        comparison_data = []
        method_names = []
        
        # 神经方法
        comparison_data.append([neural_stats.get('avg_makespan', 0)])
        method_names.append('Neural')
        
        # 基线方法
        for method, comparison in results.get('overall_comparison', {}).items():
            baseline_stats = comparison.get('baseline_stats', {})
            comparison_data.append([baseline_stats.get('avg_makespan', 0)])
            method_names.append(method)
        
        if len(comparison_data) > 1:
            axes[0, 1].boxplot(comparison_data, labels=method_names)
            axes[0, 1].set_title('Makespan Comparison (Simplified)')
            axes[0, 1].set_ylabel('Makespan')
            axes[0, 1].tick_params(axis='x', rotation=45)
        
        # 3. 改进分析
        improvements = []
        baseline_methods = []
        
        for method, comparison in results.get('overall_comparison', {}).items():
            if comparison.get('comparison_valid', False):
                improvement = comparison.get('makespan_improvement_pct', 0)
                improvements.append(improvement)
                baseline_methods.append(method)
        
        if improvements:
            colors = ['green' if imp > 0 else 'red' for imp in improvements]
            axes[1, 0].bar(baseline_methods, improvements, color=colors)
            axes[1, 0].set_title('Makespan Improvement vs Baselines')
            axes[1, 0].set_ylabel('Improvement (%)')
            axes[1, 0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
            axes[1, 0].tick_params(axis='x', rotation=45)
        
        # 4. 数据集性能对比
        dataset_summaries = results.get('dataset_summaries', {})
        if dataset_summaries:
            datasets = list(dataset_summaries.keys())
            success_rates = [summary.get('neural_success_rate', 0) * 100 
                           for summary in dataset_summaries.values()]
            
            axes[1, 1].plot(datasets, success_rates, marker='o', linewidth=2, markersize=8)
            axes[1, 1].set_title('Success Rate Trend Across Datasets')
            axes[1, 1].set_ylabel('Success Rate (%)')
            axes[1, 1].tick_params(axis='x', rotation=45)
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'makespan_distribution.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def _plot_solution_time_analysis(self, results: Dict, output_dir: str):
        """绘制求解时间分析图"""
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
        
        # 求解时间比较
        methods = ['Neural']
        times = []
        
        neural_stats = results.get('summary', {})
        times.append(neural_stats.get('avg_solution_time', 0))
        
        for method, comparison in results.get('overall_comparison', {}).items():
            baseline_stats = comparison.get('baseline_stats', {})
            methods.append(method)
            times.append(baseline_stats.get('avg_solution_time', 0))
        
        ax1.bar(methods, times, color=self.colors[:len(methods)])
        ax1.set_title('Average Solution Time Comparison')
        ax1.set_ylabel('Time (seconds)')
        ax1.tick_params(axis='x', rotation=45)
        
        # 时间改进分析
        time_improvements = []
        baseline_methods = []
        
        for method, comparison in results.get('overall_comparison', {}).items():
            if comparison.get('comparison_valid', False):
                improvement = comparison.get('time_improvement_pct', 0)
                time_improvements.append(improvement)
                baseline_methods.append(method)
        
        if time_improvements:
            colors = ['green' if imp > 0 else 'red' for imp in time_improvements]
            ax2.bar(baseline_methods, time_improvements, color=colors)
            ax2.set_title('Solution Time Improvement vs Baselines')
            ax2.set_ylabel('Improvement (%)')
            ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
            ax2.tick_params(axis='x', rotation=45)
        
        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'solution_time_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_dataset_comparison(self, results: Dict, output_dir: str):
        """绘制数据集比较图"""
        dataset_summaries = results.get('dataset_summaries', {})
        if not dataset_summaries:
            return

        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))

        datasets = list(dataset_summaries.keys())
        neural_rates = [summary.get('neural_success_rate', 0) * 100
                       for summary in dataset_summaries.values()]
        best_baselines = [summary.get('best_baseline', 'None')
                         for summary in dataset_summaries.values()]

        # 成功率对比
        ax1.bar(datasets, neural_rates, color=self.colors[0], alpha=0.7)
        ax1.set_title('Neural Method Performance by Dataset')
        ax1.set_ylabel('Success Rate (%)')
        ax1.tick_params(axis='x', rotation=45)
        ax1.set_ylim(0, 105)

        # 添加数值标签
        for i, rate in enumerate(neural_rates):
            ax1.text(i, rate + 1, f'{rate:.1f}%', ha='center', va='bottom')

        # 最佳基线方法分布
        baseline_counts = {}
        for baseline in best_baselines:
            baseline_counts[baseline] = baseline_counts.get(baseline, 0) + 1

        if baseline_counts:
            ax2.pie(baseline_counts.values(), labels=baseline_counts.keys(),
                   autopct='%1.1f%%', startangle=90, colors=self.colors)
            ax2.set_title('Best Baseline Method Distribution')

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'dataset_comparison.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _plot_improvement_analysis(self, results: Dict, output_dir: str):
        """绘制改进分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('Improvement Analysis', fontsize=16, fontweight='bold')

        overall_comparison = results.get('overall_comparison', {})

        # 1. Makespan改进
        makespan_improvements = []
        methods = []

        for method, comparison in overall_comparison.items():
            if comparison.get('comparison_valid', False):
                improvement = comparison.get('makespan_improvement_pct', 0)
                makespan_improvements.append(improvement)
                methods.append(method)

        if makespan_improvements:
            colors = ['green' if imp > 0 else 'red' for imp in makespan_improvements]
            axes[0, 0].bar(methods, makespan_improvements, color=colors)
            axes[0, 0].set_title('Makespan Improvement (%)')
            axes[0, 0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
            axes[0, 0].tick_params(axis='x', rotation=45)

        # 2. 时间改进
        time_improvements = []
        time_methods = []

        for method, comparison in overall_comparison.items():
            if comparison.get('comparison_valid', False):
                improvement = comparison.get('time_improvement_pct', 0)
                time_improvements.append(improvement)
                time_methods.append(method)

        if time_improvements:
            colors = ['green' if imp > 0 else 'red' for imp in time_improvements]
            axes[0, 1].bar(time_methods, time_improvements, color=colors)
            axes[0, 1].set_title('Solution Time Improvement (%)')
            axes[0, 1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
            axes[0, 1].tick_params(axis='x', rotation=45)

        # 3. 成功率差异
        success_rate_diffs = []
        sr_methods = []

        for method, comparison in overall_comparison.items():
            if comparison.get('comparison_valid', False):
                diff = comparison.get('success_rate_diff', 0) * 100
                success_rate_diffs.append(diff)
                sr_methods.append(method)

        if success_rate_diffs:
            colors = ['green' if diff > 0 else 'red' for diff in success_rate_diffs]
            axes[1, 0].bar(sr_methods, success_rate_diffs, color=colors)
            axes[1, 0].set_title('Success Rate Difference (%)')
            axes[1, 0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
            axes[1, 0].tick_params(axis='x', rotation=45)

        # 4. 综合改进雷达图（简化版）
        if makespan_improvements and time_improvements:
            # 创建雷达图数据
            categories = ['Makespan', 'Solution Time', 'Success Rate']

            # 选择第一个基线方法作为示例
            if methods:
                first_method = methods[0]
                comparison = overall_comparison[first_method]

                values = [
                    comparison.get('makespan_improvement_pct', 0),
                    comparison.get('time_improvement_pct', 0),
                    comparison.get('success_rate_diff', 0) * 100
                ]

                # 简化的条形图代替雷达图
                axes[1, 1].bar(categories, values, color=self.colors[:3])
                axes[1, 1].set_title(f'Improvement vs {first_method}')
                axes[1, 1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
                axes[1, 1].tick_params(axis='x', rotation=45)

        plt.tight_layout()
        plt.savefig(os.path.join(output_dir, 'improvement_analysis.png'), dpi=300, bbox_inches='tight')
        plt.close()

    def _generate_html_report(self, results: Dict, output_dir: str):
        """生成HTML报告"""
        html_content = self._create_html_template(results)

        html_path = os.path.join(output_dir, 'benchmark_report.html')
        with open(html_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"📄 HTML report saved to {html_path}")

    def _create_html_template(self, results: Dict) -> str:
        """创建HTML模板"""
        neural_stats = results.get('summary', {})

        html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Neural Column Generation Benchmark Report</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }}
                .container {{ max-width: 1200px; margin: 0 auto; background-color: white; padding: 20px; border-radius: 10px; box-shadow: 0 0 10px rgba(0,0,0,0.1); }}
                h1 {{ color: #2E86AB; text-align: center; }}
                h2 {{ color: #A23B72; border-bottom: 2px solid #A23B72; padding-bottom: 5px; }}
                .metric-card {{ background-color: #f8f9fa; padding: 15px; margin: 10px 0; border-radius: 5px; border-left: 4px solid #2E86AB; }}
                .metric-value {{ font-size: 24px; font-weight: bold; color: #2E86AB; }}
                .metric-label {{ font-size: 14px; color: #666; }}
                .comparison-table {{ width: 100%; border-collapse: collapse; margin: 20px 0; }}
                .comparison-table th, .comparison-table td {{ border: 1px solid #ddd; padding: 8px; text-align: center; }}
                .comparison-table th {{ background-color: #2E86AB; color: white; }}
                .improvement {{ color: green; font-weight: bold; }}
                .degradation {{ color: red; font-weight: bold; }}
                .image-gallery {{ display: grid; grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)); gap: 20px; margin: 20px 0; }}
                .image-card {{ text-align: center; }}
                .image-card img {{ max-width: 100%; height: auto; border-radius: 5px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🚀 Neural Column Generation Benchmark Report</h1>

                <h2>📊 Executive Summary</h2>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                    <div class="metric-card">
                        <div class="metric-value">{neural_stats.get('success_rate', 0):.1%}</div>
                        <div class="metric-label">Success Rate</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{neural_stats.get('avg_makespan', 0):.2f}</div>
                        <div class="metric-label">Average Makespan</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{neural_stats.get('avg_solution_time', 0):.3f}s</div>
                        <div class="metric-label">Average Solution Time</div>
                    </div>
                    <div class="metric-card">
                        <div class="metric-value">{neural_stats.get('total_instances', 0)}</div>
                        <div class="metric-label">Total Instances</div>
                    </div>
                </div>
        """

        # 添加比较表格
        html += self._create_comparison_table(results)

        # 添加图片画廊
        html += """
                <h2>📈 Performance Visualizations</h2>
                <div class="image-gallery">
                    <div class="image-card">
                        <h3>Performance Comparison</h3>
                        <img src="performance_comparison.png" alt="Performance Comparison">
                    </div>
                    <div class="image-card">
                        <h3>Success Rates</h3>
                        <img src="success_rates.png" alt="Success Rates">
                    </div>
                    <div class="image-card">
                        <h3>Makespan Distribution</h3>
                        <img src="makespan_distribution.png" alt="Makespan Distribution">
                    </div>
                    <div class="image-card">
                        <h3>Solution Time Analysis</h3>
                        <img src="solution_time_analysis.png" alt="Solution Time Analysis">
                    </div>
                    <div class="image-card">
                        <h3>Dataset Comparison</h3>
                        <img src="dataset_comparison.png" alt="Dataset Comparison">
                    </div>
                    <div class="image-card">
                        <h3>Improvement Analysis</h3>
                        <img src="improvement_analysis.png" alt="Improvement Analysis">
                    </div>
                </div>
            </div>
        </body>
        </html>
        """

        return html

    def _create_comparison_table(self, results: Dict) -> str:
        """创建比较表格"""
        html = """
                <h2>🔄 Method Comparison</h2>
                <table class="comparison-table">
                    <thead>
                        <tr>
                            <th>Method</th>
                            <th>Success Rate</th>
                            <th>Avg Makespan</th>
                            <th>Avg Time (s)</th>
                            <th>Makespan Improvement</th>
                            <th>Time Improvement</th>
                        </tr>
                    </thead>
                    <tbody>
        """

        # 神经方法行
        neural_stats = results.get('summary', {})
        html += f"""
                        <tr style="background-color: #e3f2fd;">
                            <td><strong>Neural Column Generation</strong></td>
                            <td>{neural_stats.get('success_rate', 0):.1%}</td>
                            <td>{neural_stats.get('avg_makespan', 0):.2f}</td>
                            <td>{neural_stats.get('avg_solution_time', 0):.3f}</td>
                            <td>-</td>
                            <td>-</td>
                        </tr>
        """

        # 基线方法行
        for method, comparison in results.get('overall_comparison', {}).items():
            if comparison.get('comparison_valid', False):
                baseline_stats = comparison.get('baseline_stats', {})
                makespan_imp = comparison.get('makespan_improvement_pct', 0)
                time_imp = comparison.get('time_improvement_pct', 0)

                makespan_class = 'improvement' if makespan_imp > 0 else 'degradation'
                time_class = 'improvement' if time_imp > 0 else 'degradation'

                html += f"""
                        <tr>
                            <td>{method}</td>
                            <td>{baseline_stats.get('success_rate', 0):.1%}</td>
                            <td>{baseline_stats.get('avg_makespan', 0):.2f}</td>
                            <td>{baseline_stats.get('avg_solution_time', 0):.3f}</td>
                            <td class="{makespan_class}">{makespan_imp:+.1f}%</td>
                            <td class="{time_class}">{time_imp:+.1f}%</td>
                        </tr>
                """

        html += """
                    </tbody>
                </table>
        """

        return html
