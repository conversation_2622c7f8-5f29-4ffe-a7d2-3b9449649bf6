#!/usr/bin/env python3
"""
Basic Usage Example
基本使用示例
"""

import sys
sys.path.append('..')

from core import NeuralColumnModel, ColumnGenerator, FeatureExtractor, ScheduleEvaluator, FJSPParser
from config import get_config

def main():
    # 加载配置
    config = get_config()
    
    # 解析FJSP实例
    parser = FJSPParser()
    instance = parser.parse_file("../data/raw/example.fjs")
    
    # 提取特征
    feature_extractor = FeatureExtractor(config)
    features = feature_extractor.extract_features(instance)
    
    # 加载模型
    model = NeuralColumnModel(config)
    # model.load_state_dict(torch.load("../data/models/pretrained_model.pth"))
    
    # 生成列
    column_generator = ColumnGenerator(model, config)
    columns = column_generator.generate_columns(features, instance['job_length'])
    
    # 评估结果
    evaluator = ScheduleEvaluator(config)
    evaluated_columns = evaluator.evaluate_columns(columns, instance)
    
    # 显示结果
    for i, column in enumerate(evaluated_columns):
        print(f"Column {i+1}: Makespan={column.makespan:.1f}, Feasible={column.feasible}")

if __name__ == "__main__":
    main()
