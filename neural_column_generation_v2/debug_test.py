#!/usr/bin/env python3
"""
Debug Test Script
调试测试脚本 - 测试核心功能
"""

import sys
import os
import torch
import numpy as np
from typing import List, Dict, Tuple

# 添加路径
sys.path.insert(0, '.')

# 导入核心模块
from core import NeuralColumnModel, ColumnGenerator, FeatureExtractor, ScheduleEvaluator, FJSPParser
from config import get_debug_config


def create_test_instance():
    """创建测试实例"""
    return {
        'instance_id': 'test_001',
        'n_jobs': 3,
        'n_machines': 3,
        'n_operations': 6,
        'job_length': [2, 2, 2],
        'processing_times': [
            [10, 15, 20],  # Job 0, Op 0
            [12, 8, 16],   # Job 0, Op 1
            [14, 18, 10],  # Job 1, Op 0
            [16, 12, 14],  # Job 1, Op 1
            [8, 20, 12],   # Job 2, Op 0
            [18, 10, 16]   # Job 2, Op 1
        ]
    }


def test_core_modules():
    """测试核心模块"""
    print("🧪 Testing Core Modules")
    print("=" * 50)
    
    # 1. 测试配置
    print("1️⃣ Testing Configuration...")
    config = get_debug_config()
    print(f"   ✅ Config loaded: {config.model.d_model} dimensions")
    
    # 2. 测试数据解析
    print("2️⃣ Testing Data Parser...")
    parser = FJSPParser()
    instance = create_test_instance()
    is_valid = parser.validate_instance(instance)
    print(f"   ✅ Instance validation: {is_valid}")
    
    # 3. 测试特征提取
    print("3️⃣ Testing Feature Extractor...")
    feature_extractor = FeatureExtractor(config)
    features = feature_extractor.extract_features(instance)
    print(f"   ✅ Features extracted:")
    print(f"      Job features: {features['job_features'].shape}")
    print(f"      Operation features: {features['operation_features'].shape}")
    print(f"      Machine features: {features['machine_features'].shape}")
    print(f"      Processing matrix: {features['processing_matrix'].shape}")
    
    # 4. 测试神经网络模型
    print("4️⃣ Testing Neural Model...")
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    model = NeuralColumnModel(config).to(device)
    print(f"   ✅ Model created on {device}")
    print(f"   ✅ Model parameters: {sum(p.numel() for p in model.parameters()):,}")
    
    # 5. 测试列生成
    print("5️⃣ Testing Column Generator...")
    column_generator = ColumnGenerator(model, config)
    
    # 移动特征到设备
    device_features = {}
    for key, value in features.items():
        if isinstance(value, torch.Tensor):
            device_features[key] = value.to(device)
        else:
            device_features[key] = value
    
    try:
        columns = column_generator.generate_columns(
            device_features, 
            instance['job_length'],
            num_columns=2
        )
        print(f"   ✅ Generated {len(columns)} columns")
        
        for i, column in enumerate(columns):
            print(f"      Column {i+1}: {len(column.schedule)} operations, feasible: {column.feasible}")
    
    except Exception as e:
        print(f"   ❌ Column generation failed: {e}")
        import traceback
        traceback.print_exc()
    
    # 6. 测试调度评估
    print("6️⃣ Testing Schedule Evaluator...")
    evaluator = ScheduleEvaluator(config)
    
    # 创建一个简单的可行调度
    simple_schedule = [
        (0, 0), (1, 1),  # Job 0
        (2, 2), (3, 0),  # Job 1  
        (4, 1), (5, 2)   # Job 2
    ]
    
    makespan, feasible, metrics = evaluator.evaluate_schedule(simple_schedule, instance)
    print(f"   ✅ Schedule evaluation:")
    print(f"      Makespan: {makespan}")
    print(f"      Feasible: {feasible}")
    print(f"      Decision quality: {metrics.get('decision_quality', 'N/A'):.3f}")
    
    return True


def test_training_components():
    """测试训练组件"""
    print("\n🏋️ Testing Training Components")
    print("=" * 50)
    
    try:
        # 简化的损失函数测试
        print("1️⃣ Testing Loss Functions...")
        
        from core.neural_model import ScheduleColumn
        
        # 创建测试列
        test_columns = [[
            ScheduleColumn(
                schedule=[(0, 0), (1, 1), (2, 2), (3, 0), (4, 1), (5, 2)],
                makespan=50.0,
                feasible=True,
                quality_score=0.8,
                decision_quality=0.9
            )
        ]]
        
        test_instances = [create_test_instance()]
        
        print("   ✅ Test data created")
        
        # 简化的训练器测试
        print("2️⃣ Testing Trainer Components...")
        config = get_debug_config()
        model = NeuralColumnModel(config)
        
        print(f"   ✅ Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Training components test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主测试函数"""
    print("🎯 NEURAL COLUMN GENERATION V2.0 - DEBUG TEST")
    print("=" * 70)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    
    # 显示系统信息
    print(f"🖥️  System Info:")
    print(f"   Python: {sys.version}")
    print(f"   PyTorch: {torch.__version__}")
    print(f"   CUDA available: {torch.cuda.is_available()}")
    if torch.cuda.is_available():
        print(f"   CUDA device: {torch.cuda.get_device_name()}")
    print()
    
    # 测试核心模块
    core_success = test_core_modules()
    
    # 测试训练组件
    training_success = test_training_components()
    
    # 总结
    print("\n" + "=" * 70)
    print("🎉 DEBUG TEST SUMMARY")
    print("=" * 70)
    
    if core_success:
        print("✅ Core modules: PASSED")
    else:
        print("❌ Core modules: FAILED")
    
    if training_success:
        print("✅ Training components: PASSED")
    else:
        print("❌ Training components: FAILED")
    
    if core_success and training_success:
        print("\n🎉 All tests passed! System is ready for use.")
        return True
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
