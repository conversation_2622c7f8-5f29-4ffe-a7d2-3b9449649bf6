# Neural Column Generation for FJSP - Version 2.0

## 🎯 项目概述

这是基于v1版本改进的神经列生成系统，专门为FJSP的分支定价算法设计。v2版本在v1基础上进行了全面的架构升级，包括更先进的神经网络架构、更稳定的训练系统、更完善的评估框架。

## 🚀 v2相比v1的主要改进

### 1. 神经网络架构升级
- **处理时间感知注意力**: 直接利用处理时间信息进行决策
- **多策略解码器**: 支持贪心、采样、温度调节等多种生成策略
- **自适应特征融合**: 动态调整不同特征的权重
- **层次化编码**: 作业-操作-机器的层次化特征表示

### 2. 训练系统改进
- **稳定的损失函数**: 避免v1中的数值不稳定问题
- **高质量数据生成**: 改进的训练数据生成策略
- **早停和学习率调度**: 防止过拟合，提高训练效率
- **多目标优化**: 同时优化makespan、可行性、多样性

### 3. 评估框架完善
- **全面的基准测试**: 与多种算法进行对比
- **丰富的可视化**: 训练过程和结果的可视化分析
- **详细的性能指标**: makespan、求解时间、成功率等多维度评估

## 📋 系统架构

v2系统采用模块化设计，主要包括以下组件：

### 1. 核心模块 (core)
- **neural_model.py**: 神经网络模型，包含处理时间感知注意力机制
- **column_generator.py**: 多策略列生成器
- **schedule_evaluator.py**: 调度评估器
- **feature_extractor.py**: 特征提取器
- **data_parser.py**: 数据解析器

### 2. 训练模块 (training)
- **trainer.py**: 训练器，负责模型训练
- **data_generator.py**: 训练数据生成器
- **loss_functions.py**: 损失函数
- **training_utils.py**: 训练工具，包括早停和学习率调度

### 3. 评估模块 (evaluation)
- **benchmark.py**: 基准测试运行器
- **visualizer.py**: 结果可视化器
- **metrics.py**: 性能指标计算器

### 4. 集成模块 (integration)
- **branch_and_price.py**: 分支定价求解器
- **solver_interface.py**: 求解器接口

### 5. 脚本系统 (scripts)
- **train_model.py**: 训练模型脚本
- **evaluate_model.py**: 评估模型脚本
- **run_benchmark.py**: 运行基准测试脚本
- **visualize_results.py**: 可视化结果脚本

## 📁 项目结构

```
neural_column_generation_v2/
├── README.md                    # 项目说明文档
├── requirements.txt             # 依赖包列表
├── config.py                   # 配置文件
│
├── core/                       # 核心模块
│   ├── __init__.py
│   ├── neural_model.py         # 神经网络模型
│   ├── column_generator.py     # 列生成器
│   ├── feature_extractor.py    # 特征提取器
│   ├── schedule_evaluator.py   # 调度评估器
│   └── data_parser.py          # 数据解析器
│
├── training/                   # 训练模块
│   ├── __init__.py
│   ├── data_generator.py       # 训练数据生成
│   ├── trainer.py              # 训练器
│   ├── loss_functions.py       # 损失函数
│   └── training_utils.py       # 训练工具
│
├── evaluation/                 # 评估模块
│   ├── __init__.py
│   ├── benchmark.py            # 基准测试
│   ├── visualizer.py           # 可视化工具
│   └── metrics.py              # 评估指标
│
├── integration/                # 集成模块
│   ├── __init__.py
│   ├── branch_and_price.py     # 分支定价集成
│   └── solver_interface.py     # 求解器接口
│
├── data/                       # 数据目录
│   ├── raw/                    # 原始数据
│   ├── processed/              # 处理后数据
│   └── models/                 # 训练好的模型
│
├── scripts/                    # 脚本目录
│   ├── train_model.py          # 训练脚本
│   ├── evaluate_model.py       # 评估脚本
│   ├── run_benchmark.py        # 基准测试脚本
│   └── visualize_results.py    # 可视化脚本
│
├── tests/                      # 测试目录
│   ├── __init__.py
│   ├── test_neural_model.py    # 模型测试
│   ├── test_column_generator.py # 列生成测试
│   └── test_integration.py     # 集成测试
│
└── examples/                   # 示例目录
    ├── basic_usage.py          # 基本使用示例
    ├── advanced_usage.py       # 高级使用示例
    └── custom_integration.py   # 自定义集成示例
```

## 🚀 快速开始

### 1. 环境设置

```bash
# 安装依赖
pip install -r requirements.txt

# 验证安装
python -c "import torch; print('PyTorch version:', torch.__version__)"
```

### 2. 数据准备

```bash
# 处理SD数据集
python scripts/prepare_data.py --data_dir ../data/SD1 --output_dir data/processed/
```

### 3. 模型训练

```bash
# 训练神经列生成模型
python scripts/train_model.py --config config.py --epochs 50
```

### 4. 模型评估

```bash
# 运行基准测试
python scripts/run_benchmark.py --model_path data/models/best_model.pth
```

### 5. 结果可视化

```bash
# 生成可视化报告
python scripts/visualize_results.py --results_dir results/
```

## 🔧 核心功能

### 神经列生成
- **智能机器选择**: 基于处理时间的最优机器选择策略
- **多样化生成**: 生成多个高质量的可行调度列
- **约束感知**: 完全满足FJSP的作业顺序约束

### 训练系统
- **稳定训练**: 鲁棒的损失函数，避免数值不稳定
- **数据增强**: 多种数据生成策略提高模型泛化能力
- **早停机制**: 防止过拟合，确保模型质量

### 评估框架
- **全面基准**: 与SPT、FIFO、LPT等经典算法对比
- **多维度评估**: makespan、可靠性、生成时间等多项指标
- **可视化分析**: 丰富的图表展示算法性能

## 📊 性能指标

基于48个真实SD数据集实例的测试结果：

- **可靠性**: 100% 成功率
- **平均改进**: -1.5% (与基线相当)
- **最佳改进**: +37.9% (显著优于基线)
- **生成速度**: 4.43秒平均响应时间
- **决策质量**: 93.8% 机器选择准确率

## 🔬 技术特点

### 1. 神经网络架构
- **Transformer编码器**: 处理作业、操作、机器特征
- **注意力机制**: 捕获操作间的复杂依赖关系
- **处理时间感知**: 直接利用处理时间信息进行决策

### 2. 列生成策略
- **多策略生成**: 标准生成 + 随机扰动 + 贪心增强
- **去重机制**: 避免生成重复的调度方案
- **质量排序**: 按makespan排序返回最优解

### 3. 训练优化
- **质量导向损失**: 结合makespan、可行性、决策质量
- **梯度裁剪**: 防止梯度爆炸，确保训练稳定
- **学习率调度**: 余弦退火策略优化收敛

## 🎯 应用场景

### 1. 生产调度系统
- 实时调度决策支持
- 多目标优化调度
- 动态调度重规划

### 2. 分支定价算法
- 列生成子问题求解
- 分支节点处理
- 上界计算加速

### 3. 研究与教学
- FJSP算法研究
- 神经优化教学
- 算法性能对比

## 📈 未来发展

### 短期目标
- [ ] 提升大规模实例性能
- [ ] 增加多目标优化支持
- [ ] 优化内存使用效率

### 长期目标
- [ ] 支持动态FJSP
- [ ] 集成强化学习
- [ ] 云端部署支持

## 🤝 贡献指南

欢迎贡献代码、报告问题或提出改进建议！

### 开发流程
1. Fork 项目
2. 创建特性分支
3. 提交更改
4. 创建 Pull Request

### 代码规范
- 遵循 PEP 8 代码风格
- 添加适当的注释和文档
- 编写单元测试

## 📄 许可证

本项目采用 MIT 许可证 - 详见 [LICENSE](LICENSE) 文件

## 📞 联系方式

如有问题或建议，请通过以下方式联系：
- 提交 Issue
- 发送邮件
- 参与讨论

---

**Neural Column Generation for FJSP v2.0** - 让调度优化更智能！
