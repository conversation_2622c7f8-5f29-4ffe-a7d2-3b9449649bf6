#!/usr/bin/env python3
"""
Working Training System
可运行的训练系统 - 修复所有问题的完整训练流程
"""

import sys
import os
import torch
import numpy as np
import time
import random

# 添加路径
sys.path.insert(0, '.')

# 导入模块
from core import NeuralColumnModel, ColumnGenerator, FeatureExtractor, ScheduleEvaluator, FJSPParser
from config import get_config


class WorkingDataGenerator:
    """可工作的数据生成器"""
    
    def __init__(self):
        self.parser = FJSPParser()
    
    def generate_dataset(self, num_instances=50):
        """生成数据集"""
        print(f"🔄 Generating {num_instances} training instances...")
        
        instances = []
        
        for i in range(num_instances):
            n_jobs = random.randint(3, 5)
            n_machines = random.randint(3, 4)
            
            instance = self._create_instance(n_jobs, n_machines, f"train_{i:03d}")
            
            if self.parser.validate_instance(instance):
                instances.append(instance)
        
        print(f"✅ Generated {len(instances)} valid instances")
        return instances
    
    def _create_instance(self, n_jobs, n_machines, instance_id):
        """创建单个实例"""
        job_lengths = []
        total_operations = 0
        
        for job_id in range(n_jobs):
            n_ops = random.randint(2, 3)
            job_lengths.append(n_ops)
            total_operations += n_ops
        
        processing_times = []
        
        for op_id in range(total_operations):
            proc_times = [0] * n_machines
            
            n_available = random.randint(1, n_machines)
            available_machines = random.sample(range(n_machines), n_available)
            
            for machine_id in available_machines:
                proc_time = random.randint(10, 30)
                proc_times[machine_id] = proc_time
            
            processing_times.append(proc_times)
        
        return {
            'instance_id': instance_id,
            'n_jobs': n_jobs,
            'n_machines': n_machines,
            'n_operations': total_operations,
            'job_length': job_lengths,
            'processing_times': processing_times,
            'format': 'synthetic'
        }


class SimpleTrainer:
    """简化的训练器"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        print(f"🎯 Initializing trainer on {self.device}")
        
        # 初始化组件
        self.model = NeuralColumnModel(config).to(self.device)
        self.column_generator = ColumnGenerator(self.model, config)
        self.feature_extractor = FeatureExtractor(config)
        self.evaluator = ScheduleEvaluator(config)
        
        # 优化器
        self.optimizer = torch.optim.Adam(
            self.model.parameters(),
            lr=0.001
        )
        
        print(f"   Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
    
    def train_one_instance(self, instance):
        """训练单个实例"""
        self.model.train()
        
        # 提取特征
        features = self.feature_extractor.extract_features(instance)
        
        # 移动特征到设备
        device_features = {}
        for key, value in features.items():
            if isinstance(value, torch.Tensor):
                device_features[key] = value.to(self.device)
            else:
                device_features[key] = value
        
        # 生成列
        columns = self.column_generator.generate_columns(
            device_features,
            instance['job_length'],
            num_columns=2
        )
        
        # 评估列
        evaluated_columns = self.evaluator.evaluate_columns(columns, instance)
        
        # 计算损失
        makespans = []
        for column in evaluated_columns:
            if column.feasible and column.makespan < float('inf'):
                makespans.append(column.makespan)
        
        if makespans:
            best_makespan = min(makespans)
            loss = torch.tensor(best_makespan / 100.0, device=self.device, requires_grad=True)
        else:
            loss = torch.tensor(1.0, device=self.device, requires_grad=True)
        
        # 反向传播
        self.optimizer.zero_grad()
        loss.backward()
        self.optimizer.step()
        
        return loss.item(), evaluated_columns
    
    def train(self, instances, num_epochs=3):
        """训练多个实例"""
        print(f"🚀 Training on {len(instances)} instances for {num_epochs} epochs...")
        
        for epoch in range(num_epochs):
            epoch_start = time.time()
            
            # 随机打乱实例
            random.shuffle(instances)
            
            # 训练每个实例
            epoch_losses = []
            feasible_count = 0
            total_columns = 0
            
            for i, instance in enumerate(instances):
                try:
                    loss, columns = self.train_one_instance(instance)
                    epoch_losses.append(loss)
                    
                    # 统计可行列
                    for column in columns:
                        total_columns += 1
                        if column.feasible:
                            feasible_count += 1
                    
                except Exception as e:
                    print(f"  ⚠️ Error training instance {instance['instance_id']}: {e}")
            
            # 计算统计信息
            avg_loss = np.mean(epoch_losses) if epoch_losses else float('inf')
            feasible_rate = feasible_count / max(total_columns, 1)
            
            epoch_time = time.time() - epoch_start
            
            print(f"Epoch {epoch+1}/{num_epochs} ({epoch_time:.1f}s) | "
                  f"Loss: {avg_loss:.4f} | Feasible: {feasible_rate:.1%}")
        
        print(f"✅ Training completed!")
        
        # 保存模型
        os.makedirs('data/models', exist_ok=True)
        torch.save(self.model.state_dict(), 'data/models/simple_model.pth')
        print(f"💾 Model saved to data/models/simple_model.pth")


def main():
    """主函数"""
    print("🎯 WORKING TRAINING SYSTEM")
    print("=" * 50)
    
    # 设置随机种子
    torch.manual_seed(42)
    np.random.seed(42)
    random.seed(42)
    
    # 加载配置
    config = get_config()
    
    print(f"📋 Configuration:")
    print(f"   Device: {config.system.device}")
    print()
    
    # 生成数据
    data_generator = WorkingDataGenerator()
    all_instances = data_generator.generate_dataset(num_instances=20)
    
    print(f"📊 Generated {len(all_instances)} instances for training")
    
    # 显示第一个实例
    if all_instances:
        instance = all_instances[0]
        print(f"\nSample instance:")
        print(f"   ID: {instance['instance_id']}")
        print(f"   Jobs: {instance['n_jobs']}")
        print(f"   Machines: {instance['n_machines']}")
        print(f"   Operations: {instance['n_operations']}")
        print(f"   Job lengths: {instance['job_length']}")
    
    # 创建训练器并训练
    trainer = SimpleTrainer(config)
    trainer.train(all_instances, num_epochs=3)
    
    print(f"\n✅ Training system completed successfully!")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
