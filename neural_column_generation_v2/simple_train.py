#!/usr/bin/env python3
"""
Simple Training Script
简单训练脚本 - 测试训练功能
"""

import os
import sys
import time
import torch
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from config import get_config
from core.neural_model import NeuralColumnModel
from core.column_generator import ColumnGenerator
from core.schedule_evaluator import ScheduleEvaluator
from core.feature_extractor import FeatureExtractor
from training.data_generator import TrainingDataGenerator
from training.loss_functions import CombinedLoss


def create_simple_trainer(model, config, device):
    """创建简单训练器"""
    
    class SimpleTrainer:
        def __init__(self, model, config, device):
            self.model = model
            self.config = config
            self.device = device
            
            # 创建组件
            self.column_generator = ColumnGenerator(model, config)
            self.evaluator = ScheduleEvaluator()
            self.feature_extractor = FeatureExtractor(config)
            self.criterion = CombinedLoss(config)
            
            # 创建优化器
            self.optimizer = torch.optim.AdamW(
                model.parameters(),
                lr=config.training.learning_rate,
                weight_decay=config.training.weight_decay
            )
        
        def train_epoch(self, train_data):
            """训练一个epoch"""
            self.model.train()
            total_loss = 0.0
            
            for i, instance in enumerate(train_data):
                self.optimizer.zero_grad()
                
                try:
                    # 提取特征
                    features = self.feature_extractor.extract_features(instance)
                    
                    # 生成列
                    columns = self.column_generator.generate_columns(
                        features,
                        instance['job_length'],
                        num_columns=2
                    )
                    
                    # 评估列
                    evaluated_columns = []
                    for column in columns:
                        evaluated_column = self.evaluator.evaluate_column(column, instance)
                        evaluated_columns.append(evaluated_column)
                    
                    # 计算损失
                    loss, loss_components = self.criterion(evaluated_columns, instance)
                    
                    # 反向传播
                    loss.backward()
                    
                    # 梯度裁剪
                    torch.nn.utils.clip_grad_norm_(self.model.parameters(), 1.0)
                    
                    # 更新参数
                    self.optimizer.step()
                    
                    total_loss += loss.item()
                    
                    print(f"    Batch {i+1}: loss={loss.item():.4f}")
                    
                except Exception as e:
                    print(f"    ⚠️ Error in batch {i+1}: {e}")
                    continue
            
            return total_loss / len(train_data) if train_data else 0.0
        
        def train(self, train_data, num_epochs=5):
            """训练模型"""
            print(f"🎯 Starting training for {num_epochs} epochs...")
            
            for epoch in range(num_epochs):
                print(f"\nEpoch {epoch + 1}/{num_epochs}")
                
                start_time = time.time()
                avg_loss = self.train_epoch(train_data)
                epoch_time = time.time() - start_time
                
                print(f"  Average loss: {avg_loss:.4f}")
                print(f"  Epoch time: {epoch_time:.2f}s")
            
            print("✅ Training completed!")
    
    return SimpleTrainer(model, config, device)


def main():
    """主函数"""
    print("🚀 Simple Training Test")
    print("=" * 50)
    
    try:
        # 加载配置
        config = get_config()
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        
        # 创建模型
        model = NeuralColumnModel(config).to(device)
        print(f"✅ Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
        
        # 生成训练数据
        data_generator = TrainingDataGenerator(config)
        train_data = data_generator.generate_synthetic_data(num_instances=5)
        print(f"✅ Generated {len(train_data)} training instances")
        
        # 创建训练器
        trainer = create_simple_trainer(model, config, device)
        
        # 训练模型
        trainer.train(train_data, num_epochs=3)
        
        print("\n🎉 Simple training test completed successfully!")
        
    except Exception as e:
        print(f"\n❌ Training test failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
