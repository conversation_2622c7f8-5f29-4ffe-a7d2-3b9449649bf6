#!/usr/bin/env python3
"""
Basic Tests
基本测试
"""

import unittest
import sys
import os

# 添加项目路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from core import NeuralColumnModel, FeatureExtractor
from config import get_debug_config

class TestNeuralModel(unittest.TestCase):
    
    def setUp(self):
        self.config = get_debug_config()
        self.model = NeuralColumnModel(self.config)
    
    def test_model_creation(self):
        """测试模型创建"""
        self.assertIsNotNone(self.model)
        self.assertEqual(self.model.d_model, self.config.model.d_model)
    
    def test_feature_extractor(self):
        """测试特征提取器"""
        extractor = FeatureExtractor(self.config)
        self.assertIsNotNone(extractor)

if __name__ == '__main__':
    unittest.main()
