#!/usr/bin/env python3
"""
Run Improved Training
运行改进的训练 - 使用改进的训练器训练模型
"""

import os
import sys
import argparse
import time
import random
import torch
import numpy as np
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from config import get_config
from core.neural_model import NeuralColumnModel
from core.column_generator import ColumnGenerator
from core.schedule_evaluator import ScheduleEvaluator
from core.feature_extractor import FeatureExtractor
from training.data_generator import TrainingDataGenerator
from improved_train import ImprovedTrainer


def parse_arguments():
    """解析命令行参数"""
    parser = argparse.ArgumentParser(description='Run Improved Training')
    
    parser.add_argument('--config', type=str, default='configs/default.json',
                       help='Configuration file path')
    parser.add_argument('--output_dir', type=str, default='outputs/improved_training',
                       help='Output directory')
    parser.add_argument('--epochs', type=int, default=50,
                       help='Number of training epochs')
    parser.add_argument('--batch_size', type=int, default=4,
                       help='Batch size')
    parser.add_argument('--learning_rate', type=float, default=0.001,
                       help='Learning rate')
    parser.add_argument('--num_instances', type=int, default=100,
                       help='Number of training instances')
    parser.add_argument('--val_split', type=float, default=0.2,
                       help='Validation split ratio')
    parser.add_argument('--device', type=str, default='auto',
                       help='Device to use (auto, cpu, cuda)')
    parser.add_argument('--seed', type=int, default=42,
                       help='Random seed')
    
    return parser.parse_args()


def setup_device(device_arg):
    """设置设备"""
    if device_arg == 'auto':
        device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    else:
        device = torch.device(device_arg)
    
    print(f"🔧 Using device: {device}")
    if device.type == 'cuda':
        print(f"   GPU: {torch.cuda.get_device_name()}")
    
    return device


def set_seed(seed):
    """设置随机种子"""
    torch.manual_seed(seed)
    torch.cuda.manual_seed_all(seed)
    np.random.seed(seed)
    random.seed(seed)
    
    # 设置CUDA的确定性
    torch.backends.cudnn.deterministic = True
    torch.backends.cudnn.benchmark = False
    
    print(f"🔧 Random seed set to {seed}")


def generate_training_data(config, num_instances, val_split):
    """生成训练数据"""
    print(f"📊 Generating {num_instances} training instances...")
    
    data_generator = TrainingDataGenerator(config)
    all_data = data_generator.generate_synthetic_data(num_instances)
    
    # 分割训练和验证数据
    val_size = int(num_instances * val_split)
    train_size = num_instances - val_size
    
    train_data = all_data[:train_size]
    val_data = all_data[train_size:] if val_size > 0 else None
    
    print(f"✅ Generated {len(train_data)} training instances and {len(val_data) if val_data else 0} validation instances")
    
    return train_data, val_data


def main():
    """主函数"""
    print("🚀 Improved Neural Column Generation Training")
    print("=" * 60)
    
    # 解析参数
    args = parse_arguments()
    
    # 设置随机种子
    set_seed(args.seed)
    
    # 设置设备
    device = setup_device(args.device)
    
    # 加载配置
    config = get_config(args.config)
    
    # 更新配置
    config.training.batch_size = args.batch_size
    config.training.learning_rate = args.learning_rate
    config.training.num_epochs = args.epochs
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    try:
        # 创建模型
        model = NeuralColumnModel(config).to(device)
        print(f"✅ Model created with {sum(p.numel() for p in model.parameters()):,} parameters")
        
        # 生成训练数据
        train_data, val_data = generate_training_data(config, args.num_instances, args.val_split)
        
        # 创建训练器
        trainer = ImprovedTrainer(model, config, device)
        trainer.output_dir = args.output_dir
        
        # 训练模型
        results = trainer.train(train_data, val_data, num_epochs=args.epochs)
        
        # 打印结果
        print("\n📋 Training Results:")
        print(f"   Best validation loss: {results['best_loss']:.4f}")
        print(f"   Total training time: {results['total_time']:.2f}s")
        print(f"   Final learning rate: {results['learning_rates'][-1]:.6f}")
        
        # 保存最终模型
        final_model_path = os.path.join(args.output_dir, 'final_model.pth')
        torch.save(model.state_dict(), final_model_path)
        print(f"💾 Final model saved to {final_model_path}")
        
        print("\n🎉 Training completed successfully!")
        
    except KeyboardInterrupt:
        print("\n⏹️ Training interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Training failed: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)


if __name__ == '__main__':
    main()
