#!/usr/bin/env python3
"""
Data Parser
数据解析器 - 解析FJSP数据文件和格式转换
"""

import os
import re
from typing import Dict, List, Optional, Union
import numpy as np


class FJSPParser:
    """FJSP数据解析器"""
    
    def __init__(self):
        self.supported_formats = ['.fjs', '.txt', '.dat']
    
    def parse_file(self, file_path: str) -> Dict:
        """解析FJSP文件"""
        if not os.path.exists(file_path):
            raise FileNotFoundError(f"File not found: {file_path}")
        
        file_ext = os.path.splitext(file_path)[1].lower()
        
        if file_ext not in self.supported_formats:
            raise ValueError(f"Unsupported file format: {file_ext}")
        
        try:
            if file_ext == '.fjs':
                return self._parse_fjs_format(file_path)
            else:
                return self._parse_generic_format(file_path)
        except Exception as e:
            raise ValueError(f"Failed to parse file {file_path}: {str(e)}")
    
    def _parse_fjs_format(self, file_path: str) -> Dict:
        """解析.fjs格式文件"""
        with open(file_path, 'r') as f:
            lines = [line.strip() for line in f.readlines() if line.strip()]
        
        if not lines:
            raise ValueError("Empty file")
        
        # 解析第一行：作业数、机器数、平均机器数
        first_line = lines[0].split()
        if len(first_line) < 2:
            raise ValueError("Invalid first line format")
        
        n_jobs = int(first_line[0])
        n_machines = int(first_line[1])
        
        # 解析作业信息
        job_lengths = []
        processing_times = []
        operation_id = 0
        
        for job_id in range(n_jobs):
            if job_id + 1 >= len(lines):
                raise ValueError(f"Missing data for job {job_id}")
            
            line = lines[job_id + 1].split()
            if not line:
                raise ValueError(f"Empty line for job {job_id}")
            
            n_operations = int(line[0])
            job_lengths.append(n_operations)
            
            idx = 1
            for op_in_job in range(n_operations):
                if idx >= len(line):
                    raise ValueError(f"Insufficient data for job {job_id}, operation {op_in_job}")
                
                # 读取可用机器数
                n_machines_available = int(line[idx])
                idx += 1
                
                # 初始化处理时间矩阵行
                proc_times = [0] * n_machines
                
                # 读取每个可用机器的处理时间
                for _ in range(n_machines_available):
                    if idx + 1 >= len(line):
                        raise ValueError(f"Insufficient machine data for job {job_id}, operation {op_in_job}")
                    
                    machine_id = int(line[idx]) - 1  # 转换为0-based索引
                    processing_time = int(line[idx + 1])
                    
                    if machine_id < 0 or machine_id >= n_machines:
                        raise ValueError(f"Invalid machine ID: {machine_id + 1}")
                    
                    proc_times[machine_id] = processing_time
                    idx += 2
                
                processing_times.append(proc_times)
                operation_id += 1
        
        return {
            'instance_id': os.path.basename(file_path),
            'file_path': file_path,
            'n_jobs': n_jobs,
            'n_machines': n_machines,
            'n_operations': operation_id,
            'job_length': job_lengths,
            'processing_times': processing_times,
            'format': 'fjs'
        }
    
    def _parse_generic_format(self, file_path: str) -> Dict:
        """解析通用格式文件"""
        with open(file_path, 'r') as f:
            content = f.read().strip()
        
        # 尝试不同的解析策略
        try:
            return self._parse_matrix_format(content, file_path)
        except:
            try:
                return self._parse_list_format(content, file_path)
            except:
                raise ValueError("Unable to parse file format")
    
    def _parse_matrix_format(self, content: str, file_path: str) -> Dict:
        """解析矩阵格式"""
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        
        # 假设第一行是维度信息
        first_line = lines[0].split()
        n_jobs = int(first_line[0])
        n_machines = int(first_line[1])
        
        # 解析处理时间矩阵
        processing_times = []
        job_lengths = []
        
        current_job = 0
        operations_in_job = 0
        
        for line in lines[1:]:
            values = [int(x) for x in line.split()]
            if len(values) == n_machines:
                processing_times.append(values)
                operations_in_job += 1
            elif len(values) == 1:  # 新作业开始
                if operations_in_job > 0:
                    job_lengths.append(operations_in_job)
                    operations_in_job = 0
                    current_job += 1
        
        if operations_in_job > 0:
            job_lengths.append(operations_in_job)
        
        return {
            'instance_id': os.path.basename(file_path),
            'file_path': file_path,
            'n_jobs': len(job_lengths),
            'n_machines': n_machines,
            'n_operations': len(processing_times),
            'job_length': job_lengths,
            'processing_times': processing_times,
            'format': 'matrix'
        }
    
    def _parse_list_format(self, content: str, file_path: str) -> Dict:
        """解析列表格式"""
        # 简化的列表格式解析
        lines = [line.strip() for line in content.split('\n') if line.strip()]
        
        # 提取数字
        all_numbers = []
        for line in lines:
            numbers = re.findall(r'\d+', line)
            all_numbers.extend([int(x) for x in numbers])
        
        if len(all_numbers) < 3:
            raise ValueError("Insufficient data")
        
        # 假设前两个数字是作业数和机器数
        n_jobs = all_numbers[0]
        n_machines = all_numbers[1]
        
        # 简单的处理时间矩阵构建
        remaining_numbers = all_numbers[2:]
        n_operations = len(remaining_numbers) // n_machines
        
        processing_times = []
        for i in range(n_operations):
            start_idx = i * n_machines
            end_idx = start_idx + n_machines
            processing_times.append(remaining_numbers[start_idx:end_idx])
        
        # 平均分配操作到作业
        ops_per_job = n_operations // n_jobs
        job_lengths = [ops_per_job] * n_jobs
        
        # 处理余数
        remainder = n_operations % n_jobs
        for i in range(remainder):
            job_lengths[i] += 1
        
        return {
            'instance_id': os.path.basename(file_path),
            'file_path': file_path,
            'n_jobs': n_jobs,
            'n_machines': n_machines,
            'n_operations': n_operations,
            'job_length': job_lengths,
            'processing_times': processing_times,
            'format': 'list'
        }
    
    def parse_directory(self, directory_path: str, pattern: str = "*.fjs") -> List[Dict]:
        """解析目录中的所有文件"""
        if not os.path.exists(directory_path):
            raise FileNotFoundError(f"Directory not found: {directory_path}")
        
        instances = []
        
        # 获取匹配的文件
        import glob
        file_pattern = os.path.join(directory_path, pattern)
        files = glob.glob(file_pattern)
        
        for file_path in sorted(files):
            try:
                instance = self.parse_file(file_path)
                instances.append(instance)
            except Exception as e:
                print(f"Warning: Failed to parse {file_path}: {e}")
                continue
        
        return instances
    
    def validate_instance(self, instance: Dict) -> bool:
        """验证实例数据的有效性"""
        required_fields = ['n_jobs', 'n_machines', 'n_operations', 'job_length', 'processing_times']
        
        # 检查必需字段
        for field in required_fields:
            if field not in instance:
                return False
        
        # 检查数据一致性
        n_jobs = instance['n_jobs']
        n_machines = instance['n_machines']
        n_operations = instance['n_operations']
        job_lengths = instance['job_length']
        processing_times = instance['processing_times']
        
        # 检查作业数
        if len(job_lengths) != n_jobs:
            return False
        
        # 检查操作数
        if sum(job_lengths) != n_operations:
            return False
        
        # 检查处理时间矩阵
        if len(processing_times) != n_operations:
            return False
        
        for proc_times in processing_times:
            if len(proc_times) != n_machines:
                return False
            
            # 检查是否至少有一个有效的处理时间
            if not any(t > 0 for t in proc_times):
                return False
        
        return True
    
    def convert_to_standard_format(self, instance: Dict) -> Dict:
        """转换为标准格式"""
        if not self.validate_instance(instance):
            raise ValueError("Invalid instance data")
        
        # 确保处理时间为整数
        processing_times = []
        for proc_times in instance['processing_times']:
            processing_times.append([int(t) for t in proc_times])
        
        standard_instance = {
            'instance_id': instance.get('instance_id', 'unknown'),
            'file_path': instance.get('file_path', ''),
            'n_jobs': int(instance['n_jobs']),
            'n_machines': int(instance['n_machines']),
            'n_operations': int(instance['n_operations']),
            'job_length': [int(l) for l in instance['job_length']],
            'processing_times': processing_times,
            'format': instance.get('format', 'unknown')
        }
        
        return standard_instance
    
    def save_instance(self, instance: Dict, output_path: str, format: str = 'fjs'):
        """保存实例到文件"""
        if format == 'fjs':
            self._save_fjs_format(instance, output_path)
        else:
            raise ValueError(f"Unsupported output format: {format}")
    
    def _save_fjs_format(self, instance: Dict, output_path: str):
        """保存为.fjs格式"""
        with open(output_path, 'w') as f:
            # 写入第一行
            f.write(f"{instance['n_jobs']} {instance['n_machines']}\n")
            
            # 写入每个作业的信息
            job_lengths = instance['job_length']
            processing_times = instance['processing_times']
            
            op_id = 0
            for job_id, n_ops in enumerate(job_lengths):
                line = [str(n_ops)]
                
                for op_in_job in range(n_ops):
                    proc_times = processing_times[op_id]
                    
                    # 找到可用机器
                    available_machines = [(m, t) for m, t in enumerate(proc_times) if t > 0]
                    
                    line.append(str(len(available_machines)))
                    
                    for machine_id, proc_time in available_machines:
                        line.append(str(machine_id + 1))  # 转换为1-based索引
                        line.append(str(proc_time))
                    
                    op_id += 1
                
                f.write(' '.join(line) + '\n')
    
    def get_instance_statistics(self, instances: List[Dict]) -> Dict:
        """获取实例统计信息"""
        if not instances:
            return {}
        
        n_jobs_list = [inst['n_jobs'] for inst in instances]
        n_machines_list = [inst['n_machines'] for inst in instances]
        n_operations_list = [inst['n_operations'] for inst in instances]
        
        # 处理时间统计
        all_processing_times = []
        for inst in instances:
            for proc_times in inst['processing_times']:
                all_processing_times.extend([t for t in proc_times if t > 0])
        
        stats = {
            'num_instances': len(instances),
            'jobs': {
                'min': min(n_jobs_list),
                'max': max(n_jobs_list),
                'avg': np.mean(n_jobs_list),
                'std': np.std(n_jobs_list)
            },
            'machines': {
                'min': min(n_machines_list),
                'max': max(n_machines_list),
                'avg': np.mean(n_machines_list),
                'std': np.std(n_machines_list)
            },
            'operations': {
                'min': min(n_operations_list),
                'max': max(n_operations_list),
                'avg': np.mean(n_operations_list),
                'std': np.std(n_operations_list)
            }
        }
        
        if all_processing_times:
            stats['processing_times'] = {
                'min': min(all_processing_times),
                'max': max(all_processing_times),
                'avg': np.mean(all_processing_times),
                'std': np.std(all_processing_times)
            }
        
        return stats
