#!/usr/bin/env python3
"""
Column Generator
列生成器 - 负责生成多样化的高质量调度列
"""

import torch
import numpy as np
from typing import List, Dict, Tuple, Optional
import random
from .neural_model import ScheduleColumn


class ColumnGenerator:
    """列生成器主类"""
    
    def __init__(self, model, config):
        self.model = model
        self.config = config
        self.device = next(model.parameters()).device
    
    def generate_columns(self, features: Dict, job_lengths: List[int], 
                        num_columns: int = None) -> List[ScheduleColumn]:
        """生成多样化的调度列"""
        if num_columns is None:
            num_columns = self.config.model.num_columns
        
        all_columns = []
        
        # 策略1: 标准神经网络生成
        standard_columns = self._generate_standard_columns(features, job_lengths, num_columns)
        all_columns.extend(standard_columns)
        
        # 策略2: 随机扰动生成
        perturbed_columns = self._generate_perturbed_columns(features, job_lengths, num_columns // 2)
        all_columns.extend(perturbed_columns)
        
        # 策略3: 贪心启发式增强
        greedy_columns = self._generate_greedy_enhanced_columns(features, job_lengths, 2)
        all_columns.extend(greedy_columns)
        
        # 去重和质量筛选
        unique_columns = self._deduplicate_and_filter(all_columns)
        
        # 按质量排序并返回最佳的num_columns个
        return self._select_best_columns(unique_columns, num_columns)
    
    def _generate_standard_columns(self, features: Dict, job_lengths: List[int], 
                                  num_columns: int) -> List[ScheduleColumn]:
        """标准神经网络生成"""
        with torch.no_grad():
            columns = self.model(
                features['job_features'].unsqueeze(0).to(self.device),
                features['operation_features'].unsqueeze(0).to(self.device),
                features['machine_features'].unsqueeze(0).to(self.device),
                features['processing_matrix'].unsqueeze(0).to(self.device),
                job_lengths,
                num_columns=num_columns
            )
        return columns
    
    def _generate_perturbed_columns(self, features: Dict, job_lengths: List[int], 
                                   num_columns: int) -> List[ScheduleColumn]:
        """随机扰动生成"""
        perturbed_columns = []
        
        for _ in range(num_columns):
            # 为特征添加小幅随机扰动
            perturbed_features = self._add_feature_noise(features, noise_level=0.05)
            
            with torch.no_grad():
                columns = self.model(
                    perturbed_features['job_features'].unsqueeze(0).to(self.device),
                    perturbed_features['operation_features'].unsqueeze(0).to(self.device),
                    perturbed_features['machine_features'].unsqueeze(0).to(self.device),
                    perturbed_features['processing_matrix'].unsqueeze(0).to(self.device),
                    job_lengths,
                    num_columns=1
                )
                perturbed_columns.extend(columns)
        
        return perturbed_columns
    
    def _generate_greedy_enhanced_columns(self, features: Dict, job_lengths: List[int], 
                                         num_columns: int) -> List[ScheduleColumn]:
        """贪心启发式增强生成"""
        greedy_columns = []
        processing_matrix = features['processing_matrix'].cpu().numpy()
        
        # 生成不同的贪心策略
        strategies = ['shortest_time', 'longest_time', 'least_loaded', 'random']
        
        for i in range(num_columns):
            strategy = strategies[i % len(strategies)]
            schedule = self._generate_greedy_schedule(processing_matrix, job_lengths, strategy)
            
            if schedule:
                column = ScheduleColumn(
                    schedule=schedule,
                    makespan=0.0,  # 将在评估时计算
                    feasible=True,
                    quality_score=0.0,  # 将在评估时计算
                    decision_quality=self._calculate_greedy_decision_quality(schedule, processing_matrix)
                )
                greedy_columns.append(column)
        
        return greedy_columns
    
    def _generate_greedy_schedule(self, processing_matrix: np.ndarray, job_lengths: List[int], 
                                 strategy: str) -> List[Tuple[int, int]]:
        """生成贪心调度"""
        n_jobs = len(job_lengths)
        n_machines = processing_matrix.shape[1]
        n_operations = processing_matrix.shape[0]
        
        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_lengths):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops
        
        schedule = []
        job_progress = [0] * n_jobs
        scheduled_ops = set()
        machine_loads = [0] * n_machines
        
        # 贪心调度循环
        while len(scheduled_ops) < n_operations:
            # 获取可调度操作
            available_ops = []
            for job_id, operations in enumerate(job_operations):
                if job_progress[job_id] < len(operations):
                    next_op = operations[job_progress[job_id]]
                    if next_op not in scheduled_ops:
                        available_ops.append(next_op)
            
            if not available_ops:
                break
            
            # 根据策略选择操作和机器
            best_op, best_machine = self._select_op_machine_by_strategy(
                available_ops, processing_matrix, machine_loads, strategy
            )
            
            if best_op is not None and best_machine is not None:
                schedule.append((best_op, best_machine))
                scheduled_ops.add(best_op)
                machine_loads[best_machine] += processing_matrix[best_op, best_machine]
                
                # 更新作业进度
                for job_id, ops in enumerate(job_operations):
                    if best_op in ops:
                        job_progress[job_id] += 1
                        break
        
        return schedule if len(scheduled_ops) == n_operations else []
    
    def _select_op_machine_by_strategy(self, available_ops: List[int], 
                                      processing_matrix: np.ndarray,
                                      machine_loads: List[float], 
                                      strategy: str) -> Tuple[Optional[int], Optional[int]]:
        """根据策略选择操作和机器"""
        best_op = None
        best_machine = None
        best_score = float('inf') if strategy in ['shortest_time', 'least_loaded'] else -float('inf')
        
        for op_id in available_ops:
            # 找到可用机器
            valid_machines = [m for m in range(processing_matrix.shape[1]) 
                             if processing_matrix[op_id, m] > 0]
            
            if not valid_machines:
                continue
            
            for machine_id in valid_machines:
                proc_time = processing_matrix[op_id, machine_id]
                
                if strategy == 'shortest_time':
                    score = proc_time
                    if score < best_score:
                        best_score = score
                        best_op = op_id
                        best_machine = machine_id
                
                elif strategy == 'longest_time':
                    score = proc_time
                    if score > best_score:
                        best_score = score
                        best_op = op_id
                        best_machine = machine_id
                
                elif strategy == 'least_loaded':
                    score = machine_loads[machine_id] + proc_time
                    if score < best_score:
                        best_score = score
                        best_op = op_id
                        best_machine = machine_id
                
                elif strategy == 'random':
                    if random.random() < 0.1:  # 10%概率选择
                        best_op = op_id
                        best_machine = machine_id
                        break
            
            if strategy == 'random' and best_op is not None:
                break
        
        return best_op, best_machine
    
    def _calculate_greedy_decision_quality(self, schedule: List[Tuple[int, int]], 
                                          processing_matrix: np.ndarray) -> float:
        """计算贪心调度的决策质量"""
        if not schedule:
            return 0.0
        
        total_efficiency = 0.0
        
        for op_id, machine_id in schedule:
            if op_id < processing_matrix.shape[0] and machine_id < processing_matrix.shape[1]:
                proc_time = processing_matrix[op_id, machine_id]
                valid_times = [processing_matrix[op_id, m] for m in range(processing_matrix.shape[1]) 
                              if processing_matrix[op_id, m] > 0]
                
                if valid_times:
                    min_time = min(valid_times)
                    max_time = max(valid_times)
                    if max_time > min_time:
                        efficiency = (max_time - proc_time) / (max_time - min_time)
                    else:
                        efficiency = 1.0
                    total_efficiency += efficiency
        
        return total_efficiency / len(schedule) if schedule else 0.0
    
    def _add_feature_noise(self, features: Dict, noise_level: float = 0.05) -> Dict:
        """为特征添加噪声"""
        noisy_features = {}
        
        for key, tensor in features.items():
            if key != 'job_lengths' and isinstance(tensor, torch.Tensor):
                noise = torch.randn_like(tensor) * noise_level
                noisy_features[key] = tensor + noise
            else:
                noisy_features[key] = tensor
        
        return noisy_features
    
    def _deduplicate_and_filter(self, columns: List[ScheduleColumn]) -> List[ScheduleColumn]:
        """去重和过滤"""
        unique_columns = []
        seen_schedules = set()
        
        for column in columns:
            if column.feasible and column.schedule:
                # 创建调度的唯一标识
                schedule_key = tuple(sorted(column.schedule))
                
                if schedule_key not in seen_schedules:
                    seen_schedules.add(schedule_key)
                    unique_columns.append(column)
        
        return unique_columns
    
    def _select_best_columns(self, columns: List[ScheduleColumn], 
                            num_columns: int) -> List[ScheduleColumn]:
        """选择最佳列"""
        if not columns:
            return []
        
        # 按makespan排序（需要先评估）
        # 这里假设makespan已经在外部计算
        valid_columns = [col for col in columns if col.feasible and col.makespan > 0]
        
        if not valid_columns:
            return columns[:num_columns]  # 返回可行的列
        
        # 按makespan排序
        valid_columns.sort(key=lambda x: x.makespan)
        
        return valid_columns[:num_columns]
    
    def calculate_diversity_score(self, columns: List[ScheduleColumn]) -> float:
        """计算列的多样性评分"""
        if len(columns) <= 1:
            return 0.0
        
        unique_schedules = set()
        for column in columns:
            if column.feasible and column.schedule:
                schedule_key = tuple(sorted(column.schedule))
                unique_schedules.add(schedule_key)
        
        return len(unique_schedules) / len(columns)
    
    def get_generation_statistics(self, columns: List[ScheduleColumn]) -> Dict:
        """获取生成统计信息"""
        if not columns:
            return {
                'total_columns': 0,
                'feasible_columns': 0,
                'feasibility_rate': 0.0,
                'diversity_score': 0.0,
                'avg_decision_quality': 0.0
            }
        
        feasible_columns = [col for col in columns if col.feasible]
        
        return {
            'total_columns': len(columns),
            'feasible_columns': len(feasible_columns),
            'feasibility_rate': len(feasible_columns) / len(columns),
            'diversity_score': self.calculate_diversity_score(columns),
            'avg_decision_quality': np.mean([col.decision_quality for col in feasible_columns]) if feasible_columns else 0.0,
            'avg_makespan': np.mean([col.makespan for col in feasible_columns if col.makespan > 0]) if feasible_columns else 0.0
        }
