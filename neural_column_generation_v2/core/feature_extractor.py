#!/usr/bin/env python3
"""
Feature Extractor
特征提取器 - 从FJSP实例中提取神经网络所需的特征
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional


class FeatureExtractor:
    """FJSP特征提取器"""
    
    def __init__(self, config):
        self.config = config
        self.job_feature_dim = config.model.job_feature_dim
        self.operation_feature_dim = config.model.operation_feature_dim
        self.machine_feature_dim = config.model.machine_feature_dim
    
    def extract_features(self, instance: Dict) -> Dict[str, torch.Tensor]:
        """从FJSP实例中提取所有特征"""
        job_lengths = instance['job_length']
        processing_times = np.array(instance['processing_times'])
        n_jobs = instance['n_jobs']
        n_machines = instance['n_machines']
        n_operations = instance['n_operations']
        
        # 提取各类特征
        job_features = self._extract_job_features(job_lengths, processing_times, n_jobs, n_operations)
        operation_features = self._extract_operation_features(processing_times, job_lengths, n_jobs, n_machines, n_operations)
        machine_features = self._extract_machine_features(processing_times, n_machines, n_operations)
        
        return {
            'job_features': torch.FloatTensor(job_features),
            'operation_features': torch.FloatTensor(operation_features),
            'machine_features': torch.FloatTensor(machine_features),
            'processing_matrix': torch.FloatTensor(processing_times),
            'job_lengths': job_lengths
        }
    
    def _extract_job_features(self, job_lengths: List[int], processing_times: np.ndarray, 
                             n_jobs: int, n_operations: int) -> List[List[float]]:
        """提取作业特征"""
        job_features = []
        
        for job_id, n_ops in enumerate(job_lengths):
            # 计算作业的操作范围
            op_start = sum(job_lengths[:job_id])
            op_end = op_start + n_ops
            
            # 基本特征
            job_id_normalized = job_id / max(n_jobs - 1, 1)
            n_ops_normalized = n_ops / n_operations
            n_ops_relative = n_ops / max(job_lengths)
            
            # 工作负载特征
            total_workload = 0
            min_workload = 0
            max_workload = 0
            
            for op_idx in range(op_start, min(op_end, len(processing_times))):
                valid_times = [t for t in processing_times[op_idx] if t > 0]
                if valid_times:
                    min_time = min(valid_times)
                    max_time = max(valid_times)
                    avg_time = np.mean(valid_times)
                    
                    total_workload += avg_time
                    min_workload += min_time
                    max_workload += max_time
            
            # 归一化工作负载
            total_workload_normalized = total_workload / 1000.0
            min_workload_normalized = min_workload / 1000.0
            max_workload_normalized = max_workload / 1000.0
            
            # 工作负载密度
            workload_density = total_workload / max(n_ops, 1) / 50.0
            
            # 作业复杂度（处理时间方差）
            complexity = 0.0
            if op_end > op_start:
                all_times = []
                for op_idx in range(op_start, min(op_end, len(processing_times))):
                    valid_times = [t for t in processing_times[op_idx] if t > 0]
                    all_times.extend(valid_times)
                
                if all_times:
                    complexity = np.std(all_times) / max(np.mean(all_times), 1.0)
            
            # 组合特征向量 (10维)
            job_feat = [
                job_id_normalized,           # 0: 作业ID
                n_ops_normalized,            # 1: 操作数（全局归一化）
                n_ops_relative,              # 2: 操作数（相对归一化）
                total_workload_normalized,   # 3: 总工作负载
                min_workload_normalized,     # 4: 最小工作负载
                max_workload_normalized,     # 5: 最大工作负载
                workload_density,            # 6: 工作负载密度
                complexity,                  # 7: 作业复杂度
                0.0,                        # 8: 预留特征1
                0.0                         # 9: 预留特征2
            ]
            
            job_features.append(job_feat)
        
        return job_features
    
    def _extract_operation_features(self, processing_times: np.ndarray, job_lengths: List[int],
                                   n_jobs: int, n_machines: int, n_operations: int) -> List[List[float]]:
        """提取操作特征"""
        operation_features = []
        
        for op_id in range(n_operations):
            if op_id >= len(processing_times):
                # 填充默认特征
                op_feat = [0.0] * self.operation_feature_dim
                operation_features.append(op_feat)
                continue
            
            proc_times = processing_times[op_id]
            
            # 找到操作所属的作业
            job_id = 0
            op_in_job = op_id
            for j, n_ops in enumerate(job_lengths):
                if op_in_job < n_ops:
                    job_id = j
                    break
                op_in_job -= n_ops
            
            # 基本特征
            op_id_normalized = op_id / max(n_operations - 1, 1)
            job_id_normalized = job_id / max(n_jobs - 1, 1)
            op_in_job_normalized = op_in_job / max(job_lengths[job_id] - 1, 1)
            
            # 处理时间特征
            valid_times = [t for t in proc_times if t > 0]
            if valid_times:
                min_time = min(valid_times)
                max_time = max(valid_times)
                avg_time = np.mean(valid_times)
                std_time = np.std(valid_times) if len(valid_times) > 1 else 0.0
                n_machines_available = len(valid_times)
                
                # 归一化处理时间特征
                min_time_normalized = min_time / 100.0
                max_time_normalized = max_time / 100.0
                avg_time_normalized = avg_time / 100.0
                std_time_normalized = std_time / 100.0
                time_range_normalized = (max_time - min_time) / 100.0
                
                # 机器可用性
                machine_availability = n_machines_available / n_machines
                
                # 处理时间效率（最短时间占比）
                time_efficiency = min_time / max(avg_time, 1.0)
                
                # 选择灵活性（可用机器数的相对重要性）
                selection_flexibility = n_machines_available / n_machines
                
            else:
                # 无效操作的默认值
                min_time_normalized = max_time_normalized = avg_time_normalized = 0.0
                std_time_normalized = time_range_normalized = 0.0
                machine_availability = time_efficiency = selection_flexibility = 0.0
            
            # 组合特征向量 (15维)
            op_feat = [
                op_id_normalized,           # 0: 操作ID
                job_id_normalized,          # 1: 所属作业ID
                op_in_job_normalized,       # 2: 作业内操作位置
                min_time_normalized,        # 3: 最短处理时间
                max_time_normalized,        # 4: 最长处理时间
                avg_time_normalized,        # 5: 平均处理时间
                std_time_normalized,        # 6: 处理时间标准差
                time_range_normalized,      # 7: 处理时间范围
                machine_availability,       # 8: 机器可用性
                time_efficiency,            # 9: 时间效率
                selection_flexibility,      # 10: 选择灵活性
                0.0,                       # 11: 预留特征1
                0.0,                       # 12: 预留特征2
                0.0,                       # 13: 预留特征3
                0.0                        # 14: 预留特征4
            ]
            
            operation_features.append(op_feat)
        
        return operation_features
    
    def _extract_machine_features(self, processing_times: np.ndarray, 
                                 n_machines: int, n_operations: int) -> List[List[float]]:
        """提取机器特征"""
        machine_features = []
        
        for machine_id in range(n_machines):
            # 基本特征
            machine_id_normalized = machine_id / max(n_machines - 1, 1)
            
            # 工作负载特征
            total_load = 0
            n_operations_available = 0
            processing_times_list = []
            
            for op_id in range(n_operations):
                if (op_id < len(processing_times) and 
                    machine_id < len(processing_times[op_id])):
                    proc_time = processing_times[op_id][machine_id]
                    if proc_time > 0:
                        total_load += proc_time
                        n_operations_available += 1
                        processing_times_list.append(proc_time)
            
            # 负载统计
            avg_load = total_load / max(n_operations_available, 1)
            operation_availability = n_operations_available / n_operations
            
            # 处理时间分布特征
            if processing_times_list:
                min_proc_time = min(processing_times_list)
                max_proc_time = max(processing_times_list)
                std_proc_time = np.std(processing_times_list)
                
                # 归一化
                total_load_normalized = total_load / 1000.0
                avg_load_normalized = avg_load / 100.0
                min_proc_time_normalized = min_proc_time / 100.0
                max_proc_time_normalized = max_proc_time / 100.0
                std_proc_time_normalized = std_proc_time / 100.0
            else:
                total_load_normalized = avg_load_normalized = 0.0
                min_proc_time_normalized = max_proc_time_normalized = 0.0
                std_proc_time_normalized = 0.0
            
            # 组合特征向量 (8维)
            machine_feat = [
                machine_id_normalized,      # 0: 机器ID
                total_load_normalized,      # 1: 总负载
                avg_load_normalized,        # 2: 平均负载
                operation_availability,     # 3: 操作可用性
                min_proc_time_normalized,   # 4: 最短处理时间
                max_proc_time_normalized,   # 5: 最长处理时间
                std_proc_time_normalized,   # 6: 处理时间标准差
                0.0                        # 7: 预留特征
            ]
            
            machine_features.append(machine_feat)
        
        return machine_features
    
    def extract_batch_features(self, instances: List[Dict]) -> Dict[str, torch.Tensor]:
        """批量提取特征"""
        batch_features = {
            'job_features': [],
            'operation_features': [],
            'machine_features': [],
            'processing_matrix': [],
            'job_lengths': []
        }
        
        for instance in instances:
            features = self.extract_features(instance)
            for key in batch_features:
                if key == 'job_lengths':
                    batch_features[key].append(features[key])
                else:
                    batch_features[key].append(features[key])
        
        # 转换为批次张量（除了job_lengths）
        for key in ['job_features', 'operation_features', 'machine_features', 'processing_matrix']:
            if batch_features[key]:
                batch_features[key] = torch.stack(batch_features[key])
        
        return batch_features
    
    def get_feature_statistics(self, instances: List[Dict]) -> Dict:
        """获取特征统计信息"""
        all_job_features = []
        all_operation_features = []
        all_machine_features = []
        
        for instance in instances:
            features = self.extract_features(instance)
            all_job_features.extend(features['job_features'].numpy())
            all_operation_features.extend(features['operation_features'].numpy())
            all_machine_features.extend(features['machine_features'].numpy())
        
        def compute_stats(feature_array):
            if not feature_array:
                return {'mean': 0, 'std': 0, 'min': 0, 'max': 0}
            
            arr = np.array(feature_array)
            return {
                'mean': np.mean(arr, axis=0).tolist(),
                'std': np.std(arr, axis=0).tolist(),
                'min': np.min(arr, axis=0).tolist(),
                'max': np.max(arr, axis=0).tolist()
            }
        
        return {
            'job_features': compute_stats(all_job_features),
            'operation_features': compute_stats(all_operation_features),
            'machine_features': compute_stats(all_machine_features),
            'num_instances': len(instances)
        }
