#!/usr/bin/env python3
"""
Schedule Evaluator
调度评估器 - 评估调度方案的质量和可行性
"""

import numpy as np
from typing import List, Tuple, Dict, Optional
from .neural_model import ScheduleColumn


class ScheduleEvaluator:
    """调度评估器"""
    
    def __init__(self, config=None):
        self.config = config
    
    def evaluate_schedule(self, schedule: List[Tuple[int, int]], instance: Dict) -> <PERSON>ple[float, bool, Dict]:
        """评估单个调度方案"""
        if not schedule:
            return float('inf'), False, self._get_empty_metrics()
        
        processing_times = np.array(instance['processing_times'])
        job_lengths = instance['job_length']
        n_machines = instance['n_machines']
        n_operations = instance['n_operations']
        
        # 检查基本有效性
        if not self._is_schedule_valid(schedule, n_operations, n_machines):
            return float('inf'), False, self._get_empty_metrics()
        
        # 检查作业顺序约束
        if not self._check_job_precedence_constraints(schedule, job_lengths):
            return float('inf'), False, self._get_empty_metrics()
        
        # 检查处理时间有效性
        if not self._check_processing_time_validity(schedule, processing_times):
            return float('inf'), False, self._get_empty_metrics()
        
        # 计算makespan和其他指标
        makespan, metrics = self._calculate_detailed_metrics(schedule, processing_times, job_lengths, n_machines)
        
        return makespan, True, metrics
    
    def evaluate_column(self, column: ScheduleColumn, instance: Dict) -> ScheduleColumn:
        """评估调度列并更新其指标"""
        makespan, feasible, metrics = self.evaluate_schedule(column.schedule, instance)
        
        # 更新列的属性
        column.makespan = makespan
        column.feasible = feasible
        column.quality_score = self._calculate_quality_score(makespan, metrics, instance)
        
        return column
    
    def evaluate_columns(self, columns: List[ScheduleColumn], instance: Dict) -> List[ScheduleColumn]:
        """批量评估调度列"""
        evaluated_columns = []
        
        for column in columns:
            evaluated_column = self.evaluate_column(column, instance)
            evaluated_columns.append(evaluated_column)
        
        return evaluated_columns
    
    def _is_schedule_valid(self, schedule: List[Tuple[int, int]], 
                          n_operations: int, n_machines: int) -> bool:
        """检查调度的基本有效性"""
        if len(schedule) != n_operations:
            return False
        
        scheduled_ops = set()
        for op_id, machine_id in schedule:
            # 检查操作ID范围
            if op_id < 0 or op_id >= n_operations:
                return False
            
            # 检查机器ID范围
            if machine_id < 0 or machine_id >= n_machines:
                return False
            
            # 检查操作是否重复
            if op_id in scheduled_ops:
                return False
            
            scheduled_ops.add(op_id)
        
        # 检查是否所有操作都被调度
        return len(scheduled_ops) == n_operations
    
    def _check_job_precedence_constraints(self, schedule: List[Tuple[int, int]], 
                                         job_lengths: List[int]) -> bool:
        """检查作业顺序约束"""
        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_lengths):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops
        
        # 跟踪每个作业的进度
        job_progress = [0] * len(job_lengths)
        
        for op_id, machine_id in schedule:
            # 找到操作所属的作业
            target_job = None
            for job_id, operations in enumerate(job_operations):
                if op_id in operations:
                    target_job = job_id
                    break
            
            if target_job is None:
                return False
            
            # 检查操作在作业中的位置
            op_index_in_job = job_operations[target_job].index(op_id)
            
            # 检查是否满足顺序约束
            if job_progress[target_job] != op_index_in_job:
                return False
            
            # 更新作业进度
            job_progress[target_job] += 1
        
        return True
    
    def _check_processing_time_validity(self, schedule: List[Tuple[int, int]], 
                                       processing_times: np.ndarray) -> bool:
        """检查处理时间有效性"""
        for op_id, machine_id in schedule:
            if (op_id >= processing_times.shape[0] or 
                machine_id >= processing_times.shape[1] or
                processing_times[op_id, machine_id] <= 0):
                return False
        
        return True
    
    def _calculate_detailed_metrics(self, schedule: List[Tuple[int, int]], 
                                   processing_times: np.ndarray,
                                   job_lengths: List[int], 
                                   n_machines: int) -> Tuple[float, Dict]:
        """计算详细的调度指标"""
        # 计算机器时间
        machine_times = [0] * n_machines
        for op_id, machine_id in schedule:
            machine_times[machine_id] += processing_times[op_id, machine_id]
        
        makespan = max(machine_times)
        total_processing_time = sum(machine_times)
        
        # 计算各种指标
        metrics = {
            'makespan': makespan,
            'total_processing_time': total_processing_time,
            'machine_times': machine_times.copy(),
            
            # 负载均衡指标
            'machine_utilization': np.mean(machine_times) / makespan if makespan > 0 else 0,
            'load_balance': 1 - (np.std(machine_times) / np.mean(machine_times)) if np.mean(machine_times) > 0 else 0,
            'max_machine_load': max(machine_times),
            'min_machine_load': min(machine_times),
            'load_variance': np.var(machine_times),
            
            # 效率指标
            'efficiency': total_processing_time / (makespan * n_machines) if makespan > 0 else 0,
            'idle_time': makespan * n_machines - total_processing_time,
            'idle_time_ratio': (makespan * n_machines - total_processing_time) / (makespan * n_machines) if makespan > 0 else 0,
            
            # 作业完成时间
            'job_completion_times': self._calculate_job_completion_times(schedule, processing_times, job_lengths),
            
            # 决策质量
            'decision_quality': self._calculate_decision_quality(schedule, processing_times),
            'optimal_machine_selections': self._count_optimal_machine_selections(schedule, processing_times)
        }
        
        return makespan, metrics
    
    def _calculate_job_completion_times(self, schedule: List[Tuple[int, int]], 
                                       processing_times: np.ndarray,
                                       job_lengths: List[int]) -> List[float]:
        """计算每个作业的完成时间"""
        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_lengths):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops
        
        # 模拟调度执行
        machine_available_time = [0] * processing_times.shape[1]
        job_completion_times = [0] * len(job_lengths)
        operation_start_times = {}
        operation_end_times = {}
        
        # 按调度顺序处理操作
        for op_id, machine_id in schedule:
            proc_time = processing_times[op_id, machine_id]
            
            # 找到操作所属作业
            job_id = None
            for j, operations in enumerate(job_operations):
                if op_id in operations:
                    job_id = j
                    break
            
            # 计算开始时间（考虑机器可用时间和作业前序约束）
            machine_ready_time = machine_available_time[machine_id]
            
            # 作业前序约束
            job_ready_time = 0
            op_index_in_job = job_operations[job_id].index(op_id)
            if op_index_in_job > 0:
                prev_op = job_operations[job_id][op_index_in_job - 1]
                if prev_op in operation_end_times:
                    job_ready_time = operation_end_times[prev_op]
            
            start_time = max(machine_ready_time, job_ready_time)
            end_time = start_time + proc_time
            
            operation_start_times[op_id] = start_time
            operation_end_times[op_id] = end_time
            machine_available_time[machine_id] = end_time
            
            # 更新作业完成时间
            job_completion_times[job_id] = max(job_completion_times[job_id], end_time)
        
        return job_completion_times
    
    def _calculate_decision_quality(self, schedule: List[Tuple[int, int]], 
                                   processing_times: np.ndarray) -> float:
        """计算决策质量（机器选择的优化程度）"""
        if not schedule:
            return 0.0
        
        total_efficiency = 0.0
        
        for op_id, machine_id in schedule:
            # 获取该操作在所有机器上的处理时间
            valid_times = [processing_times[op_id, m] for m in range(processing_times.shape[1]) 
                          if processing_times[op_id, m] > 0]
            
            if valid_times:
                min_time = min(valid_times)
                max_time = max(valid_times)
                actual_time = processing_times[op_id, machine_id]
                
                # 计算相对效率
                if max_time > min_time:
                    efficiency = (max_time - actual_time) / (max_time - min_time)
                else:
                    efficiency = 1.0  # 所有机器处理时间相同
                
                total_efficiency += efficiency
        
        return total_efficiency / len(schedule)
    
    def _count_optimal_machine_selections(self, schedule: List[Tuple[int, int]], 
                                         processing_times: np.ndarray) -> int:
        """统计最优机器选择的数量"""
        optimal_count = 0
        
        for op_id, machine_id in schedule:
            # 找到最优机器（处理时间最短）
            valid_machines = [m for m in range(processing_times.shape[1]) 
                             if processing_times[op_id, m] > 0]
            
            if valid_machines:
                optimal_machine = min(valid_machines, 
                                    key=lambda m: processing_times[op_id, m])
                
                if machine_id == optimal_machine:
                    optimal_count += 1
        
        return optimal_count
    
    def _calculate_quality_score(self, makespan: float, metrics: Dict, instance: Dict) -> float:
        """计算综合质量评分"""
        if makespan == float('inf'):
            return 0.0
        
        # 基于makespan的基础分数（越小越好）
        base_score = 1.0 / (1.0 + makespan / 100.0)
        
        # 负载均衡奖励
        load_balance_bonus = metrics.get('load_balance', 0) * 0.2
        
        # 决策质量奖励
        decision_quality_bonus = metrics.get('decision_quality', 0) * 0.3
        
        # 效率奖励
        efficiency_bonus = metrics.get('efficiency', 0) * 0.1
        
        total_score = base_score + load_balance_bonus + decision_quality_bonus + efficiency_bonus
        
        return min(total_score, 1.0)  # 限制在[0, 1]范围内
    
    def _get_empty_metrics(self) -> Dict:
        """获取空的指标字典"""
        return {
            'makespan': float('inf'),
            'total_processing_time': 0,
            'machine_times': [],
            'machine_utilization': 0,
            'load_balance': 0,
            'max_machine_load': 0,
            'min_machine_load': 0,
            'load_variance': 0,
            'efficiency': 0,
            'idle_time': 0,
            'idle_time_ratio': 0,
            'job_completion_times': [],
            'decision_quality': 0,
            'optimal_machine_selections': 0
        }
    
    def compare_schedules(self, schedule1: List[Tuple[int, int]], 
                         schedule2: List[Tuple[int, int]], 
                         instance: Dict) -> Dict:
        """比较两个调度方案"""
        makespan1, feasible1, metrics1 = self.evaluate_schedule(schedule1, instance)
        makespan2, feasible2, metrics2 = self.evaluate_schedule(schedule2, instance)
        
        comparison = {
            'schedule1': {
                'makespan': makespan1,
                'feasible': feasible1,
                'metrics': metrics1
            },
            'schedule2': {
                'makespan': makespan2,
                'feasible': feasible2,
                'metrics': metrics2
            }
        }
        
        # 确定更好的调度
        if feasible1 and feasible2:
            if makespan1 < makespan2:
                comparison['better'] = 'schedule1'
                comparison['improvement'] = (makespan2 - makespan1) / makespan2 * 100
            elif makespan2 < makespan1:
                comparison['better'] = 'schedule2'
                comparison['improvement'] = (makespan1 - makespan2) / makespan1 * 100
            else:
                comparison['better'] = 'tie'
                comparison['improvement'] = 0.0
        elif feasible1:
            comparison['better'] = 'schedule1'
            comparison['improvement'] = float('inf')
        elif feasible2:
            comparison['better'] = 'schedule2'
            comparison['improvement'] = float('inf')
        else:
            comparison['better'] = 'neither'
            comparison['improvement'] = 0.0
        
        return comparison
    
    def get_evaluation_summary(self, columns: List[ScheduleColumn], instance: Dict) -> Dict:
        """获取评估摘要"""
        if not columns:
            return {'total_columns': 0, 'feasible_columns': 0, 'feasibility_rate': 0.0}
        
        feasible_columns = [col for col in columns if col.feasible]
        
        summary = {
            'total_columns': len(columns),
            'feasible_columns': len(feasible_columns),
            'feasibility_rate': len(feasible_columns) / len(columns),
        }
        
        if feasible_columns:
            makespans = [col.makespan for col in feasible_columns]
            decision_qualities = [col.decision_quality for col in feasible_columns]
            quality_scores = [col.quality_score for col in feasible_columns]
            
            summary.update({
                'best_makespan': min(makespans),
                'worst_makespan': max(makespans),
                'avg_makespan': np.mean(makespans),
                'makespan_std': np.std(makespans),
                'avg_decision_quality': np.mean(decision_qualities),
                'avg_quality_score': np.mean(quality_scores)
            })
        
        return summary
