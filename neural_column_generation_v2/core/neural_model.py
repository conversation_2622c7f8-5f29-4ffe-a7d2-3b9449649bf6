#!/usr/bin/env python3
"""
Neural Column Generation Model
神经列生成模型 - 核心神经网络架构
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Tuple, Dict, Optional
from dataclasses import dataclass


@dataclass
class ScheduleColumn:
    """调度列数据结构"""
    schedule: List[Tuple[int, int]]  # (operation_id, machine_id) pairs
    makespan: float
    feasible: bool
    quality_score: float
    decision_quality: float = 0.0


class ProcessingTimeAwareAttention(nn.Module):
    """处理时间感知的注意力机制"""
    
    def __init__(self, d_model: int, n_heads: int):
        super().__init__()
        self.d_model = d_model
        self.n_heads = n_heads
        self.head_dim = d_model // n_heads
        
        assert d_model % n_heads == 0, "d_model must be divisible by n_heads"
        
        self.q_linear = nn.Linear(d_model, d_model)
        self.k_linear = nn.Linear(d_model, d_model)
        self.v_linear = nn.Linear(d_model, d_model)
        self.out_linear = nn.Linear(d_model, d_model)
        
        # 处理时间编码器
        self.time_encoder = nn.Sequential(
            nn.Linear(1, 32),
            nn.ReLU(),
            nn.Linear(32, self.head_dim),
            nn.Tanh()
        )
        
    def forward(self, query, key, value, processing_times=None, mask=None):
        batch_size, seq_len = query.size(0), query.size(1)
        
        # 线性变换
        Q = self.q_linear(query).view(batch_size, seq_len, self.n_heads, self.head_dim)
        K = self.k_linear(key).view(batch_size, seq_len, self.n_heads, self.head_dim)
        V = self.v_linear(value).view(batch_size, seq_len, self.n_heads, self.head_dim)
        
        # 转置以便计算注意力
        Q = Q.transpose(1, 2)  # (batch, n_heads, seq_len, head_dim)
        K = K.transpose(1, 2)
        V = V.transpose(1, 2)
        
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / np.sqrt(self.head_dim)
        
        # 如果有处理时间信息，加入时间偏置
        if processing_times is not None and processing_times.dim() == 2:
            # 简化的时间偏置处理
            time_weights = processing_times.unsqueeze(1).unsqueeze(-1)  # (batch, 1, seq_len, 1)
            time_weights = time_weights.expand(-1, self.n_heads, -1, seq_len)  # (batch, n_heads, seq_len, seq_len)
            # 使用简单的缩放而不是复杂的编码
            time_bias = time_weights * 0.01  # 小的时间偏置
            scores = scores + time_bias
        
        # 应用掩码
        if mask is not None:
            scores = scores.masked_fill(mask == 0, -1e9)
        
        # Softmax
        attention_weights = F.softmax(scores, dim=-1)
        
        # 应用注意力权重
        context = torch.matmul(attention_weights, V)
        
        # 重新组织维度
        context = context.transpose(1, 2).contiguous().view(
            batch_size, seq_len, self.d_model
        )
        
        # 输出投影
        output = self.out_linear(context)
        
        return output, attention_weights


class MachineSelectionDecoder(nn.Module):
    """机器选择解码器"""
    
    def __init__(self, d_model: int):
        super().__init__()
        self.d_model = d_model
        
        # 操作选择网络
        self.operation_selector = nn.Sequential(
            nn.Linear(d_model + 10, d_model),
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model, d_model // 2),
            nn.ReLU(),
            nn.Linear(d_model // 2, 1)
        )
        
        # 机器选择网络 - 强化处理时间感知
        self.machine_selector = nn.Sequential(
            nn.Linear(d_model + 3, d_model // 2),  # +3 for time features
            nn.ReLU(),
            nn.Dropout(0.1),
            nn.Linear(d_model // 2, d_model // 4),
            nn.ReLU(),
            nn.Linear(d_model // 4, 1)
        )
        
        # 处理时间特征编码器
        self.time_feature_encoder = nn.Sequential(
            nn.Linear(3, 16),  # min_time, relative_efficiency, is_optimal
            nn.ReLU(),
            nn.Linear(16, 32),
            nn.ReLU(),
            nn.Linear(32, 16)
        )
    
    def forward(self, operation_embeddings, machine_embeddings, processing_matrix, job_lengths):
        """生成调度列"""
        batch_size = operation_embeddings.size(0)
        schedules = []
        
        for b in range(batch_size):
            schedule = self._generate_single_schedule(
                operation_embeddings[b],
                machine_embeddings[b], 
                processing_matrix[b],
                job_lengths
            )
            schedules.append(schedule)
        
        return schedules
    
    def _generate_single_schedule(self, op_embeddings, machine_embeddings, processing_matrix, job_lengths):
        """生成单个调度"""
        n_operations = op_embeddings.size(0)
        n_machines = machine_embeddings.size(0)
        
        schedule = []
        scheduled_ops = set()
        job_progress = [0] * len(job_lengths)
        decision_quality_scores = []
        
        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_lengths):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops
        
        # 逐步生成调度
        for step in range(n_operations):
            # 获取可调度操作
            available_ops = self._get_available_operations(
                job_operations, job_progress, scheduled_ops
            )
            
            if not available_ops:
                break
            
            # 选择操作
            selected_op = self._select_operation(available_ops, op_embeddings, processing_matrix)
            
            # 选择机器（关键改进）
            selected_machine, decision_score = self._select_machine_improved(
                selected_op, machine_embeddings, processing_matrix
            )
            
            if selected_machine is not None:
                schedule.append((selected_op, selected_machine))
                scheduled_ops.add(selected_op)
                decision_quality_scores.append(decision_score)
                
                # 更新作业进度
                for job_id, ops in enumerate(job_operations):
                    if selected_op in ops:
                        job_progress[job_id] += 1
                        break
        
        # 计算平均决策质量
        avg_decision_quality = np.mean(decision_quality_scores) if decision_quality_scores else 0.0
        
        return ScheduleColumn(
            schedule=schedule,
            makespan=0.0,  # 将在后续计算
            feasible=len(scheduled_ops) == n_operations,
            quality_score=0.0,  # 将在后续计算
            decision_quality=avg_decision_quality
        )
    
    def _select_machine_improved(self, operation_id, machine_embeddings, processing_matrix):
        """改进的机器选择逻辑"""
        # 获取可用机器和处理时间
        valid_machines = []
        processing_times = []
        
        for m in range(processing_matrix.size(1)):
            proc_time = processing_matrix[operation_id, m]
            if proc_time > 0:
                valid_machines.append(m)
                processing_times.append(proc_time.item())
        
        if not valid_machines:
            return None, 0.0
        
        if len(valid_machines) == 1:
            return valid_machines[0], 1.0
        
        # 计算处理时间特征
        min_time = min(processing_times)
        max_time = max(processing_times)
        time_range = max_time - min_time if max_time > min_time else 1.0
        
        machine_scores = []
        
        for i, machine_id in enumerate(valid_machines):
            proc_time = processing_times[i]
            
            # 处理时间特征
            normalized_time = proc_time / max(max_time, 1.0)
            relative_efficiency = (max_time - proc_time) / max(time_range, 1.0)
            is_optimal = 1.0 if proc_time == min_time else 0.0
            
            # 时间特征编码
            time_features = torch.tensor([normalized_time, relative_efficiency, is_optimal], 
                                       dtype=torch.float32, device=machine_embeddings.device)
            
            # 结合机器嵌入和时间特征
            machine_embedding = machine_embeddings[machine_id]
            combined_input = torch.cat([machine_embedding, time_features], dim=0)
            
            # 神经网络评分
            neural_score = self.machine_selector(combined_input.unsqueeze(0)).squeeze().item()
            
            # 强烈的时间偏置 - 这是关键改进
            time_bias = -20.0 * normalized_time  # 更强的偏向最短时间
            optimal_bonus = 10.0 if is_optimal else 0.0  # 更大的最优奖励
            
            final_score = neural_score + time_bias + optimal_bonus
            
            machine_scores.append((final_score, machine_id, relative_efficiency))
        
        # 选择评分最高的机器
        machine_scores.sort(reverse=True, key=lambda x: x[0])
        selected_machine = machine_scores[0][1]
        decision_quality = machine_scores[0][2]
        
        return selected_machine, decision_quality
    
    def _select_operation(self, available_operations, op_embeddings, processing_matrix):
        """选择操作"""
        if len(available_operations) == 1:
            return available_operations[0]
        
        # 基于最短处理时间的操作选择
        op_scores = []
        for op_id in available_operations:
            valid_times = [processing_matrix[op_id, m].item() 
                          for m in range(processing_matrix.size(1)) 
                          if processing_matrix[op_id, m] > 0]
            
            if valid_times:
                min_time = min(valid_times)
                op_scores.append((min_time, op_id))
        
        if op_scores:
            op_scores.sort()
            return op_scores[0][1]
        
        return available_operations[0]
    
    def _get_available_operations(self, job_operations, job_progress, scheduled_ops):
        """获取可调度的操作"""
        available_ops = []
        for job_id, operations in enumerate(job_operations):
            if job_progress[job_id] < len(operations):
                next_op = operations[job_progress[job_id]]
                if next_op not in scheduled_ops:
                    available_ops.append(next_op)
        return available_ops


class NeuralColumnModel(nn.Module):
    """神经列生成模型主类"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.d_model = config.model.d_model
        
        # 特征编码器
        self.job_encoder = nn.Sequential(
            nn.Linear(config.model.job_feature_dim, self.d_model // 2),
            nn.ReLU(),
            nn.Linear(self.d_model // 2, self.d_model),
            nn.LayerNorm(self.d_model)
        )
        
        self.operation_encoder = nn.Sequential(
            nn.Linear(config.model.operation_feature_dim, self.d_model // 2),
            nn.ReLU(),
            nn.Linear(self.d_model // 2, self.d_model),
            nn.LayerNorm(self.d_model)
        )
        
        self.machine_encoder = nn.Sequential(
            nn.Linear(config.model.machine_feature_dim, self.d_model // 2),
            nn.ReLU(),
            nn.Linear(self.d_model // 2, self.d_model),
            nn.LayerNorm(self.d_model)
        )
        
        # Transformer层
        self.attention_layers = nn.ModuleList([
            ProcessingTimeAwareAttention(self.d_model, config.model.n_heads)
            for _ in range(config.model.n_layers)
        ])
        
        self.layer_norms = nn.ModuleList([
            nn.LayerNorm(self.d_model) for _ in range(config.model.n_layers)
        ])
        
        # 解码器
        self.decoder = MachineSelectionDecoder(self.d_model)
        
        # 初始化权重
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in self.modules():
            if isinstance(module, nn.Linear):
                nn.init.xavier_uniform_(module.weight)
                if module.bias is not None:
                    nn.init.zeros_(module.bias)
            elif isinstance(module, nn.LayerNorm):
                nn.init.ones_(module.weight)
                nn.init.zeros_(module.bias)
    
    def forward(self, job_features, operation_features, machine_features, 
                processing_matrix, job_lengths, num_columns=None):
        """前向传播"""
        if num_columns is None:
            num_columns = self.config.model.num_columns
        
        # 编码特征
        job_embeddings = self.job_encoder(job_features)
        operation_embeddings = self.operation_encoder(operation_features)
        machine_embeddings = self.machine_encoder(machine_features)
        
        # Transformer增强
        for attention, layer_norm in zip(self.attention_layers, self.layer_norms):
            # 操作嵌入增强
            enhanced_ops, _ = attention(
                operation_embeddings, operation_embeddings, operation_embeddings,
                processing_times=processing_matrix.mean(dim=-1)  # 平均处理时间作为时间信息
            )
            operation_embeddings = layer_norm(operation_embeddings + enhanced_ops)
            
            # 机器嵌入增强
            enhanced_machines, _ = attention(
                machine_embeddings, machine_embeddings, machine_embeddings
            )
            machine_embeddings = layer_norm(machine_embeddings + enhanced_machines)
        
        # 生成多个列
        all_columns = []
        for _ in range(num_columns):
            columns = self.decoder(
                operation_embeddings,
                machine_embeddings,
                processing_matrix,
                job_lengths
            )
            all_columns.extend(columns)
        
        return all_columns
