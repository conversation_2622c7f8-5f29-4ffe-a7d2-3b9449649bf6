#!/usr/bin/env python3
"""
Neural Column Generation Configuration
神经列生成系统配置文件
"""

import os
from dataclasses import dataclass
from typing import List, Dict, Any, Optional


@dataclass
class ModelConfig:
    """神经网络模型配置"""
    # 模型架构参数
    d_model: int = 256                    # 模型维度
    n_heads: int = 8                      # 注意力头数
    n_layers: int = 4                     # Transformer层数
    dropout: float = 0.1                  # Dropout率
    
    # 特征维度
    job_feature_dim: int = 10             # 作业特征维度
    operation_feature_dim: int = 15       # 操作特征维度
    machine_feature_dim: int = 8          # 机器特征维度
    
    # 列生成参数
    num_columns: int = 5                  # 生成列数
    max_iterations: int = 100             # 最大迭代次数
    diversity_weight: float = 0.1         # 多样性权重


@dataclass
class TrainingConfig:
    """训练配置"""
    # 基本训练参数
    batch_size: int = 32                  # 批次大小
    num_epochs: int = 50                  # 训练轮数
    learning_rate: float = 0.001          # 学习率
    weight_decay: float = 0.01            # 权重衰减
    
    # 优化器参数
    optimizer: str = 'AdamW'              # 优化器类型
    scheduler: str = 'CosineAnnealingLR'  # 学习率调度器
    warmup_epochs: int = 5                # 预热轮数
    
    # 训练稳定性
    gradient_clip: float = 1.0            # 梯度裁剪
    patience: int = 10                    # 早停耐心值
    min_delta: float = 1e-4               # 最小改进阈值
    
    # 损失函数权重
    makespan_weight: float = 2.0          # makespan损失权重
    feasibility_weight: float = 1.0       # 可行性权重
    decision_quality_weight: float = 1.0  # 决策质量权重
    diversity_weight: float = 0.5         # 多样性权重


@dataclass
class DataConfig:
    """数据配置"""
    # 数据路径
    raw_data_dir: str = "../data"         # 原始数据目录
    processed_data_dir: str = "data/processed"  # 处理后数据目录
    model_save_dir: str = "data/models"   # 模型保存目录
    
    # 数据集划分
    train_ratio: float = 0.8              # 训练集比例
    val_ratio: float = 0.1                # 验证集比例
    test_ratio: float = 0.1               # 测试集比例
    
    # 数据增强
    augmentation_factor: int = 3          # 数据增强倍数
    noise_level: float = 0.05             # 噪声水平
    
    # 实例规模范围
    min_jobs: int = 3                     # 最小作业数
    max_jobs: int = 20                    # 最大作业数
    min_machines: int = 3                 # 最小机器数
    max_machines: int = 10                # 最大机器数


@dataclass
class EvaluationConfig:
    """评估配置"""
    # 基准算法
    baseline_algorithms: List[str] = None # 基线算法列表
    
    # 评估指标
    metrics: List[str] = None             # 评估指标列表
    
    # 测试参数
    num_test_instances: int = 50          # 测试实例数
    num_runs_per_instance: int = 5        # 每个实例运行次数
    timeout_seconds: int = 300            # 超时时间
    
    # 可视化
    generate_plots: bool = True           # 是否生成图表
    save_detailed_results: bool = True    # 是否保存详细结果
    
    def __post_init__(self):
        if self.baseline_algorithms is None:
            self.baseline_algorithms = ['SPT', 'FIFO', 'LPT', 'Random']
        
        if self.metrics is None:
            self.metrics = [
                'makespan', 'success_rate', 'generation_time',
                'decision_quality', 'diversity_score'
            ]


@dataclass
class SystemConfig:
    """系统配置"""
    # 设备配置
    device: str = 'auto'                  # 设备选择 ('auto', 'cpu', 'cuda')
    num_workers: int = 4                  # 数据加载工作进程数
    pin_memory: bool = True               # 是否固定内存
    
    # 日志配置
    log_level: str = 'INFO'               # 日志级别
    log_file: Optional[str] = None        # 日志文件路径
    tensorboard_dir: str = 'runs'         # TensorBoard日志目录
    
    # 检查点配置
    save_checkpoints: bool = True         # 是否保存检查点
    checkpoint_interval: int = 10         # 检查点保存间隔
    keep_last_n_checkpoints: int = 3      # 保留最近N个检查点
    
    # 随机种子
    random_seed: int = 42                 # 随机种子
    deterministic: bool = True            # 是否使用确定性算法


class Config:
    """主配置类"""
    
    def __init__(self):
        self.model = ModelConfig()
        self.training = TrainingConfig()
        self.data = DataConfig()
        self.evaluation = EvaluationConfig()
        self.system = SystemConfig()
        
        # 自动设置设备
        if self.system.device == 'auto':
            import torch
            self.system.device = 'cuda' if torch.cuda.is_available() else 'cpu'
        
        # 创建必要的目录
        self._create_directories()
    
    def _create_directories(self):
        """创建必要的目录"""
        directories = [
            self.data.processed_data_dir,
            self.data.model_save_dir,
            'results',
            'logs',
            self.system.tensorboard_dir
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'model': self.model.__dict__,
            'training': self.training.__dict__,
            'data': self.data.__dict__,
            'evaluation': self.evaluation.__dict__,
            'system': self.system.__dict__
        }
    
    def save(self, filepath: str):
        """保存配置到文件"""
        import json
        with open(filepath, 'w') as f:
            json.dump(self.to_dict(), f, indent=2)
    
    @classmethod
    def load(cls, filepath: str):
        """从文件加载配置"""
        import json
        with open(filepath, 'r') as f:
            config_dict = json.load(f)
        
        config = cls()
        
        # 更新配置
        for section, values in config_dict.items():
            if hasattr(config, section):
                section_config = getattr(config, section)
                for key, value in values.items():
                    if hasattr(section_config, key):
                        setattr(section_config, key, value)
        
        return config
    
    def update(self, **kwargs):
        """更新配置参数"""
        for key, value in kwargs.items():
            if '.' in key:
                # 支持嵌套更新，如 'model.d_model'
                section, param = key.split('.', 1)
                if hasattr(self, section):
                    section_config = getattr(self, section)
                    if hasattr(section_config, param):
                        setattr(section_config, param, value)
            else:
                if hasattr(self, key):
                    setattr(self, key, value)
    
    def validate(self):
        """验证配置参数"""
        errors = []
        
        # 验证模型配置
        if self.model.d_model <= 0:
            errors.append("model.d_model must be positive")
        
        if self.model.n_heads <= 0:
            errors.append("model.n_heads must be positive")
        
        if self.model.d_model % self.model.n_heads != 0:
            errors.append("model.d_model must be divisible by model.n_heads")
        
        # 验证训练配置
        if self.training.batch_size <= 0:
            errors.append("training.batch_size must be positive")
        
        if self.training.learning_rate <= 0:
            errors.append("training.learning_rate must be positive")
        
        # 验证数据配置
        ratio_sum = (self.data.train_ratio + 
                    self.data.val_ratio + 
                    self.data.test_ratio)
        if abs(ratio_sum - 1.0) > 1e-6:
            errors.append("Data split ratios must sum to 1.0")
        
        if errors:
            raise ValueError("Configuration validation failed:\n" + "\n".join(errors))
        
        return True
    
    def print_summary(self):
        """打印配置摘要"""
        print("=" * 60)
        print("Neural Column Generation Configuration Summary")
        print("=" * 60)
        
        print(f"\n🧠 Model Configuration:")
        print(f"  Architecture: Transformer ({self.model.n_layers} layers, {self.model.n_heads} heads)")
        print(f"  Model dimension: {self.model.d_model}")
        print(f"  Dropout rate: {self.model.dropout}")
        print(f"  Column generation: {self.model.num_columns} columns")
        
        print(f"\n🎯 Training Configuration:")
        print(f"  Epochs: {self.training.num_epochs}")
        print(f"  Batch size: {self.training.batch_size}")
        print(f"  Learning rate: {self.training.learning_rate}")
        print(f"  Optimizer: {self.training.optimizer}")
        print(f"  Scheduler: {self.training.scheduler}")
        
        print(f"\n📊 Data Configuration:")
        print(f"  Raw data: {self.data.raw_data_dir}")
        print(f"  Processed data: {self.data.processed_data_dir}")
        print(f"  Train/Val/Test split: {self.data.train_ratio:.1f}/{self.data.val_ratio:.1f}/{self.data.test_ratio:.1f}")
        print(f"  Instance scale: {self.data.min_jobs}-{self.data.max_jobs} jobs, {self.data.min_machines}-{self.data.max_machines} machines")
        
        print(f"\n🔬 Evaluation Configuration:")
        print(f"  Baseline algorithms: {', '.join(self.evaluation.baseline_algorithms)}")
        print(f"  Test instances: {self.evaluation.num_test_instances}")
        print(f"  Runs per instance: {self.evaluation.num_runs_per_instance}")
        
        print(f"\n⚙️  System Configuration:")
        print(f"  Device: {self.system.device}")
        print(f"  Workers: {self.system.num_workers}")
        print(f"  Random seed: {self.system.random_seed}")
        print(f"  Deterministic: {self.system.deterministic}")
        
        print("=" * 60)


# 默认配置实例
default_config = Config()

# 快速配置函数
def get_config(**kwargs) -> Config:
    """获取配置实例"""
    config = Config()
    if kwargs:
        config.update(**kwargs)
    config.validate()
    return config


def get_debug_config() -> Config:
    """获取调试配置"""
    return get_config(
        **{
            'training.num_epochs': 5,
            'training.batch_size': 4,
            'data.augmentation_factor': 1,
            'evaluation.num_test_instances': 5,
            'evaluation.num_runs_per_instance': 1
        }
    )


def get_production_config() -> Config:
    """获取生产配置"""
    return get_config(
        **{
            'model.d_model': 512,
            'model.n_heads': 16,
            'model.n_layers': 6,
            'training.num_epochs': 100,
            'training.batch_size': 64,
            'evaluation.num_test_instances': 100,
            'evaluation.num_runs_per_instance': 10
        }
    )


if __name__ == "__main__":
    # 测试配置
    config = get_config()
    config.print_summary()
    
    # 保存默认配置
    config.save("default_config.json")
    print(f"\n✅ Default configuration saved to default_config.json")
