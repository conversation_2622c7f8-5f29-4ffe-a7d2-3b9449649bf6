# Neural Column Generation Requirements

# Deep Learning Framework
torch>=1.12.0
torchvision>=0.13.0
torchaudio>=0.12.0

# Scientific Computing
numpy>=1.21.0
scipy>=1.7.0
pandas>=1.3.0

# Visualization
matplotlib>=3.5.0
seaborn>=0.11.0
plotly>=5.0.0

# Progress Bars and Logging
tqdm>=4.62.0
tensorboard>=2.8.0
wandb>=0.12.0

# Configuration and Utilities
pyyaml>=6.0
omegaconf>=2.1.0
hydra-core>=1.1.0

# Testing
pytest>=6.2.0
pytest-cov>=3.0.0

# Code Quality
black>=22.0.0
flake8>=4.0.0
isort>=5.10.0

# Optional: CUDA support (uncomment if using GPU)
# torch-geometric>=2.0.0
# torch-scatter>=2.0.0
# torch-sparse>=0.6.0

# Development Tools
jupyter>=1.0.0
ipython>=7.0.0
notebook>=6.0.0
