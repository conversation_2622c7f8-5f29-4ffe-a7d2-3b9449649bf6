#!/usr/bin/env python3
"""
Improved Training Script
改进的训练脚本 - 包含早停、学习率调度、更好的监控等功能
"""

import os
import sys
import time
import torch
import numpy as np
import matplotlib.pyplot as plt
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from config import get_config
from core.neural_model import NeuralColumnModel
from core.column_generator import ColumnGenerator
from core.schedule_evaluator import ScheduleEvaluator
from core.feature_extractor import FeatureExtractor
from training.data_generator import TrainingDataGenerator
from training.loss_functions import CombinedLoss


class ImprovedTrainer:
    """改进的训练器"""
    
    def __init__(self, model, config, device):
        self.model = model
        self.config = config
        self.device = device
        
        # 创建组件
        self.column_generator = ColumnGenerator(model, config)
        self.evaluator = ScheduleEvaluator()
        self.feature_extractor = FeatureExtractor(config)
        self.criterion = CombinedLoss(config)
        
        # 创建优化器
        self.optimizer = torch.optim.AdamW(
            model.parameters(),
            lr=config.training.learning_rate,
            weight_decay=config.training.weight_decay
        )
        
        # 创建学习率调度器
        self.scheduler = torch.optim.lr_scheduler.ReduceLROnPlateau(
            self.optimizer,
            mode='min',
            factor=0.5,
            patience=5,
            min_lr=1e-6,
            verbose=True
        )
        
        # 早停参数
        self.best_loss = float('inf')
        self.patience = 15
        self.no_improvement_count = 0
        self.min_delta = 0.001
        
        # 训练历史
        self.train_losses = []
        self.val_losses = []
        self.learning_rates = []
        self.loss_components_history = []
        
        # 创建输出目录
        self.output_dir = "outputs/improved_training"
        os.makedirs(self.output_dir, exist_ok=True)
        os.makedirs(os.path.join(self.output_dir, "plots"), exist_ok=True)
    
    def train_epoch(self, train_data):
        """训练一个epoch"""
        self.model.train()
        total_loss = 0.0
        epoch_loss_components = {
            'makespan_loss': 0.0,
            'feasibility_loss': 0.0,
            'diversity_loss': 0.0,
            'decision_quality_loss': 0.0
        }
        
        for i, instance in enumerate(train_data):
            self.optimizer.zero_grad()
            
            try:
                # 提取特征
                features = self.feature_extractor.extract_features(instance)
                
                # 生成列
                columns = self.column_generator.generate_columns(
                    features,
                    instance['job_length'],
                    num_columns=self.config.model.num_columns
                )
                
                # 评估列
                evaluated_columns = []
                for column in columns:
                    evaluated_column = self.evaluator.evaluate_column(column, instance)
                    evaluated_columns.append(evaluated_column)
                
                # 计算损失
                loss, loss_components = self.criterion(evaluated_columns, instance)
                
                # 反向传播
                loss.backward()
                
                # 梯度裁剪
                grad_norm = torch.nn.utils.clip_grad_norm_(
                    self.model.parameters(), 
                    self.config.training.gradient_clip
                )
                
                # 更新参数
                self.optimizer.step()
                
                total_loss += loss.item()
                
                # 累积损失组件
                for key, value in loss_components.items():
                    epoch_loss_components[key] += value
                
                if (i + 1) % 5 == 0:
                    print(f"    Batch {i+1}/{len(train_data)}: loss={loss.item():.4f}, grad_norm={grad_norm:.4f}")
                
            except Exception as e:
                print(f"    ⚠️ Error in batch {i+1}: {e}")
                continue
        
        # 计算平均损失
        avg_loss = total_loss / len(train_data) if train_data else 0.0
        
        # 计算平均损失组件
        for key in epoch_loss_components:
            epoch_loss_components[key] /= len(train_data) if train_data else 1.0
        
        return avg_loss, epoch_loss_components
    
    def validate(self, val_data):
        """验证"""
        if not val_data:
            return None, None
        
        self.model.eval()
        total_loss = 0.0
        val_loss_components = {
            'makespan_loss': 0.0,
            'feasibility_loss': 0.0,
            'diversity_loss': 0.0,
            'decision_quality_loss': 0.0
        }
        
        with torch.no_grad():
            for instance in val_data:
                try:
                    # 提取特征
                    features = self.feature_extractor.extract_features(instance)
                    
                    # 生成列
                    columns = self.column_generator.generate_columns(
                        features,
                        instance['job_length'],
                        num_columns=self.config.model.num_columns
                    )
                    
                    # 评估列
                    evaluated_columns = []
                    for column in columns:
                        evaluated_column = self.evaluator.evaluate_column(column, instance)
                        evaluated_columns.append(evaluated_column)
                    
                    # 计算损失
                    loss, loss_components = self.criterion(evaluated_columns, instance)
                    
                    total_loss += loss.item()
                    
                    # 累积损失组件
                    for key, value in loss_components.items():
                        val_loss_components[key] += value
                
                except Exception as e:
                    print(f"    ⚠️ Validation error: {e}")
                    continue
        
        # 计算平均损失
        avg_val_loss = total_loss / len(val_data) if val_data else 0.0
        
        # 计算平均损失组件
        for key in val_loss_components:
            val_loss_components[key] /= len(val_data) if val_data else 1.0
        
        return avg_val_loss, val_loss_components
    
    def check_early_stopping(self, val_loss):
        """检查早停"""
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.no_improvement_count = 0
            return False, True  # 不早停，是最佳模型
        else:
            self.no_improvement_count += 1
            if self.no_improvement_count >= self.patience:
                return True, False  # 早停，不是最佳模型
            return False, False  # 不早停，不是最佳模型
    
    def save_checkpoint(self, epoch, is_best=False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': self.model.state_dict(),
            'optimizer_state_dict': self.optimizer.state_dict(),
            'scheduler_state_dict': self.scheduler.state_dict(),
            'best_loss': self.best_loss,
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'learning_rates': self.learning_rates
        }
        
        # 保存最新检查点
        torch.save(checkpoint, os.path.join(self.output_dir, 'latest_checkpoint.pth'))
        
        # 保存最佳检查点
        if is_best:
            torch.save(checkpoint, os.path.join(self.output_dir, 'best_checkpoint.pth'))
            print(f"    💾 Best model saved at epoch {epoch}")
    
    def plot_training_progress(self):
        """绘制训练进度"""
        if len(self.train_losses) < 2:
            return
        
        fig, axes = plt.subplots(2, 2, figsize=(15, 10))
        fig.suptitle('Training Progress', fontsize=16)
        
        epochs = range(1, len(self.train_losses) + 1)
        
        # 1. 损失曲线
        axes[0, 0].plot(epochs, self.train_losses, 'b-', label='Training Loss', linewidth=2)
        if self.val_losses and len(self.val_losses) == len(self.train_losses):
            axes[0, 0].plot(epochs, self.val_losses, 'r-', label='Validation Loss', linewidth=2)
        axes[0, 0].set_title('Loss Curves')
        axes[0, 0].set_xlabel('Epoch')
        axes[0, 0].set_ylabel('Loss')
        axes[0, 0].legend()
        axes[0, 0].grid(True, alpha=0.3)
        
        # 2. 学习率
        if self.learning_rates:
            axes[0, 1].plot(epochs, self.learning_rates, 'g-', linewidth=2)
            axes[0, 1].set_title('Learning Rate')
            axes[0, 1].set_xlabel('Epoch')
            axes[0, 1].set_ylabel('Learning Rate')
            axes[0, 1].set_yscale('log')
            axes[0, 1].grid(True, alpha=0.3)
        
        # 3. 损失组件
        if self.loss_components_history:
            components = ['makespan_loss', 'feasibility_loss', 'diversity_loss', 'decision_quality_loss']
            colors = ['red', 'green', 'blue', 'orange']
            
            for component, color in zip(components, colors):
                values = [lc.get(component, 0) for lc in self.loss_components_history]
                if any(v > 0 for v in values):
                    axes[1, 0].plot(epochs, values, color=color, label=component.replace('_', ' ').title(), linewidth=2)
            
            axes[1, 0].set_title('Loss Components')
            axes[1, 0].set_xlabel('Epoch')
            axes[1, 0].set_ylabel('Loss')
            axes[1, 0].legend()
            axes[1, 0].grid(True, alpha=0.3)
        
        # 4. 改进趋势
        if len(self.train_losses) > 1:
            improvements = []
            for i in range(1, len(self.train_losses)):
                improvement = (self.train_losses[i-1] - self.train_losses[i]) / self.train_losses[i-1] * 100
                improvements.append(improvement)
            
            axes[1, 1].bar(range(2, len(self.train_losses) + 1), improvements, alpha=0.7)
            axes[1, 1].set_title('Loss Improvement per Epoch')
            axes[1, 1].set_xlabel('Epoch')
            axes[1, 1].set_ylabel('Improvement (%)')
            axes[1, 1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
            axes[1, 1].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(os.path.join(self.output_dir, 'plots', 'training_progress.png'), dpi=300, bbox_inches='tight')
        plt.close()
    
    def train(self, train_data, val_data=None, num_epochs=50):
        """训练模型"""
        print(f"🎯 Starting improved training for {num_epochs} epochs...")
        print(f"   Training instances: {len(train_data)}")
        print(f"   Validation instances: {len(val_data) if val_data else 0}")
        print(f"   Model parameters: {sum(p.numel() for p in self.model.parameters()):,}")
        
        start_time = time.time()
        
        for epoch in range(num_epochs):
            epoch_start = time.time()
            
            print(f"\n📊 Epoch {epoch + 1}/{num_epochs}")
            print("-" * 50)
            
            # 训练
            train_loss, train_loss_components = self.train_epoch(train_data)
            
            # 验证
            val_loss, val_loss_components = self.validate(val_data)
            
            # 更新学习率
            if val_loss is not None:
                self.scheduler.step(val_loss)
            else:
                self.scheduler.step(train_loss)
            
            # 记录历史
            self.train_losses.append(train_loss)
            if val_loss is not None:
                self.val_losses.append(val_loss)
            self.learning_rates.append(self.optimizer.param_groups[0]['lr'])
            self.loss_components_history.append(train_loss_components)
            
            # 打印进度
            epoch_time = time.time() - epoch_start
            print(f"  📈 Train Loss: {train_loss:.4f}")
            if val_loss is not None:
                print(f"  📉 Val Loss: {val_loss:.4f}")
            print(f"  🔧 Learning Rate: {self.optimizer.param_groups[0]['lr']:.6f}")
            print(f"  ⏱️ Epoch Time: {epoch_time:.2f}s")
            
            # 打印损失组件
            print(f"  📊 Loss Components:")
            for key, value in train_loss_components.items():
                print(f"    {key}: {value:.4f}")
            
            # 检查早停
            if val_loss is not None:
                should_stop, is_best = self.check_early_stopping(val_loss)
                
                # 保存检查点
                self.save_checkpoint(epoch, is_best)
                
                if should_stop:
                    print(f"⏹️ Early stopping triggered after {epoch + 1} epochs")
                    print(f"   No improvement for {self.no_improvement_count} epochs")
                    break
            else:
                # 没有验证集时，定期保存
                if (epoch + 1) % 10 == 0:
                    self.save_checkpoint(epoch)
            
            # 绘制训练进度
            if (epoch + 1) % 5 == 0:
                self.plot_training_progress()
        
        # 训练完成
        total_time = time.time() - start_time
        print(f"\n✅ Training completed!")
        print(f"   Total time: {total_time:.2f}s")
        print(f"   Best validation loss: {self.best_loss:.4f}")
        print(f"   Final learning rate: {self.optimizer.param_groups[0]['lr']:.6f}")
        
        # 生成最终图表
        self.plot_training_progress()
        
        return {
            'train_losses': self.train_losses,
            'val_losses': self.val_losses,
            'learning_rates': self.learning_rates,
            'best_loss': self.best_loss,
            'total_time': total_time
        }
