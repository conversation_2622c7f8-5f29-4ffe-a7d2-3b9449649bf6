#!/usr/bin/env python3
"""
Final Benchmark
最终基准测试 - 使用训练好的模型进行完整的性能评估
"""

import os
import sys
import time
import torch
import numpy as np
import pandas as pd
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent
sys.path.append(str(project_root))

from config import get_config
from core.neural_model import NeuralColumnModel
from core.column_generator import ColumnGenerator
from core.schedule_evaluator import ScheduleEvaluator
from core.feature_extractor import FeatureExtractor
from training.data_generator import TrainingDataGenerator
# 暂时使用简化的求解器实现
# from integration.solver_interface import NeuralColumnSolver, HeuristicSolver


class SimpleSolutionResult:
    """简化的求解结果"""
    def __init__(self, makespan, schedule, time, status):
        self.optimal_makespan = makespan
        self.optimal_schedule = schedule
        self.solution_time = time
        self.status = status


class SimpleNeuralSolver:
    """简化的神经求解器"""
    def __init__(self, model, column_generator, evaluator, config):
        self.model = model
        self.column_generator = column_generator
        self.evaluator = evaluator
        self.config = config
        self.feature_extractor = FeatureExtractor(config)

    def solve(self, instance_data):
        start_time = time.time()

        try:
            # 提取特征
            features = self.feature_extractor.extract_features(instance_data)

            # 生成列
            columns = self.column_generator.generate_columns(
                features,
                instance_data['job_length'],
                num_columns=self.config.model.num_columns
            )

            # 评估列
            best_makespan = float('inf')
            best_schedule = []

            for column in columns:
                evaluated_column = self.evaluator.evaluate_column(column, instance_data)
                if evaluated_column.feasible and evaluated_column.makespan < best_makespan:
                    best_makespan = evaluated_column.makespan
                    best_schedule = evaluated_column.schedule

            solution_time = time.time() - start_time
            status = 'optimal' if best_makespan < float('inf') else 'infeasible'

            return SimpleSolutionResult(best_makespan, best_schedule, solution_time, status)

        except Exception as e:
            solution_time = time.time() - start_time
            return SimpleSolutionResult(float('inf'), [], solution_time, 'error')


class SimpleHeuristicSolver:
    """简化的启发式求解器"""
    def __init__(self, method, config):
        self.method = method
        self.config = config

    def solve(self, instance_data):
        start_time = time.time()

        try:
            job_length = instance_data['job_length']
            processing_matrix = instance_data['processing_matrix']

            if isinstance(processing_matrix, torch.Tensor):
                processing_matrix = processing_matrix.cpu().numpy()

            schedule = self._generate_heuristic_schedule(processing_matrix, job_length)
            makespan = self._calculate_makespan(schedule, processing_matrix, job_length)

            solution_time = time.time() - start_time
            status = 'optimal' if makespan < float('inf') else 'infeasible'

            return SimpleSolutionResult(makespan, schedule, solution_time, status)

        except Exception as e:
            solution_time = time.time() - start_time
            return SimpleSolutionResult(float('inf'), [], solution_time, 'error')

    def _generate_heuristic_schedule(self, processing_matrix, job_length):
        """生成启发式调度"""
        n_jobs = len(job_length)
        n_operations = processing_matrix.shape[0]

        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_length):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops

        schedule = []
        job_progress = [0] * n_jobs
        scheduled_ops = set()

        # 计算操作优先级
        operation_priorities = self._calculate_priorities(processing_matrix)

        # 贪心调度
        while len(scheduled_ops) < n_operations:
            # 获取可调度操作
            available_ops = []
            for job_id, operations in enumerate(job_operations):
                if job_progress[job_id] < len(operations):
                    next_op = operations[job_progress[job_id]]
                    if next_op not in scheduled_ops:
                        available_ops.append(next_op)

            if not available_ops:
                break

            # 选择操作
            if self.method == 'Random':
                selected_op = np.random.choice(available_ops)
            else:
                selected_op = min(available_ops, key=lambda op: operation_priorities.get(op, float('inf')))

            # 选择机器
            best_machine = self._select_best_machine(selected_op, processing_matrix)

            if best_machine is not None:
                schedule.append((selected_op, best_machine))
                scheduled_ops.add(selected_op)

                # 更新作业进度
                for job_id, ops in enumerate(job_operations):
                    if selected_op in ops:
                        job_progress[job_id] += 1
                        break

        return schedule if len(scheduled_ops) == n_operations else []

    def _calculate_priorities(self, processing_matrix):
        """计算操作优先级"""
        priorities = {}

        for op_id in range(processing_matrix.shape[0]):
            valid_times = [processing_matrix[op_id, m] for m in range(processing_matrix.shape[1])
                          if processing_matrix[op_id, m] > 0]

            if not valid_times:
                priorities[op_id] = float('inf')
                continue

            if self.method == 'SPT':
                priorities[op_id] = min(valid_times)
            elif self.method == 'LPT':
                priorities[op_id] = -max(valid_times)
            elif self.method == 'FIFO':
                priorities[op_id] = op_id
            else:  # Random
                priorities[op_id] = np.random.random()

        return priorities

    def _select_best_machine(self, operation_id, processing_matrix):
        """选择最佳机器"""
        valid_machines = []
        for m in range(processing_matrix.shape[1]):
            if processing_matrix[operation_id, m] > 0:
                valid_machines.append((processing_matrix[operation_id, m], m))

        if not valid_machines:
            return None

        valid_machines.sort()
        return valid_machines[0][1]

    def _calculate_makespan(self, schedule, processing_matrix, job_length):
        """计算makespan"""
        if not schedule:
            return float('inf')

        try:
            # 构建作业-操作映射
            job_operations = []
            op_id = 0
            for job_id, n_ops in enumerate(job_length):
                job_operations.append(list(range(op_id, op_id + n_ops)))
                op_id += n_ops

            # 初始化时间跟踪
            n_machines = processing_matrix.shape[1]
            n_jobs = len(job_length)

            machine_available_time = [0.0] * n_machines
            job_completion_time = [0.0] * n_jobs

            # 按调度顺序处理操作
            for op_id, machine_id in schedule:
                # 找到操作所属的作业
                job_id = None
                for j, ops in enumerate(job_operations):
                    if op_id in ops:
                        job_id = j
                        break

                if job_id is None:
                    continue

                # 计算开始时间
                proc_time = processing_matrix[op_id, machine_id]
                start_time = max(machine_available_time[machine_id], job_completion_time[job_id])
                completion_time = start_time + proc_time

                # 更新时间
                machine_available_time[machine_id] = completion_time
                job_completion_time[job_id] = completion_time

            return max(job_completion_time)

        except Exception:
            return float('inf')


def load_trained_model(model_path, config, device):
    """加载训练好的模型"""
    print(f"📂 Loading trained model from {model_path}")
    
    model = NeuralColumnModel(config).to(device)
    
    if os.path.exists(model_path):
        try:
            checkpoint = torch.load(model_path, map_location=device)
            
            if 'model_state_dict' in checkpoint:
                model.load_state_dict(checkpoint['model_state_dict'])
                print(f"✅ Loaded model from checkpoint (epoch {checkpoint.get('epoch', 'unknown')})")
            else:
                model.load_state_dict(checkpoint)
                print("✅ Loaded model state dict")
        except Exception as e:
            print(f"⚠️ Error loading model: {e}")
            print("   Using untrained model")
    else:
        print(f"⚠️ Model file not found: {model_path}")
        print("   Using untrained model")
    
    model.eval()
    return model


def create_diverse_test_instances(num_instances=20):
    """创建多样化的测试实例"""
    print(f"🔧 Creating {num_instances} diverse test instances...")
    
    instances = []
    
    # 小规模问题 (3-5 jobs, 2-3 machines)
    for i in range(num_instances // 4):
        n_jobs = np.random.randint(3, 6)
        n_machines = np.random.randint(2, 4)
        job_length = [np.random.randint(2, 4) for _ in range(n_jobs)]
        n_operations = sum(job_length)
        
        processing_matrix = np.random.randint(1, 11, (n_operations, n_machines)).astype(np.float32)
        
        instances.append({
            'job_length': job_length,
            'processing_matrix': torch.tensor(processing_matrix, dtype=torch.float32),
            'processing_times': processing_matrix,
            'n_jobs': n_jobs,
            'n_machines': n_machines,
            'n_operations': n_operations,
            'filename': f'small_{i}.fjs',
            'category': 'small'
        })
    
    # 中等规模问题 (5-8 jobs, 3-5 machines)
    for i in range(num_instances // 4):
        n_jobs = np.random.randint(5, 9)
        n_machines = np.random.randint(3, 6)
        job_length = [np.random.randint(2, 5) for _ in range(n_jobs)]
        n_operations = sum(job_length)
        
        processing_matrix = np.random.randint(1, 16, (n_operations, n_machines)).astype(np.float32)
        
        instances.append({
            'job_length': job_length,
            'processing_matrix': torch.tensor(processing_matrix, dtype=torch.float32),
            'processing_times': processing_matrix,
            'n_jobs': n_jobs,
            'n_machines': n_machines,
            'n_operations': n_operations,
            'filename': f'medium_{i}.fjs',
            'category': 'medium'
        })
    
    # 大规模问题 (8-12 jobs, 4-6 machines)
    for i in range(num_instances // 4):
        n_jobs = np.random.randint(8, 13)
        n_machines = np.random.randint(4, 7)
        job_length = [np.random.randint(3, 6) for _ in range(n_jobs)]
        n_operations = sum(job_length)
        
        processing_matrix = np.random.randint(1, 21, (n_operations, n_machines)).astype(np.float32)
        
        instances.append({
            'job_length': job_length,
            'processing_matrix': torch.tensor(processing_matrix, dtype=torch.float32),
            'processing_times': processing_matrix,
            'n_jobs': n_jobs,
            'n_machines': n_machines,
            'n_operations': n_operations,
            'filename': f'large_{i}.fjs',
            'category': 'large'
        })
    
    # 超大规模问题 (12-15 jobs, 5-8 machines)
    for i in range(num_instances - 3 * (num_instances // 4)):
        n_jobs = np.random.randint(12, 16)
        n_machines = np.random.randint(5, 9)
        job_length = [np.random.randint(3, 7) for _ in range(n_jobs)]
        n_operations = sum(job_length)
        
        processing_matrix = np.random.randint(1, 26, (n_operations, n_machines)).astype(np.float32)
        
        instances.append({
            'job_length': job_length,
            'processing_matrix': torch.tensor(processing_matrix, dtype=torch.float32),
            'processing_times': processing_matrix,
            'n_jobs': n_jobs,
            'n_machines': n_machines,
            'n_operations': n_operations,
            'filename': f'xlarge_{i}.fjs',
            'category': 'xlarge'
        })
    
    print(f"✅ Created {len(instances)} diverse test instances")
    return instances


def run_comprehensive_benchmark(trained_model, config, device, test_instances):
    """运行综合基准测试"""
    print("🚀 Running comprehensive benchmark...")
    
    # 创建求解器
    column_generator = ColumnGenerator(trained_model, config)
    evaluator = ScheduleEvaluator()
    
    solvers = {
        'Neural_v2_Trained': SimpleNeuralSolver(trained_model, column_generator, evaluator, config),
        'SPT': SimpleHeuristicSolver('SPT', config),
        'LPT': SimpleHeuristicSolver('LPT', config),
        'FIFO': SimpleHeuristicSolver('FIFO', config),
        'Random': SimpleHeuristicSolver('Random', config)
    }
    
    # 运行测试
    all_results = {}
    
    for solver_name, solver in solvers.items():
        print(f"\n🔄 Testing {solver_name}...")
        solver_results = []
        
        for i, instance in enumerate(test_instances):
            print(f"  Instance {i+1}/{len(test_instances)}: {instance['category']} - {instance['n_jobs']}J x {instance['n_machines']}M")
            
            try:
                start_time = time.time()
                result = solver.solve(instance)
                
                solver_results.append({
                    'instance_id': i,
                    'category': instance['category'],
                    'n_jobs': instance['n_jobs'],
                    'n_machines': instance['n_machines'],
                    'n_operations': instance['n_operations'],
                    'makespan': result.optimal_makespan,
                    'solution_time': result.solution_time,
                    'status': result.status,
                    'feasible': result.status in ['optimal', 'feasible'],
                    'success': result.status == 'optimal'
                })
                
                print(f"    Result: makespan={result.optimal_makespan:.2f}, "
                      f"time={result.solution_time:.3f}s, status={result.status}")
                
            except Exception as e:
                print(f"    ❌ Error: {e}")
                solver_results.append({
                    'instance_id': i,
                    'category': instance['category'],
                    'n_jobs': instance['n_jobs'],
                    'n_machines': instance['n_machines'],
                    'n_operations': instance['n_operations'],
                    'makespan': float('inf'),
                    'solution_time': 0.0,
                    'status': 'error',
                    'feasible': False,
                    'success': False
                })
        
        all_results[solver_name] = solver_results
    
    return all_results


def analyze_comprehensive_results(all_results):
    """分析综合结果"""
    print("\n📊 Analyzing comprehensive results...")
    
    # 创建DataFrame
    comparison_data = []
    for solver_name, solver_results in all_results.items():
        for result in solver_results:
            result['solver'] = solver_name
            comparison_data.append(result)
    
    df = pd.DataFrame(comparison_data)
    
    # 按类别和求解器分析
    print("\n📋 Results by Category and Solver:")
    print("=" * 80)
    
    categories = ['small', 'medium', 'large', 'xlarge']
    solvers = list(all_results.keys())
    
    summary_stats = []
    
    for category in categories:
        print(f"\n{category.upper()} Problems:")
        print("-" * 40)
        
        for solver in solvers:
            subset = df[(df['category'] == category) & (df['solver'] == solver)]
            successful = subset[subset['success'] == True]
            
            if len(subset) > 0:
                stats = {
                    'Category': category,
                    'Solver': solver,
                    'Total': len(subset),
                    'Successful': len(successful),
                    'Success_Rate': len(successful) / len(subset),
                    'Avg_Makespan': successful['makespan'].mean() if len(successful) > 0 else float('inf'),
                    'Avg_Time': successful['solution_time'].mean() if len(successful) > 0 else 0,
                    'Total_Time': subset['solution_time'].sum()
                }
                
                summary_stats.append(stats)
                
                print(f"  {solver:20s}: {stats['Success_Rate']:.1%} success, "
                      f"avg makespan: {stats['Avg_Makespan']:.2f}, "
                      f"avg time: {stats['Avg_Time']:.3f}s")
    
    # 总体比较
    print(f"\n📊 OVERALL COMPARISON:")
    print("=" * 80)
    
    overall_stats = []
    for solver in solvers:
        subset = df[df['solver'] == solver]
        successful = subset[subset['success'] == True]
        
        if len(subset) > 0:
            stats = {
                'Solver': solver,
                'Total': len(subset),
                'Successful': len(successful),
                'Success_Rate': len(successful) / len(subset),
                'Avg_Makespan': successful['makespan'].mean() if len(successful) > 0 else float('inf'),
                'Avg_Time': successful['solution_time'].mean() if len(successful) > 0 else 0,
                'Total_Time': subset['solution_time'].sum()
            }
            
            overall_stats.append(stats)
            
            print(f"{solver:20s}: {stats['Success_Rate']:.1%} success, "
                  f"avg makespan: {stats['Avg_Makespan']:.2f}, "
                  f"avg time: {stats['Avg_Time']:.3f}s")
    
    # Neural v2 vs 最佳基线比较
    print(f"\n🎯 NEURAL V2 vs BEST BASELINE:")
    print("-" * 50)
    
    neural_stats = next((s for s in overall_stats if s['Solver'] == 'Neural_v2_Trained'), None)
    baseline_stats = [s for s in overall_stats if s['Solver'] != 'Neural_v2_Trained']
    
    if neural_stats and baseline_stats:
        best_baseline = min(baseline_stats, key=lambda x: x['Avg_Makespan'] if x['Avg_Makespan'] < float('inf') else float('inf'))
        
        if neural_stats['Avg_Makespan'] < float('inf') and best_baseline['Avg_Makespan'] < float('inf'):
            makespan_improvement = (best_baseline['Avg_Makespan'] - neural_stats['Avg_Makespan']) / best_baseline['Avg_Makespan'] * 100
            time_ratio = neural_stats['Avg_Time'] / best_baseline['Avg_Time'] if best_baseline['Avg_Time'] > 0 else float('inf')
            
            print(f"Best baseline: {best_baseline['Solver']}")
            print(f"Neural v2 makespan improvement: {makespan_improvement:+.1f}%")
            print(f"Neural v2 time ratio: {time_ratio:.1f}x")
            print(f"Neural v2 success rate: {neural_stats['Success_Rate']:.1%}")
            print(f"Best baseline success rate: {best_baseline['Success_Rate']:.1%}")
    
    return pd.DataFrame(summary_stats), pd.DataFrame(overall_stats)


def main():
    """主函数"""
    print("🚀 Final Benchmark - Neural Column Generation v2")
    print("=" * 60)
    
    # 设置
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    config = get_config('configs/improved_training.json')
    
    # 加载训练好的模型
    model_path = 'outputs/improved_training/best_checkpoint.pth'
    trained_model = load_trained_model(model_path, config, device)
    
    # 创建测试实例
    test_instances = create_diverse_test_instances(num_instances=20)
    
    # 运行基准测试
    all_results = run_comprehensive_benchmark(trained_model, config, device, test_instances)
    
    # 分析结果
    category_stats, overall_stats = analyze_comprehensive_results(all_results)
    
    # 保存结果
    output_dir = 'outputs/final_benchmark'
    os.makedirs(output_dir, exist_ok=True)
    
    category_stats.to_csv(os.path.join(output_dir, 'category_results.csv'), index=False)
    overall_stats.to_csv(os.path.join(output_dir, 'overall_results.csv'), index=False)
    
    print(f"\n💾 Results saved to {output_dir}")
    print("🎉 Final benchmark completed!")


if __name__ == '__main__':
    main()
