#!/usr/bin/env python3
"""
Branch and Price Solver
分支定价求解器 - 集成神经列生成的分支定价算法
"""

import time
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass

from .solver_interface import SolverInterface, SolutionResult
from ..core.neural_model import ScheduleColumn


@dataclass
class BranchAndPriceConfig:
    """分支定价配置"""
    max_iterations: int = 50
    max_columns_per_iteration: int = 10
    convergence_tolerance: float = 1e-6
    time_limit: float = 300.0
    min_improvement: float = 0.01
    max_no_improvement_iterations: int = 5


class MasterProblem:
    """主问题求解器"""
    
    def __init__(self, n_jobs: int, n_machines: int):
        self.n_jobs = n_jobs
        self.n_machines = n_machines
        self.columns = []
        self.column_costs = []
        
    def add_column(self, column: ScheduleColumn) -> bool:
        """添加列到主问题"""
        if column.feasible and column.makespan < float('inf'):
            self.columns.append(column)
            self.column_costs.append(column.makespan)
            return True
        return False
    
    def solve_relaxed_master(self) -> Tuple[Optional[ScheduleColumn], Optional[np.ndarray], float]:
        """求解松弛主问题"""
        if not self.columns:
            return None, None, float('inf')
        
        # 简化的主问题求解：选择最佳列
        best_idx = np.argmin(self.column_costs)
        best_column = self.columns[best_idx]
        best_cost = self.column_costs[best_idx]
        
        # 简化的对偶价格计算
        dual_prices = np.ones(self.n_jobs) * (best_cost / self.n_jobs)
        
        return best_column, dual_prices, best_cost
    
    def solve_integer_master(self) -> Tuple[Optional[ScheduleColumn], float]:
        """求解整数主问题"""
        # 简化实现：直接返回最佳列
        solution, _, cost = self.solve_relaxed_master()
        return solution, cost


class NeuralPricingProblem:
    """神经定价问题求解器"""
    
    def __init__(self, column_generator, evaluator):
        self.column_generator = column_generator
        self.evaluator = evaluator
        
    def solve_pricing(self, dual_prices: np.ndarray, features: Dict, 
                     instance_data: Dict, num_columns: int = 5) -> List[Tuple[ScheduleColumn, float]]:
        """求解定价问题"""
        try:
            # 生成新列
            new_columns = self.column_generator.generate_columns(
                features, 
                instance_data['job_length'],
                num_columns=num_columns
            )
            
            # 评估列并计算reduced cost
            priced_columns = []
            for column in new_columns:
                # 评估列
                evaluated_column = self.evaluator.evaluate_column(column, instance_data)
                
                if evaluated_column.feasible:
                    # 计算reduced cost (简化)
                    reduced_cost = evaluated_column.makespan - np.sum(dual_prices)
                    priced_columns.append((evaluated_column, reduced_cost))
            
            # 按reduced cost排序
            priced_columns.sort(key=lambda x: x[1])
            
            return priced_columns
            
        except Exception as e:
            print(f"⚠️ Pricing problem error: {e}")
            return []


class BranchAndPriceSolver(SolverInterface):
    """分支定价求解器"""
    
    def __init__(self, model, column_generator, evaluator, config=None):
        super().__init__(config)
        self.model = model
        self.column_generator = column_generator
        self.evaluator = evaluator
        
        # 分支定价配置
        if hasattr(config, 'branch_and_price'):
            self.bp_config = config.branch_and_price
        else:
            self.bp_config = BranchAndPriceConfig()
    
    def solve(self, instance_data: Dict) -> SolutionResult:
        """使用分支定价算法求解"""
        self._start_timer()
        
        try:
            print("🚀 Starting Branch-and-Price solving...")
            
            # 提取特征
            from ..core.feature_extractor import FeatureExtractor
            feature_extractor = FeatureExtractor()
            features = feature_extractor.extract_features(instance_data)
            
            # 初始化问题
            n_jobs = len(instance_data['job_length'])
            n_machines = instance_data['processing_matrix'].shape[1]
            
            master_problem = MasterProblem(n_jobs, n_machines)
            pricing_problem = NeuralPricingProblem(self.column_generator, self.evaluator)
            
            # 生成初始列
            print("🔄 Generating initial columns...")
            initial_columns = self._generate_initial_columns(features, instance_data)
            
            for column in initial_columns:
                master_problem.add_column(column)
            
            print(f"✅ Generated {len(initial_columns)} initial columns")
            
            # 分支定价主循环
            iteration = 0
            best_solution = None
            best_makespan = float('inf')
            no_improvement_count = 0
            total_columns_generated = len(initial_columns)
            
            while iteration < self.bp_config.max_iterations:
                if self._is_time_limit_exceeded():
                    print("⏰ Time limit exceeded")
                    break
                
                iteration += 1
                print(f"\n🔄 Iteration {iteration}")
                
                # 求解松弛主问题
                current_solution, dual_prices, current_makespan = master_problem.solve_relaxed_master()
                
                if current_solution is None:
                    print("❌ No feasible solution in master problem")
                    break
                
                print(f"  Current makespan: {current_makespan:.2f}")
                
                # 检查改进
                if current_makespan < best_makespan - self.bp_config.min_improvement:
                    best_makespan = current_makespan
                    best_solution = current_solution
                    no_improvement_count = 0
                    print(f"🎯 New best makespan: {best_makespan:.2f}")
                else:
                    no_improvement_count += 1
                
                # 求解定价问题
                print("  Solving pricing problem...")
                new_columns = pricing_problem.solve_pricing(
                    dual_prices, features, instance_data, 
                    num_columns=self.bp_config.max_columns_per_iteration
                )
                
                # 检查终止条件
                if not new_columns:
                    print("✅ No improving columns found - optimal solution reached")
                    break
                
                # 添加有改进的列
                added_columns = 0
                for column, reduced_cost in new_columns:
                    if reduced_cost < -self.bp_config.convergence_tolerance:
                        if master_problem.add_column(column):
                            added_columns += 1
                            total_columns_generated += 1
                            
                            # 检查是否有更好的解
                            if column.makespan < best_makespan:
                                best_makespan = column.makespan
                                best_solution = column
                                print(f"🌟 New best from pricing: {best_makespan:.2f}")
                
                print(f"  Added {added_columns} new columns")
                
                # 检查收敛
                if added_columns == 0:
                    print("✅ No improving columns added - converged")
                    break
                
                # 检查无改进迭代次数
                if no_improvement_count >= self.bp_config.max_no_improvement_iterations:
                    print(f"⏹️ No improvement for {no_improvement_count} iterations - stopping")
                    break
            
            # 求解整数主问题
            print("\n🎯 Solving integer master problem...")
            final_solution, final_makespan = master_problem.solve_integer_master()
            
            if final_solution and final_makespan < best_makespan:
                best_solution = final_solution
                best_makespan = final_makespan
            
            # 返回结果
            if best_solution:
                status = 'optimal' if iteration < self.bp_config.max_iterations else 'time_limit'
                print(f"✅ Branch-and-Price completed: {best_makespan:.2f} in {iteration} iterations")
                
                return SolutionResult(
                    optimal_makespan=best_makespan,
                    optimal_schedule=best_solution.schedule,
                    solution_time=self._get_elapsed_time(),
                    status=status,
                    num_iterations=iteration,
                    num_columns_generated=total_columns_generated,
                    additional_info={
                        'final_iteration': iteration,
                        'total_columns': len(master_problem.columns),
                        'best_quality_score': best_solution.quality_score,
                        'decision_quality': best_solution.decision_quality
                    }
                )
            else:
                return SolutionResult(
                    optimal_makespan=float('inf'),
                    optimal_schedule=[],
                    solution_time=self._get_elapsed_time(),
                    status='infeasible',
                    num_iterations=iteration,
                    num_columns_generated=total_columns_generated
                )
                
        except Exception as e:
            print(f"❌ Branch-and-Price error: {e}")
            return self._create_error_result(str(e))
    
    def _generate_initial_columns(self, features: Dict, instance_data: Dict) -> List[ScheduleColumn]:
        """生成初始列"""
        try:
            # 使用神经网络生成初始列
            initial_columns = self.column_generator.generate_columns(
                features,
                instance_data['job_length'],
                num_columns=5
            )
            
            # 评估初始列
            evaluated_columns = []
            for column in initial_columns:
                evaluated_column = self.evaluator.evaluate_column(column, instance_data)
                if evaluated_column.feasible:
                    evaluated_columns.append(evaluated_column)
            
            # 如果神经网络生成的列不够，使用启发式方法补充
            if len(evaluated_columns) < 3:
                heuristic_columns = self._generate_heuristic_columns(instance_data)
                evaluated_columns.extend(heuristic_columns)
            
            return evaluated_columns
            
        except Exception as e:
            print(f"⚠️ Error generating initial columns: {e}")
            return self._generate_heuristic_columns(instance_data)
    
    def _generate_heuristic_columns(self, instance_data: Dict) -> List[ScheduleColumn]:
        """生成启发式列"""
        from .solver_interface import HeuristicSolver
        
        heuristic_columns = []
        methods = ['SPT', 'LPT', 'FIFO']
        
        for method in methods:
            try:
                solver = HeuristicSolver(method)
                result = solver.solve(instance_data)
                
                if result.status == 'optimal' and result.optimal_schedule:
                    column = ScheduleColumn(
                        schedule=result.optimal_schedule,
                        makespan=result.optimal_makespan,
                        feasible=True,
                        quality_score=1.0 / result.optimal_makespan if result.optimal_makespan > 0 else 0.0,
                        decision_quality=0.8  # 启发式方法的决策质量
                    )
                    heuristic_columns.append(column)
                    
            except Exception as e:
                print(f"⚠️ Error with {method} heuristic: {e}")
        
        return heuristic_columns
