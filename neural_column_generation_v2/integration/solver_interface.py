#!/usr/bin/env python3
"""
Solver Interface
求解器接口 - 提供统一的求解器接口
"""

import time
from abc import ABC, abstractmethod
from typing import Dict, List, Tuple, Optional, Any
from dataclasses import dataclass
from ..core.neural_model import ScheduleColumn


@dataclass
class SolutionResult:
    """求解结果"""
    optimal_makespan: float
    optimal_schedule: List[Tuple[int, int]]
    solution_time: float
    status: str  # 'optimal', 'feasible', 'infeasible', 'time_limit', 'error'
    num_iterations: int = 0
    num_columns_generated: int = 0
    gap: float = 0.0
    additional_info: Dict = None


class SolverInterface(ABC):
    """求解器接口基类"""
    
    def __init__(self, config=None):
        self.config = config
        self.start_time = None
        self.time_limit = getattr(config, 'time_limit', 300.0) if config else 300.0
    
    @abstractmethod
    def solve(self, instance_data: Dict) -> SolutionResult:
        """
        求解FJSP实例
        
        Args:
            instance_data: 实例数据，包含job_length, processing_matrix等
            
        Returns:
            SolutionResult: 求解结果
        """
        pass
    
    def _start_timer(self):
        """开始计时"""
        self.start_time = time.time()
    
    def _get_elapsed_time(self) -> float:
        """获取已用时间"""
        if self.start_time is None:
            return 0.0
        return time.time() - self.start_time
    
    def _is_time_limit_exceeded(self) -> bool:
        """检查是否超时"""
        return self._get_elapsed_time() > self.time_limit
    
    def _create_error_result(self, error_msg: str) -> SolutionResult:
        """创建错误结果"""
        return SolutionResult(
            optimal_makespan=float('inf'),
            optimal_schedule=[],
            solution_time=self._get_elapsed_time(),
            status='error',
            additional_info={'error': error_msg}
        )
    
    def _create_timeout_result(self, best_makespan: float = float('inf'), 
                              best_schedule: List[Tuple[int, int]] = None) -> SolutionResult:
        """创建超时结果"""
        return SolutionResult(
            optimal_makespan=best_makespan,
            optimal_schedule=best_schedule or [],
            solution_time=self._get_elapsed_time(),
            status='time_limit'
        )


class NeuralColumnSolver(SolverInterface):
    """神经列生成求解器"""
    
    def __init__(self, model, column_generator, evaluator, config=None):
        super().__init__(config)
        self.model = model
        self.column_generator = column_generator
        self.evaluator = evaluator
        
    def solve(self, instance_data: Dict) -> SolutionResult:
        """使用神经列生成求解"""
        self._start_timer()
        
        try:
            # 提取特征
            from ..core.feature_extractor import FeatureExtractor
            feature_extractor = FeatureExtractor()
            features = feature_extractor.extract_features(instance_data)
            
            # 生成列
            columns = self.column_generator.generate_columns(
                features,
                instance_data['job_length'],
                num_columns=getattr(self.config.model, 'num_columns', 5)
            )
            
            if not columns:
                return self._create_error_result("No columns generated")
            
            # 评估列
            evaluated_columns = []
            for column in columns:
                evaluated_column = self.evaluator.evaluate_column(column, instance_data)
                evaluated_columns.append(evaluated_column)
            
            # 找到最佳解
            feasible_columns = [col for col in evaluated_columns if col.feasible and col.makespan < float('inf')]
            
            if not feasible_columns:
                return SolutionResult(
                    optimal_makespan=float('inf'),
                    optimal_schedule=[],
                    solution_time=self._get_elapsed_time(),
                    status='infeasible',
                    num_columns_generated=len(columns)
                )
            
            best_column = min(feasible_columns, key=lambda x: x.makespan)
            
            return SolutionResult(
                optimal_makespan=best_column.makespan,
                optimal_schedule=best_column.schedule,
                solution_time=self._get_elapsed_time(),
                status='optimal',
                num_columns_generated=len(columns),
                additional_info={
                    'feasible_columns': len(feasible_columns),
                    'total_columns': len(columns),
                    'best_quality_score': best_column.quality_score,
                    'decision_quality': best_column.decision_quality
                }
            )
            
        except Exception as e:
            return self._create_error_result(str(e))


class HeuristicSolver(SolverInterface):
    """启发式求解器"""
    
    def __init__(self, method='SPT', config=None):
        super().__init__(config)
        self.method = method
        
    def solve(self, instance_data: Dict) -> SolutionResult:
        """使用启发式方法求解"""
        self._start_timer()
        
        try:
            job_length = instance_data['job_length']
            processing_matrix = instance_data['processing_matrix']
            
            # 转换为numpy数组
            import torch
            import numpy as np
            if isinstance(processing_matrix, torch.Tensor):
                processing_matrix = processing_matrix.cpu().numpy()
            
            # 生成启发式调度
            schedule = self._generate_heuristic_schedule(processing_matrix, job_length)
            
            if not schedule:
                return SolutionResult(
                    optimal_makespan=float('inf'),
                    optimal_schedule=[],
                    solution_time=self._get_elapsed_time(),
                    status='infeasible'
                )
            
            # 计算makespan
            makespan = self._calculate_makespan(schedule, processing_matrix, job_length)
            
            return SolutionResult(
                optimal_makespan=makespan,
                optimal_schedule=schedule,
                solution_time=self._get_elapsed_time(),
                status='optimal' if makespan < float('inf') else 'infeasible'
            )
            
        except Exception as e:
            return self._create_error_result(str(e))
    
    def _generate_heuristic_schedule(self, processing_matrix: np.ndarray, 
                                   job_length: List[int]) -> List[Tuple[int, int]]:
        """生成启发式调度"""
        import numpy as np
        
        n_jobs = len(job_length)
        n_operations = processing_matrix.shape[0]
        
        # 构建作业-操作映射
        job_operations = []
        op_id = 0
        for job_id, n_ops in enumerate(job_length):
            job_operations.append(list(range(op_id, op_id + n_ops)))
            op_id += n_ops
        
        schedule = []
        job_progress = [0] * n_jobs
        scheduled_ops = set()
        
        # 计算操作优先级
        operation_priorities = self._calculate_priorities(processing_matrix)
        
        # 贪心调度
        while len(scheduled_ops) < n_operations:
            # 获取可调度操作
            available_ops = []
            for job_id, operations in enumerate(job_operations):
                if job_progress[job_id] < len(operations):
                    next_op = operations[job_progress[job_id]]
                    if next_op not in scheduled_ops:
                        available_ops.append(next_op)
            
            if not available_ops:
                break
            
            # 选择操作
            if self.method == 'Random':
                selected_op = np.random.choice(available_ops)
            else:
                selected_op = min(available_ops, key=lambda op: operation_priorities.get(op, float('inf')))
            
            # 选择机器
            best_machine = self._select_best_machine(selected_op, processing_matrix)
            
            if best_machine is not None:
                schedule.append((selected_op, best_machine))
                scheduled_ops.add(selected_op)
                
                # 更新作业进度
                for job_id, ops in enumerate(job_operations):
                    if selected_op in ops:
                        job_progress[job_id] += 1
                        break
        
        return schedule if len(scheduled_ops) == n_operations else []
    
    def _calculate_priorities(self, processing_matrix: np.ndarray) -> Dict[int, float]:
        """计算操作优先级"""
        import numpy as np
        
        priorities = {}
        
        for op_id in range(processing_matrix.shape[0]):
            valid_times = [processing_matrix[op_id, m] for m in range(processing_matrix.shape[1]) 
                          if processing_matrix[op_id, m] > 0]
            
            if not valid_times:
                priorities[op_id] = float('inf')
                continue
            
            if self.method == 'SPT':
                priorities[op_id] = min(valid_times)
            elif self.method == 'LPT':
                priorities[op_id] = -max(valid_times)
            elif self.method == 'FIFO':
                priorities[op_id] = op_id
            else:  # Random
                priorities[op_id] = np.random.random()
        
        return priorities
    
    def _select_best_machine(self, operation_id: int, processing_matrix: np.ndarray) -> Optional[int]:
        """选择最佳机器"""
        valid_machines = []
        for m in range(processing_matrix.shape[1]):
            if processing_matrix[operation_id, m] > 0:
                valid_machines.append((processing_matrix[operation_id, m], m))
        
        if not valid_machines:
            return None
        
        # 选择处理时间最短的机器
        valid_machines.sort()
        return valid_machines[0][1]
    
    def _calculate_makespan(self, schedule: List[Tuple[int, int]], 
                          processing_matrix: np.ndarray, job_length: List[int]) -> float:
        """计算makespan"""
        if not schedule:
            return float('inf')
        
        try:
            # 构建作业-操作映射
            job_operations = []
            op_id = 0
            for job_id, n_ops in enumerate(job_length):
                job_operations.append(list(range(op_id, op_id + n_ops)))
                op_id += n_ops
            
            # 初始化时间跟踪
            n_machines = processing_matrix.shape[1]
            n_jobs = len(job_length)
            
            machine_available_time = [0.0] * n_machines
            job_completion_time = [0.0] * n_jobs
            
            # 按调度顺序处理操作
            for op_id, machine_id in schedule:
                # 找到操作所属的作业
                job_id = None
                for j, ops in enumerate(job_operations):
                    if op_id in ops:
                        job_id = j
                        break
                
                if job_id is None:
                    continue
                
                # 计算开始时间
                proc_time = processing_matrix[op_id, machine_id]
                start_time = max(machine_available_time[machine_id], job_completion_time[job_id])
                completion_time = start_time + proc_time
                
                # 更新时间
                machine_available_time[machine_id] = completion_time
                job_completion_time[job_id] = completion_time
            
            return max(job_completion_time)
            
        except Exception:
            return float('inf')
