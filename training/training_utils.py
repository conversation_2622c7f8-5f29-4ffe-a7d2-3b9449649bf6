#!/usr/bin/env python3
"""
Training Utilities
训练工具 - 训练过程中的辅助工具和类
"""

import torch
import torch.nn as nn
import numpy as np
import time
import os
import json
from typing import Dict, List, Optional, Any
from collections import defaultdict
import matplotlib.pyplot as plt


class EarlyStopping:
    """早停机制"""
    
    def __init__(self, patience: int = 10, min_delta: float = 1e-4, 
                 restore_best_weights: bool = True):
        self.patience = patience
        self.min_delta = min_delta
        self.restore_best_weights = restore_best_weights
        
        self.best_loss = float('inf')
        self.counter = 0
        self.best_weights = None
        self.early_stop = False
    
    def __call__(self, val_loss: float, model: nn.Module) -> bool:
        """检查是否应该早停"""
        if val_loss < self.best_loss - self.min_delta:
            self.best_loss = val_loss
            self.counter = 0
            
            if self.restore_best_weights:
                self.best_weights = {k: v.cpu().clone() for k, v in model.state_dict().items()}
        else:
            self.counter += 1
        
        if self.counter >= self.patience:
            self.early_stop = True
            
            if self.restore_best_weights and self.best_weights is not None:
                model.load_state_dict(self.best_weights)
                print(f"🔄 Restored best weights from {self.patience} epochs ago")
        
        return self.early_stop


class LearningRateScheduler:
    """学习率调度器"""
    
    def __init__(self, optimizer, scheduler_type: str = 'cosine', 
                 warmup_epochs: int = 5, total_epochs: int = 100):
        self.optimizer = optimizer
        self.scheduler_type = scheduler_type
        self.warmup_epochs = warmup_epochs
        self.total_epochs = total_epochs
        self.current_epoch = 0
        
        # 保存初始学习率
        self.base_lrs = [group['lr'] for group in optimizer.param_groups]
        
        # 创建调度器
        if scheduler_type == 'cosine':
            self.scheduler = torch.optim.lr_scheduler.CosineAnnealingLR(
                optimizer, T_max=total_epochs - warmup_epochs
            )
        elif scheduler_type == 'step':
            self.scheduler = torch.optim.lr_scheduler.StepLR(
                optimizer, step_size=total_epochs // 3, gamma=0.1
            )
        elif scheduler_type == 'exponential':
            self.scheduler = torch.optim.lr_scheduler.ExponentialLR(
                optimizer, gamma=0.95
            )
        else:
            self.scheduler = None
    
    def step(self, epoch: int):
        """更新学习率"""
        self.current_epoch = epoch
        
        if epoch < self.warmup_epochs:
            # 预热阶段
            warmup_factor = (epoch + 1) / self.warmup_epochs
            for i, group in enumerate(self.optimizer.param_groups):
                group['lr'] = self.base_lrs[i] * warmup_factor
        else:
            # 正常调度
            if self.scheduler is not None:
                self.scheduler.step()
    
    def get_lr(self) -> List[float]:
        """获取当前学习率"""
        return [group['lr'] for group in self.optimizer.param_groups]


class MetricsTracker:
    """指标跟踪器"""
    
    def __init__(self):
        self.metrics = defaultdict(list)
        self.epoch_metrics = defaultdict(list)
    
    def update(self, metrics: Dict[str, float], prefix: str = ''):
        """更新指标"""
        for key, value in metrics.items():
            metric_name = f"{prefix}_{key}" if prefix else key
            self.metrics[metric_name].append(value)
    
    def end_epoch(self):
        """结束当前epoch，计算平均指标"""
        for key, values in self.metrics.items():
            if values:
                self.epoch_metrics[key].append(np.mean(values))
                self.metrics[key] = []  # 清空当前epoch的指标
    
    def get_latest_metrics(self) -> Dict[str, float]:
        """获取最新的epoch指标"""
        return {key: values[-1] for key, values in self.epoch_metrics.items() if values}
    
    def get_best_metric(self, metric_name: str, mode: str = 'min') -> float:
        """获取最佳指标值"""
        if metric_name not in self.epoch_metrics or not self.epoch_metrics[metric_name]:
            return float('inf') if mode == 'min' else -float('inf')
        
        values = self.epoch_metrics[metric_name]
        return min(values) if mode == 'min' else max(values)
    
    def plot_metrics(self, save_path: Optional[str] = None):
        """绘制指标曲线"""
        if not self.epoch_metrics:
            return
        
        # 创建子图
        n_metrics = len(self.epoch_metrics)
        n_cols = min(3, n_metrics)
        n_rows = (n_metrics + n_cols - 1) // n_cols
        
        fig, axes = plt.subplots(n_rows, n_cols, figsize=(5 * n_cols, 4 * n_rows))
        if n_metrics == 1:
            axes = [axes]
        elif n_rows == 1:
            axes = axes.reshape(1, -1)
        
        # 绘制每个指标
        for i, (metric_name, values) in enumerate(self.epoch_metrics.items()):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col] if n_rows > 1 else axes[col]
            
            ax.plot(values, marker='o', linewidth=2, markersize=4)
            ax.set_title(metric_name.replace('_', ' ').title())
            ax.set_xlabel('Epoch')
            ax.set_ylabel('Value')
            ax.grid(True, alpha=0.3)
        
        # 隐藏多余的子图
        for i in range(n_metrics, n_rows * n_cols):
            row = i // n_cols
            col = i % n_cols
            ax = axes[row, col] if n_rows > 1 else axes[col]
            ax.set_visible(False)
        
        plt.tight_layout()
        
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 Metrics plot saved to {save_path}")
        
        return fig


class CheckpointManager:
    """检查点管理器"""
    
    def __init__(self, checkpoint_dir: str, keep_last_n: int = 3):
        self.checkpoint_dir = checkpoint_dir
        self.keep_last_n = keep_last_n
        
        os.makedirs(checkpoint_dir, exist_ok=True)
        self.checkpoints = []
    
    def save_checkpoint(self, model: nn.Module, optimizer, scheduler, 
                       epoch: int, metrics: Dict[str, float], 
                       is_best: bool = False):
        """保存检查点"""
        checkpoint = {
            'epoch': epoch,
            'model_state_dict': model.state_dict(),
            'optimizer_state_dict': optimizer.state_dict(),
            'scheduler_state_dict': scheduler.state_dict() if scheduler else None,
            'metrics': metrics,
            'timestamp': time.time()
        }
        
        # 保存常规检查点
        checkpoint_path = os.path.join(self.checkpoint_dir, f'checkpoint_epoch_{epoch}.pth')
        torch.save(checkpoint, checkpoint_path)
        self.checkpoints.append((epoch, checkpoint_path))
        
        # 保存最佳检查点
        if is_best:
            best_path = os.path.join(self.checkpoint_dir, 'best_checkpoint.pth')
            torch.save(checkpoint, best_path)
            print(f"💾 Best checkpoint saved at epoch {epoch}")
        
        # 清理旧检查点
        self._cleanup_old_checkpoints()
        
        print(f"💾 Checkpoint saved: epoch {epoch}")
    
    def _cleanup_old_checkpoints(self):
        """清理旧的检查点"""
        if len(self.checkpoints) > self.keep_last_n:
            # 按epoch排序
            self.checkpoints.sort(key=lambda x: x[0])
            
            # 删除最旧的检查点
            while len(self.checkpoints) > self.keep_last_n:
                epoch, path = self.checkpoints.pop(0)
                if os.path.exists(path):
                    os.remove(path)
                    print(f"🗑️  Removed old checkpoint: epoch {epoch}")
    
    def load_checkpoint(self, model: nn.Module, optimizer=None, scheduler=None, 
                       checkpoint_path: Optional[str] = None) -> Dict:
        """加载检查点"""
        if checkpoint_path is None:
            checkpoint_path = os.path.join(self.checkpoint_dir, 'best_checkpoint.pth')
        
        if not os.path.exists(checkpoint_path):
            raise FileNotFoundError(f"Checkpoint not found: {checkpoint_path}")
        
        checkpoint = torch.load(checkpoint_path, map_location='cpu')
        
        model.load_state_dict(checkpoint['model_state_dict'])
        
        if optimizer is not None:
            optimizer.load_state_dict(checkpoint['optimizer_state_dict'])
        
        if scheduler is not None and checkpoint['scheduler_state_dict'] is not None:
            scheduler.load_state_dict(checkpoint['scheduler_state_dict'])
        
        print(f"📁 Checkpoint loaded from epoch {checkpoint['epoch']}")
        
        return checkpoint


class TrainingUtils:
    """训练工具集合"""
    
    @staticmethod
    def set_random_seed(seed: int):
        """设置随机种子"""
        torch.manual_seed(seed)
        torch.cuda.manual_seed(seed)
        torch.cuda.manual_seed_all(seed)
        np.random.seed(seed)
        
        if torch.cuda.is_available():
            torch.backends.cudnn.deterministic = True
            torch.backends.cudnn.benchmark = False
    
    @staticmethod
    def count_parameters(model: nn.Module) -> int:
        """计算模型参数数量"""
        return sum(p.numel() for p in model.parameters() if p.requires_grad)
    
    @staticmethod
    def get_model_size(model: nn.Module) -> float:
        """获取模型大小（MB）"""
        param_size = 0
        buffer_size = 0
        
        for param in model.parameters():
            param_size += param.nelement() * param.element_size()
        
        for buffer in model.buffers():
            buffer_size += buffer.nelement() * buffer.element_size()
        
        size_mb = (param_size + buffer_size) / 1024 / 1024
        return size_mb
    
    @staticmethod
    def clip_gradients(model: nn.Module, max_norm: float):
        """梯度裁剪"""
        torch.nn.utils.clip_grad_norm_(model.parameters(), max_norm)
    
    @staticmethod
    def get_device_info() -> Dict[str, Any]:
        """获取设备信息"""
        info = {
            'cuda_available': torch.cuda.is_available(),
            'device_count': torch.cuda.device_count() if torch.cuda.is_available() else 0,
        }
        
        if torch.cuda.is_available():
            info['current_device'] = torch.cuda.current_device()
            info['device_name'] = torch.cuda.get_device_name()
            info['memory_allocated'] = torch.cuda.memory_allocated() / 1024**3  # GB
            info['memory_reserved'] = torch.cuda.memory_reserved() / 1024**3   # GB
        
        return info
    
    @staticmethod
    def format_time(seconds: float) -> str:
        """格式化时间"""
        if seconds < 60:
            return f"{seconds:.1f}s"
        elif seconds < 3600:
            return f"{seconds//60:.0f}m {seconds%60:.0f}s"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{hours:.0f}h {minutes:.0f}m"
    
    @staticmethod
    def save_training_config(config, save_path: str):
        """保存训练配置"""
        config_dict = config.to_dict()
        with open(save_path, 'w') as f:
            json.dump(config_dict, f, indent=2)
        print(f"💾 Training config saved to {save_path}")
    
    @staticmethod
    def create_training_summary(metrics_tracker: MetricsTracker, 
                              config, model: nn.Module, 
                              training_time: float) -> Dict[str, Any]:
        """创建训练摘要"""
        summary = {
            'model_info': {
                'parameters': TrainingUtils.count_parameters(model),
                'model_size_mb': TrainingUtils.get_model_size(model),
                'architecture': str(model.__class__.__name__)
            },
            'training_info': {
                'total_epochs': len(metrics_tracker.epoch_metrics.get('train_total_loss', [])),
                'training_time': training_time,
                'training_time_formatted': TrainingUtils.format_time(training_time),
                'device_info': TrainingUtils.get_device_info()
            },
            'final_metrics': metrics_tracker.get_latest_metrics(),
            'best_metrics': {
                'best_val_loss': metrics_tracker.get_best_metric('val_total_loss', 'min'),
                'best_train_loss': metrics_tracker.get_best_metric('train_total_loss', 'min')
            },
            'config': config.to_dict()
        }
        
        return summary
