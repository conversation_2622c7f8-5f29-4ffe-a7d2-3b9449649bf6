#!/usr/bin/env python3
"""
Neural Column Trainer
神经列生成训练器 - 主训练类
"""

import torch
import torch.nn as nn
import torch.optim as optim
import time
import os
import json
from typing import Dict, List, Optional, Tuple
from tqdm import tqdm

from core import NeuralColumnModel, ColumnGenerator
from .data_generator import TrainingDataGenerator
from .loss_functions import CombinedLoss
from .training_utils import (
    EarlyStopping, LearningRateScheduler, MetricsTracker, 
    CheckpointManager, TrainingUtils
)


class NeuralColumnTrainer:
    """神经列生成训练器"""
    
    def __init__(self, config):
        self.config = config
        self.device = torch.device(config.system.device)
        
        # 设置随机种子
        TrainingUtils.set_random_seed(config.system.random_seed)
        
        # 初始化组件
        self._initialize_components()
        
        # 训练状态
        self.current_epoch = 0
        self.best_val_loss = float('inf')
        self.training_start_time = None
        
        print(f"🎯 Neural Column Trainer initialized")
        print(f"   Device: {self.device}")
        print(f"   Model parameters: {TrainingUtils.count_parameters(self.model):,}")
        print(f"   Model size: {TrainingUtils.get_model_size(self.model):.2f} MB")
    
    def _initialize_components(self):
        """初始化训练组件"""
        # 模型
        self.model = NeuralColumnModel(self.config).to(self.device)
        
        # 列生成器
        self.column_generator = ColumnGenerator(self.model, self.config)
        
        # 数据生成器
        self.data_generator = TrainingDataGenerator(self.config)
        
        # 损失函数
        self.criterion = CombinedLoss(self.config)
        
        # 优化器
        self.optimizer = self._create_optimizer()
        
        # 学习率调度器
        self.scheduler = LearningRateScheduler(
            self.optimizer,
            scheduler_type=self.config.training.scheduler,
            warmup_epochs=self.config.training.warmup_epochs,
            total_epochs=self.config.training.num_epochs
        )
        
        # 早停
        self.early_stopping = EarlyStopping(
            patience=self.config.training.patience,
            min_delta=self.config.training.min_delta
        )
        
        # 指标跟踪
        self.metrics_tracker = MetricsTracker()
        
        # 检查点管理
        self.checkpoint_manager = CheckpointManager(
            checkpoint_dir=os.path.join(self.config.data.model_save_dir, 'checkpoints'),
            keep_last_n=self.config.system.keep_last_n_checkpoints
        )
    
    def _create_optimizer(self):
        """创建优化器"""
        if self.config.training.optimizer == 'Adam':
            return optim.Adam(
                self.model.parameters(),
                lr=self.config.training.learning_rate,
                weight_decay=self.config.training.weight_decay
            )
        elif self.config.training.optimizer == 'AdamW':
            return optim.AdamW(
                self.model.parameters(),
                lr=self.config.training.learning_rate,
                weight_decay=self.config.training.weight_decay
            )
        elif self.config.training.optimizer == 'SGD':
            return optim.SGD(
                self.model.parameters(),
                lr=self.config.training.learning_rate,
                momentum=0.9,
                weight_decay=self.config.training.weight_decay
            )
        else:
            raise ValueError(f"Unsupported optimizer: {self.config.training.optimizer}")
    
    def train(self):
        """开始训练"""
        print("🚀 Starting Neural Column Generation Training")
        print("=" * 60)
        
        self.training_start_time = time.time()
        
        # 创建数据加载器
        train_loader, val_loader, test_loader = self.data_generator.create_dataloaders()
        
        # 保存训练配置
        config_path = os.path.join(self.config.data.model_save_dir, 'training_config.json')
        TrainingUtils.save_training_config(self.config, config_path)
        
        # 训练循环
        for epoch in range(self.config.training.num_epochs):
            self.current_epoch = epoch
            
            # 训练一个epoch
            train_metrics = self._train_epoch(train_loader, epoch)
            
            # 验证
            val_metrics = self._validate_epoch(val_loader, epoch)
            
            # 更新学习率
            self.scheduler.step(epoch)
            
            # 记录指标
            self.metrics_tracker.update(train_metrics, 'train')
            self.metrics_tracker.update(val_metrics, 'val')
            self.metrics_tracker.end_epoch()
            
            # 打印进度
            self._print_epoch_summary(epoch, train_metrics, val_metrics)
            
            # 检查是否是最佳模型
            val_loss = val_metrics['total_loss']
            is_best = val_loss < self.best_val_loss
            if is_best:
                self.best_val_loss = val_loss
            
            # 保存检查点
            if self.config.system.save_checkpoints and (epoch + 1) % self.config.system.checkpoint_interval == 0:
                self.checkpoint_manager.save_checkpoint(
                    self.model, self.optimizer, self.scheduler,
                    epoch, val_metrics, is_best
                )
            
            # 自适应损失权重调整
            self.criterion.adaptive_weight_adjustment(epoch)
            
            # 早停检查
            if self.early_stopping(val_loss, self.model):
                print(f"🛑 Early stopping triggered at epoch {epoch}")
                break
        
        # 训练完成
        self._finish_training()
    
    def _train_epoch(self, train_loader, epoch: int) -> Dict[str, float]:
        """训练一个epoch"""
        self.model.train()
        
        epoch_losses = {
            'total_loss': [],
            'makespan_loss': [],
            'feasibility_loss': [],
            'decision_quality_loss': [],
            'diversity_loss': [],
            'ranking_loss': []
        }
        
        # 进度条
        pbar = tqdm(train_loader, desc=f'Epoch {epoch+1}/{self.config.training.num_epochs}')
        
        for batch_idx, batch in enumerate(pbar):
            # 前向传播
            loss_dict = self._forward_pass(batch)
            
            # 反向传播
            self.optimizer.zero_grad()
            loss_dict['total_loss'].backward()
            
            # 梯度裁剪
            if self.config.training.gradient_clip > 0:
                TrainingUtils.clip_gradients(self.model, self.config.training.gradient_clip)
            
            self.optimizer.step()
            
            # 记录损失
            for key, value in loss_dict.items():
                if isinstance(value, torch.Tensor):
                    epoch_losses[key].append(value.item())
            
            # 更新进度条
            pbar.set_postfix({
                'Loss': f"{loss_dict['total_loss'].item():.4f}",
                'LR': f"{self.scheduler.get_lr()[0]:.6f}"
            })
        
        # 计算平均损失
        avg_losses = {key: sum(values) / len(values) for key, values in epoch_losses.items() if values}
        
        return avg_losses
    
    def _validate_epoch(self, val_loader, epoch: int) -> Dict[str, float]:
        """验证一个epoch"""
        self.model.eval()
        
        epoch_losses = {
            'total_loss': [],
            'makespan_loss': [],
            'feasibility_loss': [],
            'decision_quality_loss': [],
            'diversity_loss': [],
            'ranking_loss': []
        }
        
        with torch.no_grad():
            for batch in val_loader:
                loss_dict = self._forward_pass(batch)
                
                # 记录损失
                for key, value in loss_dict.items():
                    if isinstance(value, torch.Tensor):
                        epoch_losses[key].append(value.item())
        
        # 计算平均损失
        avg_losses = {key: sum(values) / len(values) for key, values in epoch_losses.items() if values}
        
        return avg_losses
    
    def _forward_pass(self, batch) -> Dict[str, torch.Tensor]:
        """前向传播"""
        features_list = batch['features']
        instances = batch['instances']
        
        # 生成列
        all_columns = []
        
        for features, instance in zip(features_list, instances):
            # 移动特征到设备
            device_features = {}
            for key, value in features.items():
                if isinstance(value, torch.Tensor):
                    device_features[key] = value.to(self.device)
                else:
                    device_features[key] = value
            
            # 生成列
            columns = self.column_generator.generate_columns(
                device_features, 
                instance['job_length'],
                num_columns=self.config.model.num_columns
            )
            
            all_columns.append(columns)
        
        # 计算损失
        loss_dict = self.criterion(all_columns, instances)

        return loss_dict

    def _print_epoch_summary(self, epoch: int, train_metrics: Dict, val_metrics: Dict):
        """打印epoch摘要"""
        elapsed_time = time.time() - self.training_start_time

        print(f"\nEpoch {epoch+1}/{self.config.training.num_epochs}")
        print(f"Time: {TrainingUtils.format_time(elapsed_time)}")
        print(f"LR: {self.scheduler.get_lr()[0]:.6f}")

        print("Train Losses:")
        for key, value in train_metrics.items():
            print(f"  {key}: {value:.4f}")

        print("Val Losses:")
        for key, value in val_metrics.items():
            print(f"  {key}: {value:.4f}")

        # 损失权重信息
        weights = self.criterion.get_loss_weights()
        print("Loss Weights:")
        for key, value in weights.items():
            print(f"  {key}: {value:.3f}")

        print("-" * 40)

    def _finish_training(self):
        """完成训练"""
        total_time = time.time() - self.training_start_time

        print("\n" + "=" * 60)
        print("🎉 Training Completed!")
        print("=" * 60)

        # 保存最终模型
        final_model_path = os.path.join(self.config.data.model_save_dir, 'final_model.pth')
        torch.save({
            'model_state_dict': self.model.state_dict(),
            'config': self.config.to_dict(),
            'training_metrics': self.metrics_tracker.epoch_metrics
        }, final_model_path)

        print(f"💾 Final model saved to: {final_model_path}")

        # 生成训练摘要
        summary = TrainingUtils.create_training_summary(
            self.metrics_tracker, self.config, self.model, total_time
        )

        summary_path = os.path.join(self.config.data.model_save_dir, 'training_summary.json')
        with open(summary_path, 'w') as f:
            json.dump(summary, f, indent=2)

        print(f"📊 Training summary saved to: {summary_path}")

        # 绘制训练曲线
        plot_path = os.path.join(self.config.data.model_save_dir, 'training_curves.png')
        self.metrics_tracker.plot_metrics(plot_path)

        # 打印最终统计
        print(f"\n📈 Training Statistics:")
        print(f"  Total time: {TrainingUtils.format_time(total_time)}")
        print(f"  Total epochs: {self.current_epoch + 1}")
        print(f"  Best validation loss: {self.best_val_loss:.4f}")
        print(f"  Final learning rate: {self.scheduler.get_lr()[0]:.6f}")

        latest_metrics = self.metrics_tracker.get_latest_metrics()
        if latest_metrics:
            print(f"  Final train loss: {latest_metrics.get('train_total_loss', 'N/A'):.4f}")
            print(f"  Final val loss: {latest_metrics.get('val_total_loss', 'N/A'):.4f}")

        print("\n✅ Training pipeline completed successfully!")

    def resume_training(self, checkpoint_path: str):
        """恢复训练"""
        print(f"🔄 Resuming training from checkpoint: {checkpoint_path}")

        checkpoint = self.checkpoint_manager.load_checkpoint(
            self.model, self.optimizer, self.scheduler, checkpoint_path
        )

        self.current_epoch = checkpoint['epoch'] + 1
        self.best_val_loss = checkpoint['metrics'].get('total_loss', float('inf'))

        print(f"✅ Training resumed from epoch {self.current_epoch}")

    def evaluate_model(self, test_loader=None):
        """评估模型"""
        if test_loader is None:
            _, _, test_loader = self.data_generator.create_dataloaders()

        print("🔬 Evaluating model on test set...")

        self.model.eval()
        test_metrics = self._validate_epoch(test_loader, -1)

        print("📊 Test Results:")
        for key, value in test_metrics.items():
            print(f"  {key}: {value:.4f}")

        return test_metrics
