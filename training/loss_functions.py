#!/usr/bin/env python3
"""
Loss Functions
损失函数 - 神经列生成模型的各种损失函数
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
import numpy as np
from typing import List, Dict, Tuple, Optional

from core import ScheduleColumn, ScheduleEvaluator


class MakespanLoss(nn.Module):
    """Makespan损失函数"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.evaluator = ScheduleEvaluator(config)
    
    def forward(self, columns: List[List[ScheduleColumn]], instances: List[Dict]) -> torch.Tensor:
        """计算makespan损失"""
        batch_losses = []
        
        for batch_columns, instance in zip(columns, instances):
            if not batch_columns:
                batch_losses.append(torch.tensor(1000.0))
                continue
            
            # 评估所有列
            evaluated_columns = self.evaluator.evaluate_columns(batch_columns, instance)
            
            # 计算makespan损失
            makespans = []
            for column in evaluated_columns:
                if column.feasible and column.makespan < float('inf'):
                    makespans.append(column.makespan)
            
            if makespans:
                # 使用最佳makespan作为损失
                best_makespan = min(makespans)
                # 归一化损失
                normalized_loss = best_makespan / 100.0
                batch_losses.append(torch.tensor(normalized_loss))
            else:
                # 如果没有可行解，给予高损失
                batch_losses.append(torch.tensor(1000.0))
        
        return torch.stack(batch_losses).mean()


class FeasibilityLoss(nn.Module):
    """可行性损失函数"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.evaluator = ScheduleEvaluator(config)
    
    def forward(self, columns: List[List[ScheduleColumn]], instances: List[Dict]) -> torch.Tensor:
        """计算可行性损失"""
        batch_losses = []
        
        for batch_columns, instance in zip(columns, instances):
            if not batch_columns:
                batch_losses.append(torch.tensor(1.0))
                continue
            
            # 评估所有列
            evaluated_columns = self.evaluator.evaluate_columns(batch_columns, instance)
            
            # 计算可行性率
            feasible_count = sum(1 for col in evaluated_columns if col.feasible)
            feasibility_rate = feasible_count / len(evaluated_columns)
            
            # 可行性损失 = 1 - 可行性率
            feasibility_loss = 1.0 - feasibility_rate
            batch_losses.append(torch.tensor(feasibility_loss))
        
        return torch.stack(batch_losses).mean()


class DecisionQualityLoss(nn.Module):
    """决策质量损失函数"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
    
    def forward(self, columns: List[List[ScheduleColumn]], instances: List[Dict]) -> torch.Tensor:
        """计算决策质量损失"""
        batch_losses = []
        
        for batch_columns, instance in zip(columns, instances):
            if not batch_columns:
                batch_losses.append(torch.tensor(1.0))
                continue
            
            # 计算平均决策质量
            decision_qualities = []
            for column in batch_columns:
                if column.feasible:
                    decision_qualities.append(column.decision_quality)
            
            if decision_qualities:
                avg_decision_quality = np.mean(decision_qualities)
                # 决策质量损失 = 1 - 平均决策质量
                decision_loss = 1.0 - avg_decision_quality
                batch_losses.append(torch.tensor(decision_loss))
            else:
                batch_losses.append(torch.tensor(1.0))
        
        return torch.stack(batch_losses).mean()


class DiversityLoss(nn.Module):
    """多样性损失函数"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
    
    def forward(self, columns: List[List[ScheduleColumn]], instances: List[Dict]) -> torch.Tensor:
        """计算多样性损失"""
        batch_losses = []
        
        for batch_columns, instance in zip(columns, instances):
            if len(batch_columns) <= 1:
                batch_losses.append(torch.tensor(0.0))
                continue
            
            # 计算调度的多样性
            unique_schedules = set()
            feasible_columns = [col for col in batch_columns if col.feasible]
            
            for column in feasible_columns:
                if column.schedule:
                    schedule_key = tuple(sorted(column.schedule))
                    unique_schedules.add(schedule_key)
            
            if feasible_columns:
                diversity_score = len(unique_schedules) / len(feasible_columns)
                # 多样性损失 = 1 - 多样性评分
                diversity_loss = 1.0 - diversity_score
                batch_losses.append(torch.tensor(diversity_loss))
            else:
                batch_losses.append(torch.tensor(1.0))
        
        return torch.stack(batch_losses).mean()


class RankingLoss(nn.Module):
    """排序损失函数 - 鼓励生成更好的列排在前面"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        self.evaluator = ScheduleEvaluator(config)
    
    def forward(self, columns: List[List[ScheduleColumn]], instances: List[Dict]) -> torch.Tensor:
        """计算排序损失"""
        batch_losses = []
        
        for batch_columns, instance in zip(columns, instances):
            if len(batch_columns) <= 1:
                batch_losses.append(torch.tensor(0.0))
                continue
            
            # 评估所有列
            evaluated_columns = self.evaluator.evaluate_columns(batch_columns, instance)
            
            # 过滤可行列
            feasible_columns = [col for col in evaluated_columns if col.feasible and col.makespan < float('inf')]
            
            if len(feasible_columns) <= 1:
                batch_losses.append(torch.tensor(0.0))
                continue
            
            # 计算排序损失
            ranking_loss = 0.0
            n_pairs = 0
            
            for i in range(len(feasible_columns)):
                for j in range(i + 1, len(feasible_columns)):
                    col_i = feasible_columns[i]
                    col_j = feasible_columns[j]
                    
                    # 如果第i个列应该比第j个列更好（makespan更小）
                    if col_i.makespan < col_j.makespan:
                        # 但是它们的顺序是错误的，增加损失
                        margin = max(0, col_j.makespan - col_i.makespan - 1.0)
                        ranking_loss += margin
                    
                    n_pairs += 1
            
            if n_pairs > 0:
                ranking_loss /= n_pairs
            
            batch_losses.append(torch.tensor(ranking_loss))
        
        return torch.stack(batch_losses).mean()


class CombinedLoss(nn.Module):
    """组合损失函数"""
    
    def __init__(self, config):
        super().__init__()
        self.config = config
        
        # 各个损失函数
        self.makespan_loss = MakespanLoss(config)
        self.feasibility_loss = FeasibilityLoss(config)
        self.decision_quality_loss = DecisionQualityLoss(config)
        self.diversity_loss = DiversityLoss(config)
        self.ranking_loss = RankingLoss(config)
        
        # 损失权重
        self.makespan_weight = config.training.makespan_weight
        self.feasibility_weight = config.training.feasibility_weight
        self.decision_quality_weight = config.training.decision_quality_weight
        self.diversity_weight = config.training.diversity_weight
        self.ranking_weight = getattr(config.training, 'ranking_weight', 0.5)
        
        # 损失历史（用于调试）
        self.loss_history = {
            'makespan': [],
            'feasibility': [],
            'decision_quality': [],
            'diversity': [],
            'ranking': [],
            'total': []
        }
    
    def forward(self, columns: List[List[ScheduleColumn]], instances: List[Dict]) -> Dict[str, torch.Tensor]:
        """计算组合损失"""
        # 计算各个损失分量
        makespan_loss = self.makespan_loss(columns, instances)
        feasibility_loss = self.feasibility_loss(columns, instances)
        decision_quality_loss = self.decision_quality_loss(columns, instances)
        diversity_loss = self.diversity_loss(columns, instances)
        ranking_loss = self.ranking_loss(columns, instances)
        
        # 组合总损失
        total_loss = (
            self.makespan_weight * makespan_loss +
            self.feasibility_weight * feasibility_loss +
            self.decision_quality_weight * decision_quality_loss +
            self.diversity_weight * diversity_loss +
            self.ranking_weight * ranking_loss
        )
        
        # 记录损失历史
        self.loss_history['makespan'].append(makespan_loss.item())
        self.loss_history['feasibility'].append(feasibility_loss.item())
        self.loss_history['decision_quality'].append(decision_quality_loss.item())
        self.loss_history['diversity'].append(diversity_loss.item())
        self.loss_history['ranking'].append(ranking_loss.item())
        self.loss_history['total'].append(total_loss.item())
        
        return {
            'total_loss': total_loss,
            'makespan_loss': makespan_loss,
            'feasibility_loss': feasibility_loss,
            'decision_quality_loss': decision_quality_loss,
            'diversity_loss': diversity_loss,
            'ranking_loss': ranking_loss
        }
    
    def get_loss_weights(self) -> Dict[str, float]:
        """获取损失权重"""
        return {
            'makespan_weight': self.makespan_weight,
            'feasibility_weight': self.feasibility_weight,
            'decision_quality_weight': self.decision_quality_weight,
            'diversity_weight': self.diversity_weight,
            'ranking_weight': self.ranking_weight
        }
    
    def update_loss_weights(self, **kwargs):
        """更新损失权重"""
        for key, value in kwargs.items():
            if hasattr(self, key):
                setattr(self, key, value)
                print(f"Updated {key} to {value}")
    
    def get_recent_loss_stats(self, window_size: int = 100) -> Dict[str, Dict[str, float]]:
        """获取最近的损失统计"""
        stats = {}
        
        for loss_name, history in self.loss_history.items():
            if history:
                recent_losses = history[-window_size:]
                stats[loss_name] = {
                    'mean': np.mean(recent_losses),
                    'std': np.std(recent_losses),
                    'min': np.min(recent_losses),
                    'max': np.max(recent_losses),
                    'latest': recent_losses[-1]
                }
        
        return stats
    
    def reset_loss_history(self):
        """重置损失历史"""
        for key in self.loss_history:
            self.loss_history[key] = []
    
    def adaptive_weight_adjustment(self, epoch: int):
        """自适应权重调整"""
        if epoch > 0 and len(self.loss_history['total']) > 10:
            # 获取最近的损失统计
            recent_stats = self.get_recent_loss_stats(window_size=50)
            
            # 如果可行性损失太高，增加可行性权重
            if 'feasibility' in recent_stats:
                feasibility_mean = recent_stats['feasibility']['mean']
                if feasibility_mean > 0.5:
                    self.feasibility_weight = min(self.feasibility_weight * 1.1, 3.0)
                elif feasibility_mean < 0.1:
                    self.feasibility_weight = max(self.feasibility_weight * 0.9, 0.5)
            
            # 如果makespan损失稳定，增加多样性权重
            if 'makespan' in recent_stats and 'diversity' in recent_stats:
                makespan_std = recent_stats['makespan']['std']
                diversity_mean = recent_stats['diversity']['mean']
                
                if makespan_std < 0.1 and diversity_mean > 0.3:
                    self.diversity_weight = min(self.diversity_weight * 1.05, 1.0)


class ContrastiveLoss(nn.Module):
    """对比损失函数 - 用于区分好坏调度"""
    
    def __init__(self, config, margin: float = 1.0):
        super().__init__()
        self.config = config
        self.margin = margin
        self.evaluator = ScheduleEvaluator(config)
    
    def forward(self, columns: List[List[ScheduleColumn]], instances: List[Dict]) -> torch.Tensor:
        """计算对比损失"""
        batch_losses = []
        
        for batch_columns, instance in zip(columns, instances):
            if len(batch_columns) < 2:
                batch_losses.append(torch.tensor(0.0))
                continue
            
            # 评估所有列
            evaluated_columns = self.evaluator.evaluate_columns(batch_columns, instance)
            
            # 过滤可行列
            feasible_columns = [col for col in evaluated_columns if col.feasible and col.makespan < float('inf')]
            
            if len(feasible_columns) < 2:
                batch_losses.append(torch.tensor(0.0))
                continue
            
            # 找到最好和最差的列
            best_column = min(feasible_columns, key=lambda x: x.makespan)
            worst_column = max(feasible_columns, key=lambda x: x.makespan)
            
            # 计算对比损失
            makespan_diff = worst_column.makespan - best_column.makespan
            quality_diff = best_column.quality_score - worst_column.quality_score
            
            # 对比损失：鼓励好的调度有更高的质量分数
            contrastive_loss = max(0, self.margin - quality_diff)
            batch_losses.append(torch.tensor(contrastive_loss))
        
        return torch.stack(batch_losses).mean()
