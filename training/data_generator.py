#!/usr/bin/env python3
"""
Training Data Generator
训练数据生成器 - 为神经列生成模型生成训练数据
"""

import torch
import numpy as np
import random
import pickle
from typing import List, Dict, Tuple, Optional
from torch.utils.data import Dataset, DataLoader
import os

from core import FJSPParser, FeatureExtractor, ScheduleEvaluator


class FJSPDataset(Dataset):
    """FJSP数据集类"""
    
    def __init__(self, instances: List[Dict], feature_extractor: FeatureExtractor):
        self.instances = instances
        self.feature_extractor = feature_extractor
        self.features_cache = {}
    
    def __len__(self):
        return len(self.instances)
    
    def __getitem__(self, idx):
        instance = self.instances[idx]
        instance_id = instance.get('instance_id', f'instance_{idx}')
        
        # 使用缓存避免重复计算特征
        if instance_id not in self.features_cache:
            features = self.feature_extractor.extract_features(instance)
            self.features_cache[instance_id] = features
        else:
            features = self.features_cache[instance_id]
        
        return {
            'features': features,
            'instance': instance,
            'instance_id': instance_id
        }


class TrainingDataGenerator:
    """训练数据生成器"""
    
    def __init__(self, config):
        self.config = config
        self.parser = FJSPParser()
        self.feature_extractor = FeatureExtractor(config)
        self.evaluator = ScheduleEvaluator(config)
        
        # 数据路径
        self.raw_data_dir = config.data.raw_data_dir
        self.processed_data_dir = config.data.processed_data_dir
        
        # 确保目录存在
        os.makedirs(self.processed_data_dir, exist_ok=True)
    
    def generate_synthetic_instances(self, num_instances: int = 1000) -> List[Dict]:
        """生成合成FJSP实例"""
        print(f"🔄 Generating {num_instances} synthetic FJSP instances...")
        
        instances = []
        
        for i in range(num_instances):
            # 随机生成实例参数
            n_jobs = random.randint(self.config.data.min_jobs, self.config.data.max_jobs)
            n_machines = random.randint(self.config.data.min_machines, self.config.data.max_machines)
            
            # 生成作业长度
            job_lengths = []
            total_operations = 0
            
            for job_id in range(n_jobs):
                # 每个作业的操作数在2-8之间
                n_ops = random.randint(2, min(8, 20 // n_jobs + 2))
                job_lengths.append(n_ops)
                total_operations += n_ops
            
            # 生成处理时间矩阵
            processing_times = []
            
            for op_id in range(total_operations):
                proc_times = [0] * n_machines
                
                # 随机选择可用机器（至少1个，最多全部）
                n_available = random.randint(1, n_machines)
                available_machines = random.sample(range(n_machines), n_available)
                
                for machine_id in available_machines:
                    # 处理时间在1-100之间
                    proc_time = random.randint(1, 100)
                    proc_times[machine_id] = proc_time
                
                processing_times.append(proc_times)
            
            # 创建实例
            instance = {
                'instance_id': f'synthetic_{i:04d}',
                'n_jobs': n_jobs,
                'n_machines': n_machines,
                'n_operations': total_operations,
                'job_length': job_lengths,
                'processing_times': processing_times,
                'format': 'synthetic'
            }
            
            # 验证实例有效性
            if self.parser.validate_instance(instance):
                instances.append(instance)
            else:
                print(f"⚠️  Invalid synthetic instance {i}, skipping...")
        
        print(f"✅ Generated {len(instances)} valid synthetic instances")
        return instances
    
    def load_real_instances(self) -> List[Dict]:
        """加载真实FJSP实例"""
        print("📊 Loading real FJSP instances...")
        
        instances = []
        
        # SD数据集路径
        sd_paths = [
            'data/SD1/10x5',
            'data/SD1/15x10', 
            'data/SD1/20x5',
            'data/SD1/20x10',
            'data/SD2/10x5+mix',
            'data/SD2/15x10+mix',
            'data/SD2/20x5+mix',
            'data/SD2/20x10+mix'
        ]
        
        for sd_path in sd_paths:
            full_path = os.path.join(self.raw_data_dir, sd_path)
            if os.path.exists(full_path):
                try:
                    path_instances = self.parser.parse_directory(full_path, "*.fjs")
                    instances.extend(path_instances)
                    print(f"  ✅ Loaded {len(path_instances)} instances from {sd_path}")
                except Exception as e:
                    print(f"  ⚠️  Failed to load from {sd_path}: {e}")
        
        # 从原始数据目录加载
        fjsp_data_dir = os.path.join(self.raw_data_dir, "fjsp_data")
        if os.path.exists(fjsp_data_dir):
            try:
                fjsp_instances = self.parser.parse_directory(fjsp_data_dir, "*.fjs")
                instances.extend(fjsp_instances)
                print(f"  ✅ Loaded {len(fjsp_instances)} instances from fjsp_data")
            except Exception as e:
                print(f"  ⚠️  Failed to load from fjsp_data: {e}")
        
        print(f"✅ Total loaded: {len(instances)} real instances")
        return instances
    
    def augment_instances(self, instances: List[Dict]) -> List[Dict]:
        """数据增强"""
        print(f"🔄 Augmenting {len(instances)} instances...")
        
        augmented_instances = instances.copy()
        
        for instance in instances:
            for aug_id in range(self.config.data.augmentation_factor - 1):
                # 处理时间噪声增强
                augmented_instance = self._add_processing_time_noise(instance, aug_id)
                augmented_instances.append(augmented_instance)
        
        print(f"✅ Augmented to {len(augmented_instances)} instances")
        return augmented_instances
    
    def _add_processing_time_noise(self, instance: Dict, aug_id: int) -> Dict:
        """为处理时间添加噪声"""
        augmented_instance = instance.copy()
        augmented_instance['instance_id'] = f"{instance['instance_id']}_aug_{aug_id}"
        
        # 复制处理时间矩阵
        original_times = np.array(instance['processing_times'])
        augmented_times = original_times.copy()
        
        # 添加高斯噪声
        noise_level = self.config.data.noise_level
        
        for i in range(augmented_times.shape[0]):
            for j in range(augmented_times.shape[1]):
                if original_times[i, j] > 0:
                    # 添加相对噪声
                    noise = np.random.normal(0, noise_level * original_times[i, j])
                    new_time = max(1, int(original_times[i, j] + noise))
                    augmented_times[i, j] = new_time
        
        augmented_instance['processing_times'] = augmented_times.tolist()
        
        return augmented_instance
    
    def split_dataset(self, instances: List[Dict]) -> Tuple[List[Dict], List[Dict], List[Dict]]:
        """划分数据集"""
        print(f"📊 Splitting {len(instances)} instances into train/val/test...")
        
        # 随机打乱
        random.shuffle(instances)
        
        # 计算划分点
        n_total = len(instances)
        n_train = int(n_total * self.config.data.train_ratio)
        n_val = int(n_total * self.config.data.val_ratio)
        
        # 划分
        train_instances = instances[:n_train]
        val_instances = instances[n_train:n_train + n_val]
        test_instances = instances[n_train + n_val:]
        
        print(f"  ✅ Train: {len(train_instances)} instances")
        print(f"  ✅ Val: {len(val_instances)} instances")
        print(f"  ✅ Test: {len(test_instances)} instances")
        
        return train_instances, val_instances, test_instances
    
    def create_datasets(self) -> Tuple[FJSPDataset, FJSPDataset, FJSPDataset]:
        """创建训练、验证和测试数据集"""
        print("🎯 Creating FJSP datasets...")
        
        # 检查是否已有处理好的数据
        train_path = os.path.join(self.processed_data_dir, "train_dataset.pkl")
        val_path = os.path.join(self.processed_data_dir, "val_dataset.pkl")
        test_path = os.path.join(self.processed_data_dir, "test_dataset.pkl")
        
        if all(os.path.exists(p) for p in [train_path, val_path, test_path]):
            print("📁 Loading existing processed datasets...")
            
            with open(train_path, 'rb') as f:
                train_instances = pickle.load(f)
            with open(val_path, 'rb') as f:
                val_instances = pickle.load(f)
            with open(test_path, 'rb') as f:
                test_instances = pickle.load(f)
            
            print(f"  ✅ Loaded train: {len(train_instances)} instances")
            print(f"  ✅ Loaded val: {len(val_instances)} instances")
            print(f"  ✅ Loaded test: {len(test_instances)} instances")
        
        else:
            print("🔄 Creating new datasets...")
            
            # 加载真实实例
            real_instances = self.load_real_instances()
            
            # 生成合成实例
            synthetic_instances = self.generate_synthetic_instances(500)
            
            # 合并所有实例
            all_instances = real_instances + synthetic_instances
            
            # 数据增强
            if self.config.data.augmentation_factor > 1:
                all_instances = self.augment_instances(all_instances)
            
            # 划分数据集
            train_instances, val_instances, test_instances = self.split_dataset(all_instances)
            
            # 保存处理好的数据
            with open(train_path, 'wb') as f:
                pickle.dump(train_instances, f)
            with open(val_path, 'wb') as f:
                pickle.dump(val_instances, f)
            with open(test_path, 'wb') as f:
                pickle.dump(test_instances, f)
            
            print("💾 Saved processed datasets")
        
        # 创建Dataset对象
        train_dataset = FJSPDataset(train_instances, self.feature_extractor)
        val_dataset = FJSPDataset(val_instances, self.feature_extractor)
        test_dataset = FJSPDataset(test_instances, self.feature_extractor)
        
        return train_dataset, val_dataset, test_dataset
    
    def create_dataloaders(self) -> Tuple[DataLoader, DataLoader, DataLoader]:
        """创建数据加载器"""
        print("🔄 Creating data loaders...")
        
        train_dataset, val_dataset, test_dataset = self.create_datasets()
        
        # 创建DataLoader
        train_loader = DataLoader(
            train_dataset,
            batch_size=self.config.training.batch_size,
            shuffle=True,
            num_workers=self.config.system.num_workers,
            pin_memory=self.config.system.pin_memory,
            collate_fn=self._collate_fn
        )
        
        val_loader = DataLoader(
            val_dataset,
            batch_size=self.config.training.batch_size,
            shuffle=False,
            num_workers=self.config.system.num_workers,
            pin_memory=self.config.system.pin_memory,
            collate_fn=self._collate_fn
        )
        
        test_loader = DataLoader(
            test_dataset,
            batch_size=self.config.training.batch_size,
            shuffle=False,
            num_workers=self.config.system.num_workers,
            pin_memory=self.config.system.pin_memory,
            collate_fn=self._collate_fn
        )
        
        print(f"✅ Created data loaders:")
        print(f"  Train: {len(train_loader)} batches")
        print(f"  Val: {len(val_loader)} batches")
        print(f"  Test: {len(test_loader)} batches")
        
        return train_loader, val_loader, test_loader
    
    def _collate_fn(self, batch):
        """批处理整理函数"""
        # 由于FJSP实例大小不同，这里返回列表而不是张量
        return {
            'features': [item['features'] for item in batch],
            'instances': [item['instance'] for item in batch],
            'instance_ids': [item['instance_id'] for item in batch]
        }
    
    def get_dataset_statistics(self) -> Dict:
        """获取数据集统计信息"""
        train_dataset, val_dataset, test_dataset = self.create_datasets()
        
        all_instances = (train_dataset.instances + 
                        val_dataset.instances + 
                        test_dataset.instances)
        
        return self.parser.get_instance_statistics(all_instances)
